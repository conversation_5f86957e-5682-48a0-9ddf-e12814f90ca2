from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json
from django.db import transaction
from rest_framework.decorators import *
from ..permissions import *
from rest_framework.permissions import AllowAny
from api.integrations.integration_utils import handle_send_notification
from .mystore_model import *
from ..users.user_model import Company
from .mystore_utils import *
from ..util_functions import *
from ..orders.order_utils import *
from .users.user_utils import *
from .orders.order_model import *
from .orders.order_utils import *
from ..category.category_utils import *
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_mystore'])])
@subscription_required
def add_mystore(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    with transaction.atomic():
        company = user.company
        name = json_data.get('name')

        validation_errors = mystore_name_validator(name,company)
        if validation_errors:
            return JsonResponse({'success': False,'message': validation_errors}, status=400)
        
        mystore, created = MyStore.objects.update_or_create(
            company=company,
            defaults={'name': name,'is_archived': False,})
        if not created:
            return JsonResponse({'success': True, 'message': 'Store updated and activated successfully'}, status=200)
        
    return JsonResponse({'success': True, 'message': 'Mystore Created Successfully'}, status=201)
    
@csrf_exempt
def update_mystore(request):
    if request.method != 'PUT':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    values = json_data
    values['updated_by'] = user.name
    values['company'] = user.company

    try:
        # validation_errors = mystore_name_validator(values['name'],values['company'])
        # if validation_errors:
        #     return JsonResponse({'success': False,'message': validation_errors}, status=400)

        store = update_mystore_util(values)
        if not store:
            return JsonResponse({'success': False, 'error': 'Mystore not found'}, status=400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)
    
    serializer = MyStoreSerializer(store)
    response_data = {
        'success': True,
        'mystore': serializer.data
    }
    return JsonResponse(response_data, status=200)

@csrf_exempt
def get_mystore(request):
    if request.method != 'GET':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    company = user.company

    try:
        store = MyStore.objects.get(company=company)
    except MyStore.DoesNotExist:
        return JsonResponse({'success': False, 'mystore': None}, status=200)
    
    serializer = MyStoreSerializer(store)
    response_data = {
        'success': True,
        'mystore': serializer.data
    }
    return JsonResponse(response_data, status=200)

@csrf_exempt
def get_mystore_public(request):
    if request.method != 'GET':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    try:
        store = get_origin(request)
    except MyStore.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'My store not found'}, status=400)
    
    serializer = MyStoreSerializer(store)
    response_data = {
        'success': True,
        'mystore': serializer.data
    }
    return JsonResponse(response_data, status=200)

@csrf_exempt
def get_banners(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    fields_to_include = [
    'id',
    'title',
    'banner_image_url',
    'link_url',
    'link_label',
    'link_color',
    'active'
    ]

    
    try:
        queryset = BannerDetail.objects.filter(mystore=mystore).order_by('-id')
    except:
        return JsonResponse({'success': False, 'error': 'My store not found'}, status=400)
    
    # return all the banner detail DESC
    related_fields = {}
    banner_detail = search_filter_group(queryset,json_data,fields_to_include, related_fields)
    banner_detail = object_to_json(banner_detail,[],{})

    response_data = {
        'success': True,
        'banner_detail':banner_detail,
        'data_count':len('banner_detail')
    }
    return JsonResponse(response_data, status=200)

@csrf_exempt
def add_banner_detail(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
        
    required_fields = [
        'title',
        'banner_image_url',
        'link_url',
        'link_label',
        'link_color',
        'active'
    ]

    try:
        company = user.company
        
        mystore = get_mystore_util(company)
        if not mystore:
            return JsonResponse({'success': False, 'error': 'Mystore not found'}, status=404)

        values = json_data
        values['mystore'] = mystore
        values['created_by'] = user.name
        values['updated_by'] = user.name
        
        validation_errors = validate_missing_field(values, required_fields)
        if validation_errors:
            return JsonResponse({'success': False,'error': validation_errors}, status=400)
        
        banner_detail = create_banner_detail(values)
        if not banner_detail:
            return JsonResponse({'success': False, 'error': 'Failed to create Banner detail'}, status=500)
    except BannerDetail.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Banner detail not found'}, status=404)
    
    return JsonResponse({'success': True, 'message': 'Banner detail created successfully'}, status=201)

@csrf_exempt
def delete_mystore(request):
    if request.method!= 'DELETE':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    try:
        company = user.company
        mystore = MyStore.objects.get(company=company)
        mystore.delete()
    except MyStore.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'My Store not found'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'error': e.message}, status=500)
    return JsonResponse({'success': True, 'message': 'My Store deleted successfully'}, status=200)

@csrf_exempt
def get_banner_auth(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)


    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    
    fields_to_include = [
    'id',
    'title',
    'banner_image_url',
    'link_url',
    'link_label',
    'link_color',
    'active',
    'mystore',
    'created_at',
    'updated_at',
    'created_by',
    'updated_by'
    ]
    
    try:
        company = user.company
        mystore = MyStore.objects.get(company=company)

        queryset = BannerDetail.objects.filter(mystore = mystore).order_by('-id')
        related_fields = {
            'mystore': MyStoreSerializer
        }

        banner_detail = search_filter_group(queryset,json_data,fields_to_include, related_fields)
        banner_detail = object_to_json(banner_detail,[],{})

        response_data = {
            'success': True,
            'banner_detail':banner_detail,
            'data_count':len('banner_detail')
        }
        return JsonResponse(response_data, status=200)
    except Exception as e:
        return JsonResponse({'success': False, 'error': 'My store not found', 'Exception': e.args}, status=400)

@csrf_exempt
def update_banner_detail(request):
    if request.method !="PUT":
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    required_fields = [
        'id',
        'title',
        'banner_image_url',
        'link_url',
        'link_label',
        'link_color',
        'active'
        ]
    try:
        values = json_data
        company = user.company
        values['company'] = company
        values['mystore'] = get_mystore_util(company)
        values['created_by'] = user.name
        values['updated_by'] = user.name

        validation_errors = validate_missing_field(values, required_fields)
        if validation_errors:
            return JsonResponse({'success': False,'message': validation_errors}, status=400)
        banner_detail = update_banner_detail_util(values)
        if not banner_detail:
            return JsonResponse({'success': False, 'message': 'Banner detail update failed'}, status=500)
    except BannerDetail.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Banner detail not found'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    return JsonResponse({'success': True, 'message': 'Banner detail updated successfully'}, status=200)

def delete_mystore_banner(request):
    if request.method!= 'DELETE':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    required_fields = [
        'id'
        ]
    
    values = json_data
    validation_errors = validate_missing_field(values, required_fields)
    if validation_errors:
        return JsonResponse({'success': False,'message': validation_errors}, status=400)
    try:
        company = user.company
        mystore = MyStore.objects.filter(company=company).first()
        mystore_banner = BannerDetail.objects.get(id=values['id'], mystore = mystore)
        mystore_banner.delete()
    except BannerDetail.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Banner not found'}, status=404)
    except:
        return JsonResponse({'success': False, 'error': 'Store not found'}, status=404)

    return JsonResponse({'success': True, 'message': 'Banner deleted successfully'}, status=200)

@csrf_exempt
def add_mystore_order(request):

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    company = get_company(mystore.company.company_id)
    if not company:
        return JsonResponse({'success': False, 'message': 'Invalid company'}, status=400)

    ecommerce = json_data.get('channel')
    
    try:
        subscription = get_current_subscription(company)
    except Exception as e:
        return JsonResponse({'success': False, 'error': 'Store can not receiving order right now'}, status=404)

    # requires Fields from frontend
    required_fields = [
        'customer_mobile', 'customer_name', 'address',
        'area', 'sub_area', 'country', 'product_info', 'package_cost'
    ]

    order_sequences = []
    order_references = []
    validation_errors = []

    validation_errors, foreign_key_objects = validate_order(json_data, company, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)

    try:
        print(1)
        order_lines = json_data.pop('order_lines', [])
        print(2)
        order_values = build_order_values(json_data, foreign_key_objects,company, company.company_name)
        print(3)
        order = create_order(order_values,order_lines)
        if not order:
            return JsonResponse({'success': False, 'error': 'FAILED_TO_CREATE_ORDER'}, status=400)    
        order_references.append(order.order_reference)
        order_sequences.append(order.order_sequence)
    except Exception as e:
        return JsonResponse({'success': False, 'error':str(e) }, status=500)
    handle_send_notification(order_sequences,'my_store')
    return JsonResponse({'success': True, 'message': 'Orders added successfully', 'order_sequences': order_sequences, 'order_references': order_references}, status=201)

@csrf_exempt
def add_mystore_order_auth(request):

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    company = get_company(mystore.company.company_id)
    if not company:
        return JsonResponse({'success': False, 'message': 'Invalid company'}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    # requires Fields from frontend
    required_fields = [
        'customer_mobile', 'customer_name', 'address',
        'area', 'sub_area', 'country', 'product_info', 'package_cost'
    ]

    order_sequences = []
    order_references = []
    validation_errors = []

    validation_errors, foreign_key_objects = validate_order(json_data, company, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)

    try:
        order_lines = json_data.pop('order_lines', [])
        order_values = build_order_values(json_data, foreign_key_objects,company, company.company_name)
        order = create_order(order_values,order_lines)
        store_order = create_store_order(user,mystore,order)
        if not order:
            return JsonResponse({'success': False, 'error': 'FAILED_TO_CREATE_ORDER'}, status=400)

        if not store_order:
            return JsonResponse({'success': False, 'error': 'FAILED_TO_CREATE_STORE_ORDER'}, status=400)
    
        order_references.append(order.order_reference)
        order_sequences.append(order.order_sequence)
    except Exception as e:
        return JsonResponse({'success': False, 'error':str(e) }, status=500)

    return JsonResponse({'success': True, 'message': 'Orders added successfully', 'order_sequences': order_sequences, 'order_references': order_references}, status=201)

@csrf_exempt
def get_store_orders(request):

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    try:
        company_id = mystore.company.company_id
    except Company.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Company could not be found'}, status=400)


    fields_to_include = ['id', 'order_sequence', 'customer_mobile', 'country_code',
        'customer_name','address', 'area', 'sub_area',
        'country', 'delivery_fee', 'extra_delivery_fee', 'product_info', 'package_cost', 'total_cod', 'status', 'note',
        'order_lines'
    ]

    model_map = {
        'country': Country,
        'sub_area': SubArea,
        'area': Area,
        'delivery_company': DeliveryCompany,
        'status': Status,
        'product': Product
    }

    related_fields = {
        'order_lines': OrderLineSerializer
    }

    store_order = Order.objects.filter(store_orders__user=user)

    objects_to_handle = ['sub_area', 'area', 'country', 'delivery_company', 'status', 'order_lines']

    orders = search_filter_group(store_order, json_data, fields_to_include, related_fields)
    orders = object_to_json(orders, objects_to_handle, model_map)

    response_data = {
        'success': True,
        'orders': orders,
    }
    return JsonResponse(response_data, status=200)

@csrf_exempt
def add_store_section(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    company = user.company
    
    required_fields = [
        'section_description',
        'section_code',
        ]
    
    store = MyStore.objects.get(company = company)
    if not store:
        return JsonResponse({'success': False, 'message': 'Invalid store'}, status=400)

    values = json_data
    values['store'] = store
    values['created_by'] = user.name
    values['updated_by'] = user.name

    validation_errors = validate_missing_field(values, required_fields)
    if validation_errors:
        return JsonResponse({'success': False,'error': validation_errors}, status=400)
    
    try:
        section = create_store_section(values)

    except Exception as e:
        return JsonResponse({'success': False, 'error':str(e) }, status=500)
    
    return JsonResponse({'success': True, 'message': 'Store Section Created Successfully'}, status=201)

@csrf_exempt
def get_store_section_auth(request):

    if request.method != 'GET':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    company = user.company
    
    fields_to_include = [
        "id",
        "section_description",
        "section_code",
        "categories",
        "tags",
        "position",
        "active"
    ]

    related_fields = {
        'categories': CategorySerializer,
        'tags': TagSerializer,
    }
    try:
        store_section = StoreSection.objects.filter(store__company = company)

        store_section = search_filter_group(store_section, json_data, fields_to_include, related_fields)
        store_section = object_to_json(store_section, [], {})

    except Exception as e :
        return JsonResponse({'success': False, 'error': str(e)}, status=400)
    
    response_data = {
        'success': True,
        'store_section': store_section,
    }
    return JsonResponse(response_data, status=200)

@csrf_exempt
def get_store_section_public(request):
    
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    try:
        company_id = mystore.company.company_id
    except Company.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Company could not be found'}, status=400)
    
    company = mystore.company
    
    fields_to_include = [
        "section_description",
        "section_code",
        "categories",
        "tags",
        "position",
        "active"
    ]

    related_fields = {
        'categories': CategorySerializer,
        'tags': TagSerializer,
    }
    try:
        store_section = StoreSection.objects.filter(store__company = company)

        store_section = search_filter_group(store_section, json_data, fields_to_include, related_fields)
        store_section = object_to_json(store_section, [], {})

    except Exception as e :
        return JsonResponse({'success': False, 'error': str(e)}, status=400)
    
    response_data = {
        'success': True,
        'store_section': store_section,
    }
    return JsonResponse(response_data, status=200)

@csrf_exempt
def update_store_section(request):
    
    if request.method !="PUT":
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    required_fields = [
        'id',
        'section_description',
        'categories',
        'tags',
        'position',
        'active'
        ]
    try:
        values = json_data
        company = user.company
        values['store'] = get_mystore_util(company)
        values['created_by'] = user.name
        values['updated_by'] = user.name

        validation_errors = validate_missing_field(values, required_fields)
        if validation_errors:
            return JsonResponse({'success': False,'message': validation_errors}, status=400)

        store_section = update_store_section_util(values);

        if not store_section:
            return JsonResponse({'success': False, 'message': 'Store Section update failed'}, status=500)

    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    return JsonResponse({'success': True, 'message': 'Store Section updated successfully'}, status=200)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_store_delivery_fee(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        try:
            mystore = validate_mystore(request)
        except Exception as e:
            raise get_error_response('MYSTORE_1101',{})

        area_id = json_data.get('area')
        if not area_id:
            raise get_error_response('GENERAL_003', {'fields':'area'})
        
        try:
            area = Area.objects.get(id=area_id)
        except Area.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'Area', 'field': 'id', 'value': area_id})

        delivery_fee = get_delivery_fee_util(mystore.company, None, area)
        return JsonResponse({'success': True, 'delivery_fee': delivery_fee}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

