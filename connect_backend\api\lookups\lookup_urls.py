from django.urls import path
from . import lookup_apis

urlpatterns = [
    path('get_countries', lookup_apis.get_countries, name='country_get'),
    path('get_areas', lookup_apis.get_areas, name='area_get'),
    path('get_sub_areas', lookup_apis.get_sub_areas, name='area_sub_get'),
    path('get_country_by_code', lookup_apis.get_country_by_code, name='get_country_by_code'),
    path('get_area_by_code', lookup_apis.get_area_by_code, name='get_area_by_code'),
    path('get_sub_area_by_code', lookup_apis.get_sub_area_by_code, name='get_sub_area_by_code'),
    path('get_area_by_country', lookup_apis.get_area_by_country, name='get_area_by_country'),
    path('get_sub_area_by_area', lookup_apis.get_sub_area_by_area, name='get_sub_area_by_area'),
    path('get_area_sub_area', lookup_apis.get_area_sub_area, name='get_area_sub_area'),
    path('get_sub_area_by_country', lookup_apis.get_sub_area_by_country, name='get_sub_area_by_country'),
    path('get_areas_for_company', lookup_apis.get_areas_for_company, name='get_areas_for_company'),
]