from django.core.management.base import BaseCommand
from django.db.models import Count
from api.orders.order_model import Order

class Command(BaseCommand):

    def handle(self, *args, **kwargs):
        # Find duplicate order_references per company
        duplicates = (
            Order.objects.values('company', 'order_reference')
            .annotate(count=Count('id'))
            .filter(count__gt=1, order_reference__isnull=False)
        )

        total_removed = 0

        for duplicate in duplicates:
            company = duplicate['company']
            order_reference = duplicate['order_reference']

            # Get all orders with this duplicate reference, ordered by id
            duplicate_orders = Order.objects.filter(
                company=company,
                order_reference=order_reference
            ).order_by('id')  # Sort by ID to pick the first one

            # Get the first order (retain the order_reference) and update the others
            first_order = duplicate_orders.first()
            if first_order:
                # Update all orders except the first one
                duplicate_orders.exclude(id=first_order.id).update(order_reference=None)
                total_removed += duplicate_orders.count() - 1  # Removed all but the first one

        self.stdout.write(self.style.SUCCESS(f"Removed duplicate references from {total_removed} orders"))
