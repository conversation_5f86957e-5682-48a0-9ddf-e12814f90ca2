from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from .category_model import *
from ..util_functions import *
from .category_utils import *
from django.db import IntegrityError
from ..mystore.mystore_utils import *
from django.utils.translation import gettext as _
from rest_framework.decorators import *
from ..permissions import *
from rest_framework.permissions import AllowAny
from ..error.error_model import *

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_category'])])
@subscription_required
def add_category(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    company = user.company

    required_fields = ['name']

    values = json_data
    values['company'] = company
    values['created_by'] = user.name
    values['updated_by'] = user.name

    validation_errors = validate_missing_field(values, required_fields)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})

    category = create_category(values)

    serializer = CategorySerializer(category)
    return JsonResponse({'success': True, 'message': 'Categroy created successfully','category': serializer.data}, status=201)

@csrf_exempt
@api_view(["POST"])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_category'])])
def get_categories_auth(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('Category', json_data, user)
    return JsonResponse(response_data, status=200)

@csrf_exempt
def update_category(request):
    if request.method != 'PUT':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    company = user.company

    required_fields = ['name']

    # handling create category
    try:
        values = json_data
        values['company'] = company
        values['created_by'] = user.name
        values['updated_by'] = user.name

        validation_errors = validate_missing_field(values, required_fields)
        if validation_errors:
            return JsonResponse({'success': False,'message': validation_errors}, status=400)

        category = update_category_util(values)
        if not category:
            return JsonResponse({'success': False, 'message': 'Category update failed'}, status=500)
    except Category.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Category not found'}, status=404)
    
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

    serializer = CategorySerializer(category)
    return JsonResponse({'success': True, 'message': 'Categroy updated successfully', 'category': serializer.data}, status=201)

@csrf_exempt
def get_categories(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    try:
        mystore = validate_mystore(request)
    except Exception as e:
            raise get_error_response('MYSTORE_1101',{})

    try:
        company_id = mystore.company.company_id
    except Company.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Company could not be found'}, status=400)

    fields_to_include = [
        'id',
        'category_sequence', 
        'name',
        'active',
        'category_image',
        'category_image_url',
        'publish',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by'
    ]

    queryset = Category.objects.filter(company__company_id=company_id).order_by('-category_sequence')

    related_fields = {}
    
    category = search_filter_group(queryset, json_data, fields_to_include, related_fields)
    category = object_to_json(category, [], {})

    response_data = {
        'success': True,
        'category': category,
        'data_count': len(category)
    }
    
    return JsonResponse(response_data, status=200)

@csrf_exempt
@api_view(['DELETE'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['delete_category'])])
def delete_category(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    user = get_user_from_token(request)
    if not user:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    try:
        values = json_data
        company = user.company
        category = Category.objects.get(id=values.get('id'), company=company)
        handle_delete_category(category, company)
    except Category.DoesNotExist:
        return JsonResponse({'success': False, 'error': _('Category not found')}, status=404)
    return JsonResponse({'success': True, 'message': 'Category deleted successfully'}, status=200)
