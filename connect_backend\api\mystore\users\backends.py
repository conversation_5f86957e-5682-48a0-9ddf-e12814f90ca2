from django.contrib.auth.backends import BaseBackend
from .user_model import StoreUser

class StoreUserBackend(BaseBackend):

    def authenticate(self,request, mobile_number, password, **kwargs):
        try:
            user = StoreUser.objects.get(mobile_number=mobile_number ,**kwargs)
            if user.check_password(password):
                return user
        except StoreUser.DoesNotExist:
            return None

    def get_user(self, user_id):
        try:
            return StoreUser.objects.get(pk=user_id)
        except StoreUser.DoesNotExist:
            return None