from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from ..util_functions import get_user_from_token
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q
from .developer_connection_model import *
from ..serializers import *
from rest_framework.decorators import *
from ..permissions import *
from ..error.error_model import *
from ..util_functions import *
from django.utils.translation import gettext as _
from .developer_connection_utils import *
from ..integrations.integration_utils import encode_token
from rest_framework.permissions import AllowAny

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_developerconnection'])])
@csrf_exempt
def get_developer_connections(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('DeveloperConnection', json_data, user)

    return JsonResponse(response_data, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_developerconnection'])])
def add_developer_connection(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    required_fields = ['name']

    validation_errors = validate_developer_connection(json_data, required_fields)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})

    developer_connection_values = build_developer_connection_values(json_data, user.company, user.name)
    developer_connection = create_developer_connection(developer_connection_values)

    serializer = DeveloperConnectionSerializer(developer_connection)
    response = {
        'success': True,
        'developer_connection': serializer.data
    }
    return JsonResponse(response, status=201)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_developerconnection'])])
def generate_developer_connection_token(request):

    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid request data'}, status=400)

    # Extract developer_integration_id from the JSON data
    developer_integration_id = json_data.get('id')
    if not developer_integration_id:
        return JsonResponse({'success': False, 'error': 'Developer integration ID is required'}, status=400)

    # Authenticate the user
    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    # Generate the developer connection token
    company = user.company
    try:
        developer_integration = DeveloperConnection.objects.get(id=developer_integration_id)
    except Package.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Package not found'}, status=404)
    try:
        developer_connection_token = create_developer_connection_token(
            company=company,
            developer_connection=developer_integration,
            username=user.name
        )
        token = encode_developer_token(developer_connection_token, developer_integration_id)
        return JsonResponse({'token': token}, status=200)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_developerconnection'])])
@csrf_exempt
def get_developer_connection_webhooks(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('DeveloperConnectionWebhook', json_data, user)

    return JsonResponse(response_data, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_developerconnection'])])
@csrf_exempt
def set_webhook(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    id = json_data.get('id', None)
    developer_connection = DeveloperConnection.objects.get(id=json_data.get('developer_connection'))
    if id:
        try:
            webhook = DeveloperConnectionWebhook.objects.get(id=id, developer_connection__company=user.company)
            webhook.url = json_data.get('url', webhook.url)
            webhook.is_active = json_data.get('is_active', webhook.is_active)
            webhook.apply_to_all_orders = json_data.get('apply_to_all_orders', webhook.apply_to_all_orders)
            webhook.headers = json_data.get('headers', webhook.headers)
            webhook.save()
        except DeveloperConnectionWebhook.DoesNotExist:
            raise get_error_response('GENERAL_002', {'id': id})
    else:
        webhook = DeveloperConnectionWebhook.objects.create(
            developer_connection=developer_connection,
            url=json_data.get('url'),
            is_active=json_data.get('is_active', True),
            apply_to_all_orders=json_data.get('apply_to_all_orders', False),
            headers=json_data.get('headers', {})
        )

    serializer = DeveloperConnectionWebhookSerializer(webhook)
    response = {
        'success': True,
        'webhook': serializer.data
    }
    return JsonResponse(response, status=201)

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def test_webhook(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    print(f'Request Body: {json_data}')
    print(f'Request Headers: {request.headers}')
    
    return JsonResponse({'success': True}, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_webhooklog'])])
@csrf_exempt
def get_webhook_logs(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('WebhookLog', json_data, user)

    return JsonResponse(response_data, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_webhooklog'])])
@csrf_exempt
def get_webhook_log_summation(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    developer_connection = json_data.get('developer_connection', None)
    developer_connection = DeveloperConnection.objects.get(id=developer_connection) if developer_connection else None
    
    response_data = {
        'total_logs': 0,
        'total_successful': 0,
        'total_failed': 0,
        'success_percentage': '0%',
    }
    if developer_connection:
        webhook = DeveloperConnectionWebhook.objects.filter(developer_connection=developer_connection).first()
        webhook_logs = WebhookLog.objects.filter(webhook=webhook)
        response_data['total_logs'] = webhook_logs.count()
        response_data['total_successful'] = webhook_logs.filter(status_code__in=[200, 201]).count()
        response_data['total_failed'] = webhook_logs.exclude(status_code__in=[200, 201]).count()
        if response_data['total_logs'] > 0:
            response_data['success_percentage'] = f"{(response_data['total_successful'] / response_data['total_logs']) * 100:.2f}%"

    return JsonResponse(response_data, status=200)