<!DOCTYPE html>
{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_10_15.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}

    {% for doc in docs %}
    {% set products = doc.order_lines %}
    {% set chunk_size = 4 %}
    {% set product_count = products|length %}
    {% set chunks = (product_count / chunk_size)|round(0, 'ceil')|int or 1 %}

    {% for i in range(chunks) %}
    <div class="waybill-container page-break">
        <div class="header">
            <div class="header-info">
                <div class="info-item">
                    {{doc.company_name}}
                </div>
                <div class="info-item">
                    {{meta.labels.DATE}}:
                    <span class="fw-bold">
                        {{meta.formatted_now}}
                    </span>
                </div>
            </div>
            <div class="business-logo">
                <img src="{{doc.company_logo}}" class="business-logo">
                {% if doc.company_registry %}
                <div class="text-center fw-bold">{{doc.company_registry}}</div>
                {% endif %}
            </div>
        </div>
        <div class="">
            <div class="cards-container">
                <div class="card recipient-card">
                    <div class="card-header">
                        {{meta.labels.RECIPIENT}}
                    </div>
                    <div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.NAME}}: {{doc.order.customer_name}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.MOBILE_NUMBER}}: {{doc.order.customer_mobile}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.SECOND_MOBILE_NUMBER}}: {{doc.order.customer_second_mobile}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.ADDRESS}}: {{doc.order.area.name}}, 
                                {{doc.order.sub_area.name}},
                                {{doc.order.address}}
                            </span>
                        </div>
                        <div class="info-block">
                            <span class=" ">
                                {{meta.labels.CREATOR}}: {{doc.order.created_by}}
                            </span>
                        </div>
                    </div>
                </div>
            </div><div class="cards-container">
                <div class="card">
                    <div class="card-header-container">
                        <div class="card-header">
                            {{meta.labels.DELIVERY_DETAILS}}
                        </div>
                    </div>
                    <div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.DELIVERY_COMPANY_NAME}}
                            </span>
                            <span class="">
                                {% if doc.order.connection and doc.order.connection.delivery_company %}
                                {{doc.order.connection.delivery_company.name}}
                                {% else %}
                                
                                {% endif %}
                            </span>
                        </div>
                        <div class="info-block">
                            <span>
                                {{meta.labels.TOTAL_AMOUNT}}
                            </span>
                            <span class="fw-bold">
                                {{doc.order.total_cod}}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="overflow-hidden">
                        <div class="notes-content">{{meta.labels.NOTES}}: {{ doc.order.note if doc.order.note is not none else "" }}</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    {{meta.labels.PRODUCTS}}
                </div>

                <div class="products-container">
                    {% if doc.order_lines and doc.order_lines|length > 0 %}
                    <table class="o-table">
                        <thead>
                            <tr>
                                <th>
                                    #
                                </th>
                                <th>
                                    {{meta.labels.PRODUCT_SEQUENCE if doc.print_product_sequence else meta.labels.PRODUCT_NAME}}
                                </th>
                                <th>
                                    {{meta.labels.QUANTITY}}
                                </th>
                                <th>
                                    {{meta.labels.PRICEـUNIT}}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="fs-sm">
                            {% for item in products[i*chunk_size : (i+1)*chunk_size] %}
                            <tr>
                                <td class="border-b-grey">
                                    {{loop.index}}
                                </td>
                                <td class="cell-truncate border-b-grey fw-bold">
                                    {{item.product.reference_sequence if doc.print_product_sequence else item.product_variant.variant.name}}
                                </td>
                                <td class="border-b-grey fw-bold">
                                    X{{item.quantity}}
                                </td>
                                <td class="border-b-grey">
                                    {{item.price}}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    {{doc.order.product_info.replace('\n', '<br>')}}
                    {% endif %}
                </div>
            </div>
            <div class="position-relative text-center">
                <img class="barcode-image m-auto" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}"
                    alt="">
                <div class="page-number-container">
                    {% if chunks > 1 %}
                        <span class="fs-sm">(Page {{i+1}} of {{chunks}})</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    {% endfor %}
</body>

</html>