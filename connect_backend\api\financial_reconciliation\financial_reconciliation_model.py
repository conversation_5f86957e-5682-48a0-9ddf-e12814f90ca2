from django.db import models

from  ..users.user_model import Company

from ..orders.order_model import Order
from ..models import SuperModel

class FinancialReconciliation(SuperModel):
    sequence = models.CharField(max_length=200)
    percentage = models.FloatField(max_length=15)
    orders = models.ManyToManyField(Order)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    
    def save(self, *args, **kwargs):
        if not self.sequence:
            last_sequence = FinancialReconciliation.objects.aggregate(max_seq=models.Max('sequence'))['max_seq']
            if last_sequence:
                next_sequence = str(int(last_sequence) + 1).zfill(8)
            else:
                next_sequence = "00000001"
            self.sequence = next_sequence

        super().save(*args, **kwargs)