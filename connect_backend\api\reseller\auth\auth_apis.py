from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from ...util_functions import *
from ...auth.auth_utils import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ...permissions import *
from ...error.error_model import *
from ..models import *
from django.utils.translation import gettext as _
from .auth_utils import *
from rest_framework.parsers import MultiPartParser, FormParser

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_token(request):
    try:
        json_body = parse_request_body(request)
        if not json_body:
            raise get_error_response('GENERAL_001', {})
    
        mobile_number = json_body.get('mobile_number')
        password = json_body.get('password')

        if not mobile_number or not password:
            raise get_error_response('GENERAL_003', {'fields': 'mobile_number, password'})

        try:
            reseller = Reseller.objects.get(mobile_number=mobile_number)
            if not reseller.check_password(password):
                raise AuthenticationFailed('Invalid credentials')
        except Reseller.DoesNotExist:
            raise AuthenticationFailed('User not found')

        if not reseller.is_active:
            raise get_error_response('AUTH_413', {})

        token = generate_JWT(reseller)

        serializer = ResellerSerializer(reseller)

        return JsonResponse({'success': True, 'token': token, 'reseller': serializer.data}, status=200)

    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def extend_token_validity(request):
    try:
        reseller = request.user
        token = generate_JWT(reseller)

        return JsonResponse({'sucess': True,'token': token}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
@parser_classes([MultiPartParser, FormParser])
def create_user(request):
    try:
        json_body = parse_request_body(request)
        if not json_body:
            raise get_error_response('GENERAL_001', {})
        
        values = json_body
        required_fields = ['name', 'country', 'area', 'address', 'national_id_url']
        if values.get('is_delivery_company'):
            required_fields = ['name', 'country', 'area', 'address', 'company_logo_url']

        if Reseller.objects.filter(mobile_number=values.get('mobile_number')).exists():
            raise get_error_response('AUTH_404', {'mobile_number': values.get('mobile_number')})

        validation_errors, foreign_key_objects = validate_reseller(values, required_fields)

        if validation_errors:
            raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
        
        branches = values.pop('branches', [])
        pricelist_items = values.pop('pricelist_items', [])
        
        values = {**values, **foreign_key_objects, 'is_active': True, 'waiting_confirmation': True, 'is_approved': False}
        reseller = create_reseller_util(values, branches, pricelist_items)
        serializer = ResellerSerializer(reseller)
        return JsonResponse({'success': True,'reseller': serializer.data}, status=201)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def reset_password_otp(request):
    try:
        json_body = parse_request_body(request)
        if not json_body:
            raise get_error_response('GENERAL_001', {})
        
        mobile_number = json_body.get('mobile_number')
        if not validate_mobile_number(mobile_number):
            raise get_error_response('AUTH_403', {'mobile_number': mobile_number})

        try:
            reseller = Reseller.objects.get(mobile_number=mobile_number)
        except Reseller.DoesNotExist as e:
            raise get_error_response('AUTH_411', {})
        
        if not reseller.is_active:
            raise get_error_response('AUTH_415', {})

        otp_code = generate_otp_code()
        otp_values = {
            'mobile_number': mobile_number,
            'code': otp_code,
            'created_at': timezone.now(),
            'expire_date': timezone.now() + timedelta(minutes=3),
            'error_trials': 0
        }
        try:
            otp = get_or_create_otp(mobile_number, otp_values)
            update_otp_trials(otp)
            response = send_otp_message(reseller.country_code, mobile_number, otp_code)
        except Exception as e:
            raise get_error_response('AUTH_405', {'error': str(e)})

        if response.status_code == 200:
            return JsonResponse({'success': True, 'message': _('OTP sent successfully')})
        else:
            raise get_error_response('AUTH_405', {'error': str(e)})
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def reset_password(request):
    try:
        json_body = parse_request_body(request)
        if not json_body:
            raise get_error_response('GENERAL_001', {})

        mobile_number = json_body.get('mobile_number')
        password = json_body.get('password')
        token = json_body.get('token')

        if not mobile_number or not password:
            raise get_error_response('GENERAL_003', {'fields': 'mobile_number, password'})

        if not Reseller.objects.filter(mobile_number=mobile_number).exists():
            raise get_error_response('AUTH_414', {})
        
        validate_token(token)
        reseller = Reseller.objects.get(mobile_number=mobile_number)
        reseller.set_password(password)
        reseller.save()
    
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_pricelist_template(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('ResellerPricelistTemplate', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)