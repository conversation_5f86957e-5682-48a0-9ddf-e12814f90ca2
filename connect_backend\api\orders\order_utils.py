import json
from django.http import JsonResponse
from django.core.exceptions import ValidationError, ObjectDoesNotExist
from django.db import transaction
from decimal import Decimal
from ..lookups.lookup_model import *
from .order_model import *
from ..pricelists.pricelist_model import *
from ..users.user_model import *
from ..connection.connection_model import *
from ..util_functions import *
from ..logs.logs_model import *
from ..client.client_model import *
from ..billing.billing_model import *
from ..billing.billing_utils import *
from cryptography.fernet import Fernet
import base64
from django.core.signing import Signer, BadSignature
from django.utils.translation import gettext as _

def create_order(values, order_lines):
    with transaction.atomic():
        order = Order.objects.create(**values)
        log = create_log('Order', values['company'], 'create', values['created_by'], order.id)
        if not Client.objects.filter(mobile_number=values['customer_mobile']).exists():
            client_values = get_client_values(values)
            Client.objects.get_or_create(**client_values)
        fields = get_tracked_fields(values['company'], 'Order')
        log_tracked_fields(log, values, fields)
        delete_log_if_no_changes(log)
        create_order_lines(order, order_lines, values['created_by'])
        order.save()
        handle_stock(order)
    return order

def get_client_values(json_data):
    values = {}
    values['name'] = json_data['customer_name']
    values['mobile_number'] = json_data['customer_mobile']
    if len(values['mobile_number']) > 11:
        values['country_code'] = '+'.join(json_data['customer_mobile'][:3])
    else:
        values['country_code'] = json_data['country_code']
    values['address'] = json_data['address']
    values['area'] = json_data['area']
    values['sub_area'] = json_data['sub_area']
    values['country'] = json_data['country']

    return values

def validate_order(order, company, required_fields):
    validation_errors = []
    foreign_key_objects = {}

    order_count = get_remaining_orders_count(company)
    if order_count <= 0:
        validation_errors.append('EXCEEDED_MAXIMUM_NUMBER_OF_ORDERS_ALLOWED')
        raise get_error_response('BILLING_801', {})

    for field in required_fields:
        value = order.get(field, None)
        if value == None or value == '':
            validation_errors.append(_('Missing field: ') + field)

    for field in ['note', 'product_info']:
        if field in order and order[field]:
            if len(str(order[field])) > NOTE_INFO_MAX_LENGHT:
                validation_errors.append(field + _(' is too long'))

    if 'total_cod' in order and order['total_cod'] != None:
        try:
            total_cod = float(order.get('total_cod'))
        except ValueError:
            validation_errors.append(_('Invalid type for total_cod: ') + order["total_cod"])

    if 'country_code' in order and len(order['country_code']) > 6 and '|' in order['country_code']:
        order['country_code'] = order['country_code'].split('|')[1]

    foreign_key_objects.update(validate_order_foreign_keys(order, validation_errors,company))
    if 'order_reference' in order and order.get('order_reference') is not None:
        check_order_reference(order, company, validation_errors)

    return validation_errors, foreign_key_objects

def validate_order_foreign_keys(data, validation_errors,company):
    foreign_key_objects = {}
    if 'country' in data and data['country']:
        foreign_key_objects['country'] = get_country(data, validation_errors)

    if 'area' in data and data['area']:
        foreign_key_objects['area'] = get_area(data, validation_errors, foreign_key_objects['country']) if 'country' in foreign_key_objects else get_area(data, validation_errors, None)

    if 'sub_area' in data and data['sub_area']:
        foreign_key_objects['sub_area'] = get_sub_area(data, validation_errors, foreign_key_objects['area']) if 'area' in foreign_key_objects else get_sub_area(data, validation_errors, None)

    if 'order_type' in data and data['order_type']:
        foreign_key_objects['order_type'] = get_order_type(data, validation_errors)

    if 'branch_page' in data and data['branch_page']:
        foreign_key_objects['branch_page'] = get_branch_page(data, validation_errors,company)

    if 'parent_order' in data and data['parent_order']:
        foreign_key_objects['parent_order'] = get_order(data.get('parent_order'))

    if 'delivery_company' in data and data['delivery_company']:
        foreign_key_objects['delivery_company'] = get_delivery_company(data['delivery_company'])
        if not foreign_key_objects['delivery_company']:
            validation_errors.append('Invalid delivery_company ID')

    if 'driver' in data and data['driver'] is not None:
        foreign_key_objects['driver'] = get_driver(data['driver'])
        if not foreign_key_objects['driver']:
            validation_errors.append('Invalid driver ID')

    if 'connection' in data and data.get('connection', None):
        foreign_key_objects['connection'] = get_connection(data['connection'])
        if not foreign_key_objects['connection']:
            validation_errors.append('Invalid connection ID')

    if data.get('stuck_reason', None):
        foreign_key_objects['connection'] = get_connection(data['connection'])
        if not foreign_key_objects['connection']:
            validation_errors.append('Invalid connection ID')

    if data.get('warehouse', None):
        foreign_key_objects['warehouse'] = get_warehouse(data.get('warehouse'))
        if not foreign_key_objects['warehouse']:
            validation_errors.append('Invalid warehouse ID')

    return foreign_key_objects

def check_order_reference(order, company, validation_errors):
    if Order.objects.filter(order_reference=order.get('order_reference'), company=company).exists():
        validation_errors.append(_('Order ') + str(order["order_reference"]) + _(' already exists'))

def build_order_values(json_data, foreign_key_objects, company, name):
    values = {**json_data, **foreign_key_objects, 'company': company}
    values['status'] = Status.objects.get(code="new_order")
    values['created_by'] = values['updated_by'] = name
    return values

def get_driver(id):
    try:
        return User.objects.get(id=id)
    except User.DoesNotExist:
        return None
    except Exception as e:
        return None

def auto_send_orders(order_list, company, name, status):
    company_conf = get_company_conf(company)
    if not company_conf.auto_send_orders:
        return [], []
    if not status.code == company_conf.auto_send_on_status:
        return [], []
    success_messages = []
    fail_messages = []
    default_connection = get_default_connection(company)
    driver_orders = []
    delivery_company_orders = {}
    for order in order_list:
        if order.area_mismatch and not company_conf.force_send_on_area_mismatch:
            continue
        if order.driver:
            driver_orders.append((order.driver.id, order.id))
        elif order.connection:
            connection = order.connection
            if connection.id not in delivery_company_orders:
                delivery_company_orders[connection.id] = []
            delivery_company_orders[connection.id].append(order.id)
        elif default_connection and not order.connection:
            if default_connection.id not in delivery_company_orders:
                delivery_company_orders[default_connection.id] = []
            delivery_company_orders[default_connection.id].append(order.id)

    driver_success_messages, driver_fail_messages = process_driver_orders_wrapper(driver_orders, name, auto_send=True)

    delivery_request_status = get_company_delivery_request_status(company)
    delivery_success_messages, delivery_fail_messages = process_delivery_orders_wrapper(delivery_company_orders, name, delivery_request_status, auto_send=True)
    success_messages.extend(driver_success_messages)
    success_messages.extend(delivery_success_messages)
    fail_messages.extend(driver_fail_messages)
    fail_messages.extend(delivery_fail_messages)
    return success_messages, fail_messages
    
def update_order_util(values, order_lines, user):
    order = get_order(values['id'])
    if not order:
        return None, None

    log = create_log('Order', values['company'], 'update', values['updated_by'], order.id)
    with transaction.atomic():
        vhub_vals, missing_values = update_order_fields(order, values, log, user=user)
        create_order_lines(order, order_lines, values['updated_by'],user=user)
        order.save()
        handle_stock(order)
    delete_log_if_no_changes(log)
    order.save()
    if vhub_vals:
        warning = handle_update_order(order, vhub_vals, values['company'])
    else:
        warning = None
    if missing_values:
        missing_values_str = ', '.join(map(str, missing_values))
        
        if warning:
            warning['message'] += '\n' + _('fields not reflected: ') + missing_values_str
        else:
            warning = {
                'success': False,
                'message': _('fields not reflected: ') + missing_values_str,
                'what_to_do': _('Contact Support to resolve this issue')
            }

    return order, warning

def update_order_statuses(values, order):
    log = create_log('Order', values['company'], 'update', values['updated_by'], order.id)
    if values.get('status').code == 'money_received' and not order.money_received_date:
        amount_received = order.order_due_amount
        create_log_info(log, 'amount_received', order.amount_received, order.amount_received + float(amount_received))
        order.amount_received = order.amount_received + amount_received
        order.money_received_date = timezone.now()
    try:
        with transaction.atomic():
            vhub_vals, missing_values = update_order_fields(order, values, log)
    except Exception as e:
        delete_log_if_no_changes(log)
        raise ValidationError('Order was not updated due to' + str(e))
    delete_log_if_no_changes(log)
    order.save()
    return order

def update_order_fields(order, values, log, user=None):
    vhub_vals = {}
    fields = get_tracked_fields(order.company, 'Order')
    values_missing = []
    prevent_sale_update = False
    if user and order.status.code != 'new_order' and user.role.code == 'sales':
        prevent_sale_update = True
    for key, value in values.items():
        vhub_vals, missing_value = update_vhub_vals(vhub_vals, key, value, order, values)
        if missing_value:
            values_missing.append(missing_value)
        old_value = getattr(order, key, None)
        if old_value != value:
            if prevent_sale_update and key in ['total_cod', 'package_cost', 'extra_delivery_fee', 'profit', 'extra_cost', 'discount']:
                raise get_error_response('ORDER_203', {'status': _(order.status.name)})
            log_and_update_field(order, key, value, old_value, log, bool(key in fields))

    return vhub_vals, values_missing


def update_vhub_vals(vhub_vals, key, value, order, values):
    order_value = getattr(order, key, None)
    delivery_status = getattr(order, 'delivery_company_status', None)
    vhub_field_mapping = VhubFieldMap.objects.filter(status__delivery_company_status=delivery_status, company=order.company, field=key).last()
    model = order._meta.model
    vhub_value = None
    if model and vhub_field_mapping:
        field = model._meta.get_field(key)
        if field:
            if isinstance(field, models.CharField):
                if str(order_value) != str(value):
                    vhub_vals[vhub_field_mapping.vhub_field] = value
            elif isinstance(field, models.FloatField):
                if float(order_value) != float(value):
                    vhub_vals[vhub_field_mapping.vhub_field] = float(value)
            elif isinstance(field, models.BooleanField):
                if order_value!= value:
                    vhub_vals[vhub_field_mapping.vhub_field] = value
            elif isinstance(field, models.ForeignKey) and value:
                if order_value and value and order_value.id != value.id:
                    try:
                        vhub_foreign_key_mapping = VhubForeignKeyMap.objects.get(field=key)
                        if vhub_foreign_key_mapping:
                            query_params = {vhub_foreign_key_mapping.model_key: value.id}
                            foreign_key_model = apps.get_model('api', vhub_foreign_key_mapping.model)
                            try:
                                if foreign_key_model == AreaMap or foreign_key_model == SubAreaMap:
                                    query_params['delivery_company'] = order.delivery_company
                                model_instance = foreign_key_model.objects.get(**query_params)
                                vhub_value = getattr(model_instance, vhub_foreign_key_mapping.model_value, None)
                                if vhub_value:
                                    vhub_vals[vhub_field_mapping.vhub_field] = vhub_value
                                    if vhub_field_mapping.vhub_field == 'paid':
                                        vhub_vals['delivery_cost_on_customer']= False if vhub_value else True
                                else:
                                    return vhub_vals, value.name
                            except foreign_key_model.DoesNotExist:
                                pass
                    except VhubForeignKeyMap.DoesNotExist:
                        pass
    return vhub_vals, None

def add_order_to_delivery_company(order, connection, orders_by_delivery_company):
    connection_id = connection.id
    if connection_id not in orders_by_delivery_company:
        orders_by_delivery_company[connection_id] = []
    mapped_area, mapped_subarea = map_order_area(order, connection.delivery_company)
    try:
        delivery_fee = get_delivery_fee_util(order.company, connection, order.area)
    except Exception as e:
        delivery_fee = 0
    if not delivery_fee:
        delivery_fee = 0
    difference_in_delivery_fee = delivery_fee - order.delivery_fee
    order.delivery_fee = delivery_fee
    if not order.is_fixed_total:
        order.total_cod += difference_in_delivery_fee
    order.save()
    connection_user = connection.credentials
    orders_by_delivery_company[connection_id].append(order_to_json(order, order.company, mapped_area, mapped_subarea, connection_user))

def handle_delivery_company_status(order, company, status):
    if order.connection and order.olivery_sequence:
        try:
            connection_user = order.connection.credentials
        except Exception as e:
            connection_user = None
        if connection_user:
            vhub_vals = {}
            if status.name == 'Cancelled':
                vhub_vals['partner_status'] = 'Cancelled'
                vhub_vals['internal_partner_status'] = 'canceled'
                vhub_vals['state'] = 'canceled'
                response = update_order_to_vhub(vhub_vals, order.olivery_sequence, order.connection.delivery_company, connection_user, company)
                if isinstance(response, dict):
                    if response.get('status') == False:
                        order.send_failed = True
                        order.save()
                        return response
                return None
                
def handle_update_order(order, vhub_vals, company):
    if not order.connection:
        return
    try:
        connection_user = order.connection.credentials
    except Exception as e:
        connection_user = None
    response = update_order_to_vhub(vhub_vals, order.olivery_sequence, order.connection.delivery_company, connection_user, company)
    if isinstance(response, dict):
        if response.get('status') == False:
            order.send_failed = True
            order.save()
            return response
    return None
                
def get_company(company_id):
    try:
        return Company.objects.get(company_id=company_id)
    except Company.DoesNotExist:
        return None

def get_pricelist(company):
    return Pricelist.objects.filter(company=company, delivery_company=None).first()

def get_pricelist_item(pricelist, from_area, to_area):
    return PricelistItem.objects.filter(pricelist=pricelist, from_area=from_area, to_area=to_area).first()

def create_order_line(order, order_line, item_id, fk_objects, name):
    current_order = get_order(order.id)
    index = int(item_id.split('_')[-1])
    item = order_line[index]
    values = build_order_line_values(item, fk_objects, order)
    order_line = OrderLine.objects.create(**values)
    if current_order.parent_order and not current_order.order_type.code in ['replacement','retrieval']:
        order_line.virtual_deducted = order_line.quantity
        order_line.physical_deducted = order_line.quantity
        order_line.save()
    log = create_log('OrderLine', order.company, 'create', name, order_line.id)
    log_tracked_fields(log, values, get_tracked_fields(order.company, 'OrderLine'))
    delete_log_if_no_changes(log)

def get_existing_order_lines_ids(order):
    return set(map(str, OrderLine.objects.filter(order=order).values_list('id', flat=True)))

def create_order_lines(order, order_lines, name, user=None):
    validation_errors, item_foreign_key_objects = validate_and_prepare_order_lines(order, order_lines)

    if validation_errors:
        raise ValidationError(validation_errors)

    with transaction.atomic():
        existing_item_ids = get_existing_order_lines_ids(order)
        item_ids = set(item_foreign_key_objects.keys())
        ids_to_delete = existing_item_ids - item_ids

        if ids_to_delete:
            delete_order_lines(order, ids_to_delete, order.company, name)

        process_order_lines(order, order_lines, item_foreign_key_objects, name, user=user)

def validate_and_prepare_order_lines(order, order_lines):
    validation_errors = []
    item_foreign_key_objects = {}

    for index, item in enumerate(order_lines):
        errors, fk_objects = validate_order_line(order, item)
        validation_errors.extend(errors)

        item_id = str(item.get('id', ''))

        if not item_id:
            temp_key = f'new_item_{index}'
            item_foreign_key_objects[temp_key] = fk_objects
        else:
            if item_id.isdigit():
                item_foreign_key_objects[item_id] = fk_objects
            else:
                validation_errors.append(f'Invalid item ID: {item_id}')

    return validation_errors, item_foreign_key_objects

def delete_order_lines(order , item_ids, company, name):
    for item_id in item_ids:
        order_line = OrderLine.objects.get(id=item_id)
        log = create_log('Order', company, f'REMOVE&{order_line.product_variant.variant.name}&{order_line.quantity}&{order_line.price}', name, order.id)
        if order_line.virtual_deducted:
            order_line.product_variant.virtual_quantity += order_line.virtual_deducted
        if order_line.physical_deducted:
            order_line.product_variant.physical_quantity += order_line.physical_deducted
        order_line.product_variant.save()
        order_line.delete()

def process_order_lines(order, order_lines, item_foreign_key_objects, name, user=None):
    prevent_sale_update = False
    if order.status.code != 'new_order' and user.role.code == 'sales':
        prevent_sale_update = True
    for item_id, fk_objects in item_foreign_key_objects.items():
        if item_id.startswith('new_item_'):
            if prevent_sale_update:
                raise get_error_response('ORDER_203', {'status': _(order.status.name)})
            create_order_line(order, order_lines, item_id, fk_objects, name)
        else:
            update_order_line(item_id, order, order_lines, fk_objects, name, prevent_sale_update=prevent_sale_update)

def update_order_line(item_id, order, order_lines, fk_objects, name, prevent_sale_update=False):
    try:
        order_line = OrderLine.objects.get(id=item_id)
    except OrderLine.DoesNotExist:
        raise ValidationError([f'OrderLine with ID {item_id} does not exist.'])

    item = next((item for item in order_lines if str(item.get('id')) == item_id), None)
    if item is None:
        raise ValidationError([f'OrderLine with ID {item_id} not found in the provided OrderLines.'])

    values = build_order_line_values(item, fk_objects, order)
    log = create_log('OrderLine', order.company, 'update', name, item_id)
    use_stock = False
    try:
        conf = get_company_conf(order.company)
        use_stock = conf.use_stock
    except Exception as e:
        pass

    for key, value in values.items():
        old_value = getattr(order_line, key, None)
        if isinstance(old_value, Decimal):
            value = Decimal(value)
        if old_value != value:
            if prevent_sale_update:
                raise get_error_response('ORDER_203', {'status': _(order.status.name)})
            if key == 'product_variant' and use_stock:
                if order_line.virtual_deducted:
                    order_line.product_variant.virtual_quantity += order_line.virtual_deducted
                    value.virtual_quantity -= order_line.virtual_deducted
                if order_line.physical_deducted:
                    order_line.product_variant.physical_quantity += order_line.physical_deducted
                warehouse_tracking = WarehouseTracking.objects.create(
                    warehouse_variant=order_line.product_variant,
                    physical_change=order_line.physical_deducted,
                    virtual_change=order_line.virtual_deducted,
                    order=order,
                )
                order_line.virtual_deducted = 0
                order_line.physical_deducted = 0
                order_line.product_variant.save()
            log_and_update_field(order_line, key, value, old_value, log, bool(key in get_tracked_fields(order.company, 'OrderLine')))
    
    order_line.save()
    delete_log_if_no_changes(log)

def build_order_line_values(order_line, foreign_key_objects, order):
    product = foreign_key_objects.get('product')
    product_variant = foreign_key_objects.get('product_variant')
    
    return {
        'product': product,
        'quantity': order_line.get('quantity'),
        'price': order_line.get('price'),
        'product_variant': product_variant,
        'order': order
    }

def validate_order_line(order, order_line):
    validation_errors = []
    foreign_key_objects = {}
    
    required_fields = ['quantity', 'price', 'product_variant']
    for field in required_fields:
        if not order_line.get(field):
            validation_errors.append(f'Missing field: {field}')

    if not order_line.get(field):
        validation_errors.append(f'Missing field: {field} in order line')
    if field == 'quantity':
        try:
            quantity = int(order_line.get(field))
            if quantity < 1:
                validation_errors.append(f'Invalid quantity: {order_line["quantity"]}')
        except ValueError:
            validation_errors.append(f'Invalid quantity: {order_line["quantity"]}')

    if 'product_variant' in required_fields:
        product_variant = get_variant(order_line['product_variant'])
        if not product_variant:
            validation_errors.append(f'Variant not found for order line: {order_line}')
        elif product_variant.variant.product.company != order.company:
            validation_errors.append(f'Variant not found for order line: {order_line}')
        else:
            foreign_key_objects['product_variant'] = product_variant
            foreign_key_objects['product'] = product_variant.variant.product

    return validation_errors, foreign_key_objects

key = Fernet.generate_key()
cipher_suite = Fernet(key)
signer = Signer()

def encrypt_data(data):
    encrypted_data = cipher_suite.encrypt(data.encode('utf-8'))
    return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')

def decrypt_data(encrypted_data):
    decrypted_data = cipher_suite.decrypt(base64.urlsafe_b64decode(encrypted_data))
    return decrypted_data.decode('utf-8')

def generate_signed_token(data):
    encrypted_data = encrypt_data(data)
    signed_data = signer.sign(encrypted_data)
    return signed_data

def verify_signed_token(signed_data):
    try:
        encrypted_data = signer.unsign(signed_data)
        data = decrypt_data(encrypted_data)
        return json.loads(data)
    except (BadSignature, TypeError, ValueError):
        return None

from django.db import transaction
from django.core.exceptions import ValidationError

# Helper function to fetch required statuses
def fetch_statuses():
    try:
        return {
            'returned': Status.objects.get(code='returned'),
            'expected_return_money': Status.objects.get(code='expected_return_money'),
            'expected_return_package': Status.objects.get(code='expected_return_package'),
            'money_received': Status.objects.get(code='money_received'),
            'money_collected': Status.objects.get(code='money_collected')
        }
    except Status.DoesNotExist:
        raise ValidationError('Status not found.')

# Get the Order instance by ID
def get_order(order_id):
    try:
        return Order.objects.get(id=order_id)
    except Order.DoesNotExist:
        raise ValidationError(f'Order with ID {order_id} does not exist.')

# Get the Product instance by ID
def get_product(product_id):
    try:
        return Product.objects.get(id=product_id)
    except Product.DoesNotExist:
        raise ValidationError(f'Product with ID {product_id} does not exist.')
    
def get_variant(variant_id):
    try:
        return WarehouseVariant.objects.get(id=variant_id)
    except WarehouseVariant.DoesNotExist:
        return None


def calculate_removed_total(order, removed_order_lines):
    removed_total = 0
    for order_line in removed_order_lines:
        variant = get_variant(order_line['variant_id'])
        try:
            order_line_instance = OrderLine.objects.get(order=order, product_variant=variant)
            removed_total += order_line_instance.quantity * variant.variant.product.price
        except OrderLine.DoesNotExist:
            raise ValidationError(f'OrderLine with variant ID {variant.id} not found for order {order.id}.')
    return removed_total

def clone_order(parent_order, status,money_received,created_by,removed_order_lines=None):
    cloned_order_data = {
        field.name: getattr(parent_order, field.name)
        for field in parent_order._meta.fields
        if field.name not in ['id', 'status', 'parent_order', 'created_at', 'updated_at', 'order_sequence', 'order_reference', 'delivery_fee', 'extra_delivery_fee', 'created_by', 'amount_received']
    }
    cloned_order_data['delivery_fee'] = 0
    cloned_order_data['extra_delivery_fee'] = 0
    cloned_order_data['total_cod'] = money_received
    cloned_order_data['package_cost'] = 0

    cloned_order = Order.objects.create(**cloned_order_data, parent_order=parent_order)
    cloned_order.status = status
    cloned_order.created_by = created_by    
    cloned_order.save()
    log = create_log('Order', cloned_order.company, 'create',created_by, cloned_order.id)
    fields = get_tracked_fields(cloned_order.company, 'Order')
    log_tracked_fields(log, cloned_order_data, fields)
    delete_log_if_no_changes(log)
    if removed_order_lines:
        for order_line in removed_order_lines:
            variant = get_variant(order_line['variant_id'])
            if not variant:
                raise ValidationError(f"Variant with ID {order_line['variant_id']} not found.")
            virtual_deducted = order_line['quantity']
            physical_deducted = order_line['quantity']
            OrderLine.objects.create(
                order=cloned_order,
                product=variant.variant.product,
                quantity=order_line['quantity'],
                price=variant.variant.product.price * order_line['quantity'],
                product_variant=variant,
                virtual_deducted=virtual_deducted,
                physical_deducted=physical_deducted
            )
            for parent_order_line in parent_order.order_lines.all():
                if parent_order_line.product_variant.id == order_line['variant_id']:
                    if parent_order_line.quantity <= order_line['quantity']:
                        log = create_log(
                            'Order',
                            parent_order.company,
                            f'REMOVE&{variant.variant.name}&{parent_order_line.quantity}&{parent_order_line.price}',
                            created_by,
                            parent_order.id
                        )
                        parent_order_line.delete()
                    else:
                        parent_order_line.quantity -= order_line['quantity']
                        price = order_line["quantity"] * variant.variant.product.price
                        log = create_log(
                            'Order',
                            parent_order.company,
                            f'REMOVE&{variant.variant.name}&{order_line["quantity"]}&{price}',
                            created_by,
                            parent_order.id
                        )
                        parent_order_line.save()
    return cloned_order

def handle_order_type(values,is_reseller=False):
    edited_order_ids = []
    statuses = fetch_statuses()
    created_by = values.pop('created_by')
    money_received_fully_paid = values.get('money_received_fully_paid', 0)
    money_received_partially_paid = values.get('money_received_partially_paid', 0)  
    is_fully_paid = values.get('is_fully_paid', False)
    is_partially_paid = values.get('is_partially_paid', False)
    returned_order_lines = values.get('returned_order_lines', None)
    left_order_lines = values.get('left_order_lines', None)
    is_expected_money = values.get('is_expected_money', False)
    parent_order = get_order(values.pop('id'))

    if is_expected_money:
        parent_log = create_log('Order', parent_order.company, 'update', created_by, parent_order.id)
        create_log_info(parent_log, 'status', parent_order.status.name, statuses['expected_return_money'].name)
        parent_order.status = statuses['expected_return_money']
        parent_order.save()
        handle_stock(parent_order)
        edited_order_ids.append(parent_order.id)

    if is_fully_paid:
        parent_log = create_log('Order', parent_order.company, 'update', created_by, parent_order.id)
        if is_reseller:
            create_log_info(parent_log, 'status', parent_order.status.name, statuses['money_collected'].name)
            parent_order.status = statuses['money_collected']
        else:
            create_log_info(parent_log, 'status', parent_order.status.name, statuses['money_received'].name)
            parent_order.status = statuses['money_received']
        create_log_info(parent_log, 'amount_received', parent_order.amount_received, money_received_fully_paid)
        parent_order.amount_received = money_received_fully_paid
        parent_order.save()
        handle_stock(parent_order)
        edited_order_ids.append(parent_order.id)
        
    if is_partially_paid:
        cloned_order =  clone_order(parent_order,statuses['expected_return_money'],money_received_partially_paid,created_by)
        edited_order_ids.append(cloned_order.id)
        parent_log = create_log('Order', parent_order.company, 'update', created_by, parent_order.id)
        if not is_fully_paid:
            if is_reseller:
                create_log_info(parent_log, 'status', parent_order.status.name, statuses['money_collected'].name)
                parent_order.status = statuses['money_collected']
            else:
                create_log_info(parent_log, 'status', parent_order.status.name, statuses['money_received'].name)
                parent_order.status = statuses['money_received']
            parent_order.save()
        create_log_info(parent_log, 'clone Order', None, cloned_order.order_sequence)
        cloned_log = create_log('Order', cloned_order.company, 'clone', created_by, cloned_order.id)
        create_log_info(cloned_log, 'parent Order', None, parent_order.order_sequence)
        handle_stock(parent_order)
        handle_stock(cloned_order)
        edited_order_ids.append(parent_order.id)
    
    if isinstance(returned_order_lines,list):
        if is_fully_paid or is_partially_paid:
            cloned_order = clone_order(parent_order, statuses['returned'], 0, created_by, returned_order_lines)
            handle_stock(cloned_order)
            edited_order_ids.append(cloned_order.id)
            parent_log = create_log('Order', parent_order.company, 'update', created_by, parent_order.id)
            create_log_info(parent_log, 'clone Order', None, cloned_order.order_sequence)
            cloned_log = create_log('Order', cloned_order.company, 'clone', created_by, cloned_order.id)
            create_log_info(cloned_log, 'parent Order', None, parent_order.order_sequence)
        else:
            parent_log = create_log('Order', parent_order.company, 'update', created_by, parent_order.id)
            create_log_info(parent_log, 'status', parent_order.status.name, statuses['returned'].name)
            parent_order.status = statuses['returned']
            parent_order.save()
            handle_stock(parent_order)
            edited_order_ids.append(parent_order.id)

    if isinstance(left_order_lines,list):
        cloned_order = clone_order(parent_order, statuses['expected_return_package'], 0, created_by, left_order_lines)
        handle_stock(cloned_order)
        edited_order_ids.append(cloned_order.id)
        parent_log = create_log('Order', parent_order.company, 'update', created_by, parent_order.id)
        create_log_info(parent_log, 'clone Order', None, cloned_order.order_sequence)
        cloned_log = create_log('Order', cloned_order.company, 'clone', created_by, cloned_order.id)
        create_log_info(cloned_log, 'parent Order', None, parent_order.order_sequence)

    return edited_order_ids

def process_excel_orders(file, user):
    header_mapping = {
        'customer_name': 'customer_name',
        'customer_mobile': 'customer_mobile',
        'area': 'area',
        'sub_area': 'sub_area',
        'address': 'address',
        'product_info': 'product_info',
        'note': 'note',
        'total_cod': 'total_cod',
        'package_cost': 'package_cost',
        'created_by': 'created_by',
        'order_reference': 'order_reference',
        'country': 'country',
        'product_variant': 'product_variant',
        'quantity': 'quantity',
        'is_fixed_total': 'is_fixed_total',
        'store_page': 'branch_page',
    }
    
    required_fields = ['total_cod', 'address', 'area', 'sub_area', 'customer_mobile', 'customer_name', 'package_cost']
    df = read_and_validate_excel(file, header_mapping, required_fields)

    if df.empty:
        raise get_error_response('GENERAL_010')
    validate_order_count(user, len(df))
    orders, errors, warnings = process_orders_from_df(df, user, header_mapping)
    return (orders, errors, warnings) if not errors else (None, errors, warnings)

def validate_order_count(user, order_count):
    if get_remaining_orders_count(user.company) < order_count:
        raise get_error_response('BILLING_801', {})

def process_orders_from_df(df, user, header_mapping):
    company_conf = get_company_conf(user.company)
    default_area, default_country = company_conf.default_area, company_conf.default_country
    orders, errors, warnings = [], [], []
    
    try:
        status = Status.objects.get(code='new_order')
    except Status.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Status', 'field': 'code', 'value': 'new_order'})

    grouped_orders = {}
    
    for index, row in df.iterrows():
        row_data = row.to_dict()
        
        if row_data.get('customer_name') and not pd.isna(row_data.get('customer_name')):
            order_key = f'{row_data.get("customer_name")}-{row_data.get("customer_mobile")}'

        if order_key not in grouped_orders:
            order_data = row_data.copy()
            order_data = normalize_order_data(order_data, user, company_conf)
            order_data.pop('product_variant', None)
            order_data.pop('quantity', None)
            order_data['status'] = status
            order_data['connection'] = company_conf.default_connection.id if company_conf.default_connection else None
            order_data['order_lines'] = []
            grouped_orders[order_key] = order_data
        else:
            order_data = grouped_orders[order_key]

        order_line = process_excel_order_lines(row_data, user, errors, index)
        if order_line:
            grouped_orders[order_key]['order_lines'].append(order_line)

    for order_data in grouped_orders.values():
        required_fields = ['address', 'area', 'sub_area', 'customer_mobile', 'customer_name']
        if order_data['is_fixed_total']:
            required_fields.append('total_cod')
        else:
            required_fields.append('package_cost')
        validation_errors, foreign_keys = validate_order(
            order_data, user.company, required_fields
        )
        try:
            delivery_fee = get_delivery_fee_util(user.company, foreign_keys['connection'], foreign_keys['area'])
        except Exception as e:
            delivery_fee = 0
        if not order_data.get('is_fixed_total'):
            if order_data.get('order_lines', []):
                package_cost = 0
                for order_line in order_data.get('order_lines', []):
                    package_cost += float(order_line.get('price', 0))
                order_data['package_cost'] = package_cost
            order_data['total_cod'] = float(order_data.get('package_cost')) + delivery_fee
            order_data['delivery_fee'] = delivery_fee
        else:
            calculate_cod_and_fees(order_data, user.company, foreign_keys)
        handle_default_mappings(order_data, validation_errors, foreign_keys, default_country, default_area)

        if validation_errors:
            errors.append({'order_reference': order_data.get('order_reference'), 'errors': validation_errors})
            continue

        orders.append((order_data, foreign_keys))

    return orders, errors, warnings

def normalize_order_data(order_data, user, company_conf):
    if order_data.get('created_by', None) and not pd.isna(order_data.get('created_by')):
        order_data['created_by'] = order_data.get('created_by')
        order_data['updated_by'] = order_data['created_by']
    else:
        order_data['created_by'] = user.name
        order_data['updated_by'] = user.name
    
    if order_data.get('country', None) and not pd.isna(order_data.get('country')):
        order_data['country'] = order_data.get('country')
    else:
        order_data['country'] = company_conf.default_country.code
    order_data = normalize_country_and_mobile(order_data)

    if not pd.isna(order_data.get('is_fixed_total')):
        if order_data.get('is_fixed_total', '').lower() in ['t', 'true', 'fixed', 'ثابت']:
            order_data['is_fixed_total'] = True
        elif order_data.get('is_fixed_total', '').lower() in ['f', 'false', 'calculated', 'محوسب']:
            order_data['is_fixed_total'] = False
        else:
            order_data['is_fixed_total'] = company_conf.is_fixed_total
    else:
        order_data['is_fixed_total'] = company_conf.is_fixed_total      
    
    for key, value in order_data.items():
        if pd.isna(value):
            if key in ['package_cost', 'total_cod']:
                order_data[key] = 0
            else:
                order_data[key] = None
    
    return order_data


def handle_default_mappings(order_data, errors, foreign_keys, default_country, default_area):
    default_values = {
        'Invalid country name': ('country', default_country),
        'Invalid area name': ('area', default_area),
        'Invalid sub area name': ('sub_area', SubArea.objects.filter(area=foreign_keys.get('area')).first())
    }
    
    for error, (key, default) in default_values.items():
        if error in errors:
            foreign_keys[key] = default

def process_excel_order_lines(order_data, user, errors, index):
    product_variant = order_data.get('product_variant', None)
    quantity = order_data.get('quantity', 1)

    if product_variant and not pd.isna(product_variant):
        order_line = {
            "product_variant": product_variant,
            "quantity": int(quantity) if not pd.isna(quantity) else 0,
        }

        order_line, line_errors = validate_order_line_before_create(order_line, user.company, country=order_data.get('country', None))
        if line_errors:
            errors.append({'index': index + 2, 'errors': line_errors})
            return None
        if order_line:
            return order_line

    return None

def validate_order_line_before_create(order_line, company, country=None):
    errors = []
    
    product_variant_input = order_line.get('product_variant', None)
    if not product_variant_input:
        return None, errors
    variants = ProductVariant.objects.filter(
        models.Q(sku=product_variant_input) | models.Q(name=product_variant_input),
        product__company=company
    )

    count = variants.count()
    if count == 1:
        variant = variants.first()
        warehouse_variant = WarehouseVariant.objects.filter(variant_id=variant.id).first()
        if warehouse_variant:
            order_line['product_variant'] = warehouse_variant.id
        else:
            errors.append(f"Warehouse variant not found for variant: {variant.name}")
    elif count > 1:
        errors.append(f"Multiple product variants found: {', '.join(variants.values_list('name', flat=True))}")
    else:
        errors.append("Product variant not found")

    try:
        quantity = int(order_line.get('quantity', 0))
        if quantity <= 0:
            errors.append("Quantity must be a positive integer")
    except ValueError:
        errors.append("Quantity must be an integer")

    if product_variant_input and count == 1:
        country = get_country({'country': country}, [])
        product = variants.first().product
        product_price = ProductPrice.objects.filter(product=product, country=country).first()
        price = product_price.price if product_price else product.price
        total_price = price * quantity
    else:
        return None, errors

    return {
        "product_variant": order_line.get('product_variant'),
        "quantity": quantity,
        "price": f"{total_price:.2f}"
    }, errors

def get_delivery_fee(company, area):
    company_conf = get_company_conf(company)
    if not company_conf:
        return 0, None

    default_connection = company_conf.default_connection
    if not default_connection:
        return 0, None

    try:
        pricelist = Pricelist.objects.get(company=company, connection=default_connection)
        pricelist_item = PricelistItem.objects.filter(pricelist=pricelist, from_area=default_connection.area, to_area=area).last()
        return pricelist_item.price if pricelist_item else 0, default_connection

    except (Pricelist.DoesNotExist, PricelistItem.DoesNotExist, ConnectDeliveryCompany.DoesNotExist):
        return 0, default_connection
    
def create_branch_page_util(values):
    try:
        branch_page = BranchPage.objects.create(**values)
        log = create_log('BranchPage', branch_page.company, 'create', values['created_by'], branch_page.id)
        log_tracked_fields(log, values, get_tracked_fields(branch_page.company, 'BranchPage'))
        delete_log_if_no_changes(log)
        return branch_page
    except Exception as e:
        print(f'Error creating BranchPage: {str(e)}')
        return None
    
def update_branch_page_util(values):
    try:
        branch_page = BranchPage.objects.get(id=values['id'])
    except BranchPage.DoesNotExist:
        return None
    try:
        log = create_log('BranchPage', branch_page.company, 'update', values['updated_by'], branch_page.id)
        update_branch_page_fields(branch_page, values, log)
        delete_log_if_no_changes(log)
        branch_page.save()
        return branch_page
    except Exception as e:
        print(f'Error updating BranchPage: {str(e)}')
        return None
    
def update_branch_page_fields(branch_page, values, log):
    fields = get_tracked_fields(branch_page.company, 'BranchPage')

    for key, value in values.items():
        old_value = getattr(branch_page, key, None)
        if old_value != value:
            log_and_update_field(branch_page, key, value, old_value, log, bool(key in fields))

def calculate_cod_and_fees(order, company, foreign_key_objects):
    if 'total_cod' in order and 'area' in foreign_key_objects :
        connection = foreign_key_objects.get('connection', None)
        if not connection:
            delivery_fee, connection = get_delivery_fee(company, foreign_key_objects['area'])
        else:
            try:
                delivery_fee = get_delivery_fee_util(company, connection, foreign_key_objects['area'])
            except Exception as e:
                delivery_fee = 0
        order['delivery_fee'] = delivery_fee
        foreign_key_objects['connection'] = connection
        if order['total_cod'] == 0:
            order['paid'] = True
        elif 'delivery_fee' in order:
            order['package_cost'] = float(order['total_cod']) - float(order['delivery_fee']) - float(order.get('extra_delivery_fee', 0))
        elif 'delivery_fee' not in order:
            order['package_cost'] = float(order['total_cod'])

def sync_delivery_fees_util(order_ids):
    for order_id in order_ids:
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'id', 'value': order_id})
        if order.olivery_sequence and order.connection and order.connection.delivery_company.is_olivery_company:
            connection = order.connection
            connection_user = connection.credentials
            params = {
                "jsonrpc": "2.0",
                "params": {
                    'db': order.delivery_company.delivery_company_db,
                    'login': connection_user.company_username,
                    'password': connection_user.company_password,
                    'order_ids': [order.olivery_sequence]
                }
            }
            response = send_api_to_delivery_company(order.delivery_company, '/order', params, return_raw=True)
            if response:
                result = response.get('result', {})
                log = create_log('Order', order.company, 'Synced delivery fee', order.delivery_company.name, order_id)
                if result.get('status', 403) == 200:
                    old_delivery_fee = order.delivery_fee
                    order.delivery_fee = result.get('response', {})[0].get('delivery_cost', 0) or 0
                    order.save()
                    log_and_update_field(order, 'delivery_fee', order.delivery_fee, old_delivery_fee, log, True) 
                else:
                    delete_log_if_no_changes(log)
                    raise get_error_response('CONNECTION_308', {'delivery_company': connection.delivery_company.name, 'error': result.get('message', 'Unexpected Error')})
        else:
            request_template = RequestTemplate.objects.get(delivery_company=order.connection.delivery_company, name='get_order_delivery_fee')
            params = build_request_params(request_template, order)
            response = send_dynamic_api_request(request_template, params)
            log = create_log('Order', order.company, 'Synced delivery fee', order.delivery_company.name, order_id)
            delivery_fee = response.get('data')
            old_delivery_fee = order.delivery_fee
            if delivery_fee:
                log_and_update_field(order, 'delivery_fee', order.delivery_fee, old_delivery_fee, log, True) 
            delete_log_if_no_changes(log)
            order.delivery_fee = delivery_fee
            order.save()


def duplicate_order_util(order, user):
    new_order_data = {  
        field.name: getattr(order, field.name)
        for field in order._meta.fields
        if field.name not in ['id', 'status', 'created_at', 'updated_at', 'created_by','updated_by','reschedule_date','money_received_date','delivered_by','send_to_delivery_date','store_stuck_comment',
                              'ready_for_delivery_date','preparing_date','return_date','delivery_date','delivery_status_change_date','delivery_status','store_status_change_date','reject_reason',
                             'created_by_user','order_sequence', 'order_reference', 'olivery_sequence','delivery_company_status','tried_to_delivered','receipt_date','stuck_comment']
    }
    new_order_data['created_by'] = user.name
    new_order_data['status'] = Status.objects.get(code='new_order')
    new_order_lines = []
    order_lines = order.order_lines.all()
    for order_line in order_lines:
        new_order_lines.append({
            'product_variant': order_line.product_variant.id,
            'quantity': order_line.quantity,
            'price': order_line.price
        })
    new_order = create_order(new_order_data, new_order_lines)
    message = f'Duplicate order from {order.order_sequence}'
    create_log('Order', new_order.company, message, user.name, new_order.id)
    return new_order