from django.db import transaction
from ..mystore_model import *
from ..mystore_utils import *
from .order_model import *



def create_store_order(user,mystore,order):
    try:
        with transaction.atomic():
            store_order = StoreOrder.objects.create(user = user, store = mystore, order = order)
    except Exception as e:
        raise ValidationError(f"Error creating order: {str(e)}")
    return store_order