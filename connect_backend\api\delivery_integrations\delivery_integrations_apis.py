from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from ..serializers import *
from rest_framework.decorators import *
from ..permissions import *
from .delivery_integrations_model import *
from ..error.error_model import *
from ..util_functions import *
from django.utils.translation import gettext as _
from rest_framework.permissions import AllowAny

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_connectiontoken'])])
def get_connection_token(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    delivery_company_id = json_data.get('delivery_company_id')
    delivery_company = DeliveryCompany.objects.get(id=delivery_company_id)
    try:
        connection_token = ConnectionToken.objects.get(delivery_company=delivery_company, company=user.company)
    except ConnectionToken.DoesNotExist:
        connection_token = None
    if not connection_token:
        return JsonResponse({'success': True, 'connection_token': None}, status=200)
    serializer = ConnectionTokenSerializer(connection_token)
    return JsonResponse({'success': True, 'connection_token': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_connectiontoken'])])
def add_connection_token(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    delivery_company_id = json_data.get('delivery_company_id')
    delivery_company = DeliveryCompany.objects.get(id=delivery_company_id)
    connection_token, _ = ConnectionToken.objects.get_or_create(delivery_company=delivery_company, company=user.company, defaults={'is_active': True})
    serializer = ConnectionTokenSerializer(connection_token)
    return JsonResponse({'success': True, 'connection_token': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_connectiontoken'])])
def regenerate_connection_token(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    delivery_company_id = json_data.get('delivery_company_id')
    delivery_company = DeliveryCompany.objects.get(id=delivery_company_id)
    connection_token = ConnectionToken.objects.get(delivery_company=delivery_company, company=user.company)
    connection_token.save()
    serializer = ConnectionTokenSerializer(connection_token)
    return JsonResponse({'success': True, 'connection_token': serializer.data}, status=200)

@api_view(['POST'])
@authentication_classes([DeliveryIntegrationAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def update_order_status(request):
    connection = request.user
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    order_sequence = json_data.get('order_sequence')
    if not order_sequence:
        raise get_error_response('GENERAL_003', {'fields': 'order_sequence'})
    status_code = json_data.get('status_code')
    if not status_code:
        raise get_error_response('GENERAL_003', {'fields': 'status_code'})
    
    company = connection.company
    delivery_company = connection.delivery_company
    
    try:
        order = Order.objects.get(order_sequence=order_sequence, company=company)
    except Order.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Order', 'field':'order_sequence', 'value': order_sequence})
    
    if order.delivery_company != delivery_company:
        raise get_error_response('INTEGRATION_1300', {'delivery_company': delivery_company.name, 'order_sequence': order_sequence})
    
    try:
        status = StatusMap.objects.get(status_code=status_code)
    except Status.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Status', 'field':'code', 'value': status_code})

    order.delivery_company_status = status.delivery_company_status
    order.updated_by = delivery_company.name
    order.save()
    return JsonResponse({'success': True, 'message': 'Status Updated Successully'}, status=200)
    
@api_view(['POST'])
@authentication_classes([DeliveryIntegrationAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def refresh_token(request):
    connection = request.user
    connection.save()
    return JsonResponse({'success': True,'message': 'Token Refreshed Successfully', 'token': connection.token}, status=200)

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def update_status_webhook(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    delivery_company_id = request.GET.get('delivery_company')
    delivery_company = DeliveryCompany.objects.filter(id=delivery_company_id).first()
    if not delivery_company:
        raise get_error_response('GENERAL_002', {'model': 'DeliveryCompany', 'field': 'id', 'value': delivery_company_id})

    response_template = ResponseTemplate.objects.filter(request_template__name='update_status_webhook', request_template__delivery_company=delivery_company).first()

    if not response_template:
        raise get_error_response('GENERAL_002', {'model': 'ResponseTemplate', 'field': 'delivery_company', 'value': delivery_company_id})
    
    response_schema = {
        "success_type": response_template.success_type,
        "success_values": response_template.success_values,
        "failure_values": response_template.failure_values,
        "success_path": response_template.success_path,
        "data_path": response_template.data_path,
        "data_mapping": response_template.data_mapping,
        "error_path": response_template.error_path,
        "process_function": response_template.process_function,
        "request_context": {},
    }

    serializer = DynamicResponseSerializer(response_schema=response_schema)
    response = MockResponse(json_data=json_data, status_code=200)
    processed_response = serializer.parse_response(response)

    if not processed_response.get('success'):
        raise get_error_response('CONNECTION_303', {'delivery_company': delivery_company, 'error': processed_response.get('data')})
    
    order_sequence = processed_response.get('data', {}).get('order_sequence')
    olivery_sequence = processed_response.get('data', {}).get('olivery_sequence')
    if order_sequence:
        order = Order.objects.get(order_sequence=order_sequence, connection__delivery_company=delivery_company)
    elif olivery_sequence:
        order = Order.objects.get(olivery_sequence=olivery_sequence, connection__delivery_company=delivery_company)
    else:
        raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'order_sequence or delivery_sequence', 'value': order_sequence or olivery_sequence})

    delivery_status = str(processed_response.get('data', {}).get('delivery_status'))
    delivery_status_map = DeliveryStatusMap.objects.filter(delivery_company=delivery_company, imported_status_code=delivery_status).first()
    if not delivery_status_map or not delivery_status_map.status:
        raise get_error_response('GENERAL_002', {'model': 'DeliveryStatusMap', 'field': 'imported_status_code', 'value': delivery_status})

    log = create_log('Order', order.company, 'update', delivery_company.name, order.id)
    order.updated_by = delivery_company.name
    if delivery_status_map.status == order.delivery_status:
        delete_log_if_no_changes(log)
        return JsonResponse({'success': True, 'message': 'Status already updated'}, status=200)
    create_log_info(log, 'delivery_company_status', order.delivery_company_status, delivery_status_map.status.delivery_company_status)
    create_log_info(log, 'delivery_status', order.delivery_status.delivery_company_status, delivery_status_map.status.delivery_company_status)
    order.delivery_company_status = delivery_status_map.status.delivery_company_status
    order.delivery_status = delivery_status_map.status
    order.save()
    return JsonResponse({'success': True, 'message': 'Status Updated Successfully'}, status=200)
