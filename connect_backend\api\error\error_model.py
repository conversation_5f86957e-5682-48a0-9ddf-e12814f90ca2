from .error_data import ERROR_MESSAGES
from django.utils.translation import gettext as _

class ConnectError(Exception):
    def __init__(self, code, message, what_to_do, status, context=None):
        self.code = code
        self.message = _(message).format(**(context or {}))
        self.what_to_do = _(what_to_do).format(**(context or {}))
        self.status = status

    def to_dict(self):
        return {
            'success': False,
            'error': {
                'code': self.code,
                'message': self.message,
                'what_to_do': self.what_to_do
            }
        }

    def __str__(self):
        return str(self.message)

def get_error_response(error_code, context=None):
    error_data = ERROR_MESSAGES.get(error_code, {})
    
    if not error_data:
        return ConnectError('UNKNOWN_ERROR', _("Unknown error occurred."), _("Contact support."), status=500)

    return ConnectError(
        code=error_code,
        message=error_data['message'],
        what_to_do=error_data['what_to_do'],
        status=error_data.get('status', 500),
        context=context
    )