import boto3
from django.http import JsonResponse
from rest_framework.decorators import api_view
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ..auth.auth_model import *
import uuid

@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def generate_presigned_url(request):
    file_name = request.GET.get('file_name')
    file_type = request.GET.get('file_type')

    if not file_name or not file_type:
        return JsonResponse({'error': 'Missing file_name or file_type'}, status=400)

    s3_client = boto3.client(
        's3',
        region_name=settings.AWS_S3_REGION_NAME,
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
    )
    try:
        unique_file_name = f"{uuid.uuid4()}"
        presigned_url = s3_client.generate_presigned_url(
            'put_object',
            Params={
                'Bucket': settings.AWS_STORAGE_BUCKET_NAME,
                'Key': f'public-folder/{unique_file_name}',
                'ContentType': file_type,
            },
            ExpiresIn=3600  # URL expiration time in seconds
        )
        return JsonResponse({'url': presigned_url, 'file_name': unique_file_name})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
