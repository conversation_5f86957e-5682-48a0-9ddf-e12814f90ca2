from .billing_model import *
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import get_object_or_404, redirect
from django.http import JsonResponse
from .billing_utils import *
from ..serializers import *
from ..util_functions import *
from ..mystore.mystore_utils import *
from ..collection.collection_utils import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ..permissions import *
from ..error.error_model import *
import stripe
import logging
from django.utils.translation import gettext as _

logger = logging.getLogger(__name__)

endpoint_secret = settings.STRIPE_WEBHOOK_SECRET
stripe.api_key = settings.STRIPE_TEST_SECRET_KEY

@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def current_plan(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    company = user.company
    try:
        subscription = get_current_subscription(company)
    except Exception as e:
        return JsonResponse({'success': False, 'subscription': None}, status=200)
    serializer = SubscriptionSerializer(subscription)
    return JsonResponse({'success': True, 'subscription': serializer.data}, status=200)

@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_order_count_and_limit(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    company = user.company
    
    try:
        subscription = get_current_subscription(company)
    except Exception as e:
        return JsonResponse({'success': False, 'order_count': 0, 'order_limit': 0}, status=200)
    order_count = get_orders_created_in_current_plan(company, subscription)
    order_limit = get_orders_limit(subscription)
    return JsonResponse({'success': True, 'order_count': order_count, 'order_limit': order_limit}, status=200)
    
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_prorated_charge(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    company = user.company

    try:
        subscription = get_current_subscription(company)
    except:
        return JsonResponse({'success': False, 'error': 'Subscription not found'}, status=404)
    
    package = request.GET.get('package')

    try:
        try:
            package = Package.objects.get(id=package)
        except Package.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Package not found'}, status=404)
        month = get_discount_month(subscription, package.price_monthly)
        year = get_prorated_upgrade_cost(subscription, package.price_yearly, subscription.company_plan.package.price_yearly)
        price = {
            'month': month if month else 0,
            'year': year if year else 0
        }
        return JsonResponse({'success': True, 'price': price}, status=200)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_clientpayment'])])
@csrf_exempt
def get_clientpayment(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    response = get_records('ClientPayment', json_data, user)
    return JsonResponse(response, status=200)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_provider'])])
@csrf_exempt
def get_providers(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    response = get_records('Provider', json_data, False)
    return JsonResponse(response, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_subscription'])])
@csrf_exempt
def create_payment_intent(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    company = user.company
    
    if json_data.get('is_limit_upgrade', False):
        limit_upgrade = json_data.get('limit_upgrade')
        session = handle_limit_upgrade(company, limit_upgrade)
        return JsonResponse({'success': True, 'url': session.url}, status=200)
    else:
        package_id = json_data.get('package')
        recurring_interval = json_data.get('recurring_interval')
        session = handle_subscription(company, package_id, recurring_interval)

    return JsonResponse({'success': True, 'url': session.url}, status=200)
    
@csrf_exempt
def get_plans(request):
    if request.method!= 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    packages = Package.objects.filter(is_active=True).order_by('tier')
    serializer = PackageSerializer(packages, many=True)
    return JsonResponse({'success': True, 'plans': serializer.data}, status = 200)

@csrf_exempt
def get_conversion_rates(request):
    if request.method!= 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    currency = user.company.company_area.country.currency
    rates = fetch_conversion_rates()
    rate = ConversionRate.objects.filter(currency_to=currency).order_by('-date').first()
    serializer = ConversionRateSerializer(rate)
    return JsonResponse({'success': True, 'rate': serializer.data}, status = 200)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def stripe_webhook(request):
    billing_log = BillingLog.objects.create(process='Webhook received\n', success=False)
    try:
        payload = request.body.decode('utf-8')
        sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')
        billing_log.process += 'Payload decoded and signature header fetched\n'
        billing_log.save()

        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, endpoint_secret
            )
            billing_log.process += f'Event constructed: {event["type"]}\n'
        except ValueError:
            billing_log.process += 'Failed to decode event\n'
            billing_log.save()
            raise Exception('Invalid payload')
        except stripe.error.SignatureVerificationError:
            billing_log.process += 'Signature verification failed\n'
            billing_log.save()
            raise Exception('Invalid signature')

        event_type = event['type']
        data = event['data']['object']
        handled_action = ''
        is_connect = data.get('metadata', {}).get('is_connect', False)
        billing_log.process += f'Event type: {event_type}, is_connect: {is_connect}\n'
        billing_log.save()

        if not is_connect:
            existing_subscription = None
            if event_type in ['invoice.payment_succeeded']:
                existing_subscription = Subscription.objects.filter(stripe_subscription_id=data.get('subscription')).first()
            if not existing_subscription:
                billing_log.delete()
                return JsonResponse({'success': False, 'message': 'Not Connect Response'}, status=200)

        if event_type == 'checkout.session.completed':
            handled_action = handle_checkout_session_completed(data, billing_log)
        elif event_type == 'invoice.payment_succeeded':
            handled_action = handle_invoice_payment_succeeded(data, billing_log)
        elif event_type == 'price.created':
            handled_action = handle_price_created(data, billing_log)
        elif event_type == 'price.deleted':
            handled_action = handle_price_deleted(data, billing_log)
        elif event_type == 'price.updated':
            handled_action = handle_price_updated(data, billing_log)
        else:
            billing_log.process += f'Unhandled event type: {event_type}\n'
            billing_log.save()
            raise Exception(f'Event type not handled {event_type}')

        billing_log.process += f'Action handled: {handled_action}\n'
        billing_log.success = True
        billing_log.save()
        return JsonResponse({'status': 'success', 'handled_action': handled_action}, status=200)
    except ConnectError as e:
        error = e.to_dict()
        billing_log.process += f'ConnectError: {error}\n'
        billing_log.success = False
        billing_log.save()
        return JsonResponse(error, status=200)
    except Exception as e:
        billing_log.process += f'Exception: {str(e)}\n'
        billing_log.success = False
        billing_log.save()
        print(f"Error processing webhook: {e}")
        return JsonResponse({'error': str(e)}, status=200)

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def lahza_webhook(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            return JsonResponse({'error': 'Invalid JSON payload'}, status=400)
        print(json_data)
        event = json_data.get('event', None)
        if event is None:
            return JsonResponse({'error': 'Missing event'}, status=400)
        data = json_data.get('data')
        if event == 'charge.success':
            order_id = data.get('page', {}).get('metadata', {}).get('order_id')
            amount = data.get('page', {}).get('amount')
            first_name = data.get('customer', {}).get('first_name')
            last_name = data.get('customer', {}).get('last_name')
            customer_name = first_name + ' ' + last_name
            handle_client_payment(order_id, amount, customer_name, 'Lahza')
        return JsonResponse({'success': True, 'message': 'Success Payment'}, status=200)
    except Exception as e:
        print(str(e))
        return JsonResponse({'success': False, 'error': str(e)}, status=400)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_limitupgrade'])])
@csrf_exempt
def get_limit_upgrades(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    response_data = get_records('LimitUpgrade', json_data, False)
    return JsonResponse(response_data, status=200)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['cancel_subscription'])])
@csrf_exempt
def cancel_subscription(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    company = user.company
    subscription = get_current_subscription(company)

    if not subscription or not subscription.stripe_subscription_id:
        raise get_error_response('BILLING_802')

    try:
        stripe.Subscription.modify(
            subscription.stripe_subscription_id,
            cancel_at_period_end=True
        )
        subscription.cancelled = True
        subscription.save()
    except stripe.error.StripeError as e:
        raise get_error_response('GENERAL_007', {'error': str(e)})

    return JsonResponse({'success': True, 'message': _('Subscription Cancelled Successfully')}, status=200)
