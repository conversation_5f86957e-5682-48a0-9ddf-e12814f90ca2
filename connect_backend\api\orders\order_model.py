from django.db import models
from ..lookups.lookup_model import Area, Country, SubArea
from ..users.user_model import Company, User, CompanyConf
from ..delivery_company.delivery_company_model import DeliveryCompany
from ..models import SuperModel
from ..collection.collection_model import Collection, ConversionRate
from django.utils.translation import gettext as _
from datetime import date
from django.conf import settings
from django.db import transaction
from ..connection.connection_model import ConnectDeliveryCompany
import requests
from django.utils import timezone
from django.db.utils import IntegrityError
import json
from django.utils.functional import Promise
from django.utils.encoding import force_str

NOTE_INFO_MAX_LENGHT = 5000

def fetch_conversion_rates():
    today = date.today()
    try:
        conversion_rate = ConversionRate.objects.filter(date=today)
        if conversion_rate:
            return conversion_rate
    except ConversionRate.DoesNotExist:
        pass


    currencies = ['JOD', 'ILS']

    for currency in currencies:
        API_URL = settings.CONVERT_RATE_API_URL + '/' + currency

        try:
            response = requests.get(API_URL)
            data = response.json()
            
            if data['result'] != 'success':
                raise Exception("Failed to retrieve conversion rates")
            
            base_currency = data['base_code']
            conversion_rates = data['conversion_rates']

            required_rates = {currency: rate for currency, rate in conversion_rates.items() if currency in currencies}

            conversion_rate_objs = []
            for currency, rate in required_rates.items():
                if currency != base_currency:
                    conversion_rate_objs.append(ConversionRate(
                        currency_from=base_currency,
                        currency_to=currency,
                        rate = rate - 0.04
                    ))

            with transaction.atomic():
                rates = ConversionRate.objects.bulk_create(conversion_rate_objs)
                return rates

        except requests.RequestException as e:
            raise Exception(f"API request failed: {e}")
        except Exception as e:
            raise Exception(f"Error saving conversion rates: {e}")

def convert_lazy(obj):
    if isinstance(obj, Promise):
        return force_str(obj)
    elif isinstance(obj, dict):
        return {k: convert_lazy(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_lazy(i) for i in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_lazy(i) for i in obj)
    return obj

class Status(models.Model):
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=200, unique= True, null=False,  default="new_order")
    hidden = models.BooleanField(default=False)
    show_at_position = models.IntegerField(default=1, null=False, blank=False)

class OrderType(models.Model):
    code = models.CharField(max_length=200, unique= True, null=False)
    name = models.CharField(max_length=200)

class StatusMap(SuperModel):
    delivery_company_status = models.CharField(max_length=200, null=True)
    status_code = models.CharField(max_length=200, null=True)
    description = models.TextField(null=True)

class BranchPage(SuperModel):
    name = models.CharField(max_length=200)
    # ! branch_page_image to be deleted after migration
    branch_page_image = models.ImageField(upload_to='branch_page_images/', null=True, blank=True)
    branch_page_image_url = models.CharField(max_length=200, null=True, blank=True)
    company= models.ForeignKey(Company, on_delete=models.CASCADE, related_name="branch_pages")
    branch_page_commercial_number = models.CharField(max_length=15, null=True, blank=True)

class StuckReason(models.Model):
    reason = models.CharField(max_length=500)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    active = models.BooleanField(default=True)

class Order(SuperModel):
    order_type = models.ForeignKey(OrderType, on_delete=models.CASCADE, default=1)
    parent_order = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='cloned_orders')
    customer_mobile = models.CharField(max_length=15)
    country_code = models.CharField(max_length=15)
    customer_second_mobile = models.CharField(max_length=15,null=True, blank=True)
    customer_name = models.CharField(max_length=200)
    delivery_company = models.ForeignKey(DeliveryCompany,on_delete=models.SET_NULL, null=True)
    address = models.CharField(max_length=200)
    area = models.ForeignKey(Area,on_delete=models.SET_NULL,null=True)
    sub_area = models.ForeignKey(SubArea,on_delete=models.SET_NULL,null=True)
    country = models.ForeignKey(Country,on_delete=models.SET_NULL,null=True)
    delivery_company_status = models.CharField(max_length=200, null=True, blank=True)
    channel = models.CharField(
        max_length=50,
        choices=[
            ("facebook", _("Facebook")),
            ("whatsapp", _("Whatsapp")),
            ("shopify", _("Shopify")),
            ("instagram", _("Instagram")),
            ("woocommerce", _("Woocommerce")),
            ("mystore",_("Mystore")),
            ("developer_integration",_("Developer Integration"))
        ]
    )
    status = models.ForeignKey(Status,on_delete=models.SET_NULL,null=True)
    delivery_fee = models.FloatField(max_length=20, null=True, default=0)
    extra_delivery_fee = models.FloatField(max_length=20, null=True, default=0)
    product_info = models.CharField(max_length=NOTE_INFO_MAX_LENGHT, null=True)
    package_cost = models.FloatField(max_length=20, default=0)
    total_cod = models.FloatField(max_length=20, default=0)
    company = models.ForeignKey(Company,on_delete=models.CASCADE,null=False)
    note = models.CharField(max_length=NOTE_INFO_MAX_LENGHT, null=True, blank=True)
    order_sequence = models.CharField(max_length=15, editable=False)
    olivery_sequence = models.CharField(max_length=20, null=True)
    order_reference = models.CharField(max_length=20, null=True)
    stuck_comment = models.CharField(max_length=500, null=True)
    customer_payment = models.IntegerField(null=True)
    area_mismatch = models.CharField(max_length=200, null=True, blank=True)
    send_failed = models.BooleanField(default=False)
    collection = models.ForeignKey(Collection, on_delete=models.SET_NULL, null=True)
    currency = models.CharField(max_length=200, null=True, blank=True)
    # this field can be populated from Woocommerce, and its value depends on the plugins installed there like (Zelle, greeninvoice-morning)
    payment_method = models.CharField(max_length=100, null=True, blank=True)
    delivery_status_change_date = models.DateField(null=True, blank=True)
    delivery_date = models.DateTimeField(null=True, blank=True)
    return_date = models.DateTimeField(null=True, blank=True)
    branch_page = models.ForeignKey(BranchPage, on_delete=models.SET_NULL, null=True)
    social_message_link = models.CharField(max_length=200, null=True, blank=True)
    paid = models.BooleanField(null=True, blank=True, default=False)
    order_due_amount = models.FloatField(max_length=20, null=True)
    profit = models.FloatField(null=True, blank=True, default=0)
    extra_cost = models.FloatField(null=True, blank=True, default=0)
    ecommerce_version = models.CharField(max_length=25, null=True, blank=True)
    is_online_payment = models.BooleanField(default=False)
    is_fixed_total = models.BooleanField(default=False)
    amount_received = models.FloatField(null=True, blank=True, default=0)
    preparing_date = models.DateTimeField(null=True, blank=True)
    ready_for_delivery_date = models.DateTimeField(null=True, blank=True)
    driver = models.ForeignKey(User, on_delete=models.SET_NULL,blank=True, null=True)
    reschedule_date = models.DateTimeField(null=True, blank=True)
    reject_reason = models.CharField(max_length=500, blank=True, null=True)
    delivery_status = models.ForeignKey(StatusMap, on_delete=models.SET_NULL, null=True, blank=True)
    money_received_date = models.DateTimeField(null=True, blank=True)
    connection = models.ForeignKey(ConnectDeliveryCompany, on_delete=models.SET_NULL, blank=True, null=True)
    store_stuck_comment = models.CharField(max_length=500, null=True)
    discount = models.FloatField(default=0)
    delivered_by = models.CharField(max_length=200, blank=True, null=True)
    tried_to_delivered = models.BooleanField(default=False)
    receipt_date = models.DateField(null=True,blank=True)
    store_status_change_date = models.DateTimeField(null=True,blank=True)
    driver_note = models.CharField(max_length=500, null=True)
    send_to_delivery_date = models.DateField(null=True,blank=True)
    store_stuck_reason = models.ForeignKey(StuckReason, on_delete=models.SET_NULL, null=True, blank=True)
    weight = models.IntegerField(null=True, blank=True, default=0)
    warehouse = models.ForeignKey('Warehouse', null=True, on_delete=models.SET_NULL)
    meta_data = models.JSONField(default=dict)
    partial_delivery_date = models.DateTimeField(null=True, blank=True)

    RELATED_FIELDS =  [
            'delivery_company', 'area', 'sub_area', 'country', 'status', 
            'order_type', 'parent_order', 'collection', 'branch_page', 
            'driver', 'delivery_status', 'store_stuck_reason', 'warehouse',
            'connection', 'connection__delivery_company', 'connection__area',
            'connection__credentials',
        ]
    PREFETCH_RELATED = [
            'order_lines__product',
            'order_lines__product_variant__variant',
            'order_lines__product_variant__variant__product',
            'order_lines__product__prices',
            'cloned_orders'
        ]

    class Meta:
        unique_together = [
            ('order_sequence', 'company'),
            ('company', 'order_reference'),
        ]
        
    def generate_order_sequence(self):
        if not self.pk:
            customer_code = f"{self.company.company_id:03d}"
            if len(customer_code) > 4:
                customer_code = f"{self.company.company_id}"
            last_order = (
                Order.objects
                .filter(company=self.company)
                .order_by('-id')
                .first()
            )
            
            if last_order:
                if last_order.order_sequence.startswith("CO"):
                    try:
                        last_sequence_number = int(last_order.order_sequence[3:])
                    except ValueError:
                        last_sequence_number = 0
                elif last_order.order_sequence.startswith(f"C{customer_code}"):
                    try:
                        if last_order.order_sequence.startswith(f"C{customer_code}T"):
                            last_sequence_number = int(last_order.order_sequence[len(f"C{customer_code}T"):])
                        else:
                            last_sequence_number = int(last_order.order_sequence[len(f"C{customer_code}"):])
                    except ValueError:
                        last_sequence_number = 0
                else:
                    last_sequence_number = 0
            else:
                last_sequence_number = 0
            
            self.order_sequence = f"C{customer_code}T{last_sequence_number + 1:06d}"

    def set_default_order_type(self):
        if not self.order_type:
            try:
                self.order_type = OrderType.objects.get(code='normal')
            except OrderType.DoesNotExist:
                raise ValueError('Order Type "normal" not found')

    def set_currency(self):
        self.currency = self.country.currency

    def set_dates(self):
        if not self.delivery_date and self.delivery_company_status == 'Delivered':
            self.delivery_date = timezone.now()
            for order_line in self.order_lines.all():
                order_line.product_variant.variant.successful_delivery_count +=1
                order_line.product.successful_delivery_count +=1
                order_line.product_variant.variant.save()
                order_line.product.save()
        if not self.return_date and self.delivery_company_status in ['Returned', 'Rejected', 'Stuck']:
            self.return_date = timezone.now()
            for order_line in self.order_lines.all():
                order_line.product_variant.variant.return_attempt_count +=1
                order_line.product.return_attempt_count +=1
                order_line.product_variant.variant.save()
                order_line.product.save()
        if self.id:
            try:
                order_instance = Order.objects.get(id=self.id)
                if self.status != order_instance.status:
                    self.store_status_change_date = timezone.now()
                if self.delivery_company_status != order_instance.delivery_company_status:
                    self.delivery_status_change_date = timezone.now().date()
                    OrderDeliveryStatusHistory.objects.create(
                        order=self,
                        status=self.delivery_status,
                        updated_by=self.updated_by
                    )
            except Order.DoesNotExist:
                pass
        if not self.preparing_date and self.status and self.status.code == 'preparing':
            self.preparing_date = timezone.now()
        if not self.ready_for_delivery_date and self.status and self.status.code == 'ready_for_delivery':
            self.ready_for_delivery_date = timezone.now()
        if not self.tried_to_delivered and self.status and self.status.code in ['with_delivery_company','with_driver']:
            self.send_to_delivery_date = timezone.now().date()
            self.tried_to_delivered = True
            for order_line in self.order_lines.all():
                order_line.product_variant.variant.delivery_attempt_count +=1
                order_line.product.delivery_attempt_count +=1
                order_line.product_variant.variant.save()
                order_line.product.save()
        if not self.partial_delivery_date and self.delivery_status and self.delivery_status.status_code == 'delivered_partial':
            self.partial_delivery_date = timezone.now()

    def calculate_order_due_amount(self):
        self.order_due_amount = float(self.total_cod) - float(self.delivery_fee)

    def convert_currency(self, amount, currency_from, currency_to):
        if currency_from != currency_to:
            fetch_conversion_rates()
            rate = ConversionRate.objects.filter(
                currency_from=currency_from,
                currency_to=currency_to
            ).order_by('-date').first()
            return float(amount) * float(rate.rate) if rate else amount
        return amount

    def calculate_total_profit(self):
        company_currency = self.company.company_area.country.currency

        package_cost = float(self.convert_currency(self.package_cost, self.currency, company_currency))
        delivery_fee = float(self.convert_currency(self.delivery_fee, self.currency, company_currency))
        extra_delivery_fee = float(self.convert_currency(self.extra_delivery_fee, self.currency, company_currency))
        total_cod = float(self.convert_currency(self.total_cod, self.currency, company_currency))
        extra_cost = float(self.convert_currency(self.extra_cost, self.currency, company_currency))
        products_cost = float(sum(
            float(order_line.product.cost) * float(order_line.quantity)
            for order_line in self.order_lines.all()
        ))

        if self.paid:
            self.profit = package_cost + extra_cost - (products_cost + delivery_fee + extra_delivery_fee)
        else:
            self.profit = total_cod - products_cost - delivery_fee - extra_delivery_fee

    def should_recalculate_profit(self):
        fields_to_track = ['total_cod', 'package_cost', 'delivery_fee', 'extra_delivery_fee', 'extra_cost', 'currency']
        if not self.pk:
            return True
        old_instance = Order.objects.get(id=self.id)
        if any(getattr(old_instance, field) != getattr(self, field) for field in fields_to_track):
            return True
        return False
    
    def handle_country_code(self):
        if not self.country_code or self.country_code == 'TEMP':
            country_code = self.country.mobile_intro
        else:
            country_code = self.country_code
        if '|' in country_code:
            country_code = country_code.split('|')[0]
        self.country_code = country_code

    def is_paid(self):
        if self.paid:
            self.total_cod = 0

    def set_delivery_status(self):
        if self.delivery_company_status:
            try:
                self.delivery_status = StatusMap.objects.get(delivery_company_status=self.delivery_company_status)
            except StatusMap.DoesNotExist:
                pass

    def set_store_stuck_reason(self):
        if self.store_stuck_comment:
            try:
                self.store_stuck_reason = StuckReason.objects.filter(reason=self.store_stuck_comment, company=self.company, active=True).first()
            except StuckReason.DoesNotExist:
                self.store_stuck_reason = None
    
    def set_default_warehouse(self):
        if not self.warehouse:
            conf = CompanyConf.objects.get(company=self.company)
            self.warehouse = conf.default_warehouse
            
    def send_to_webhook(self):
        try:
            from ..developer_connection.developer_connection_model import DeveloperConnectionWebhook, DeveloperOrder, WebhookLog
            from ..serializers import StatusMapSerializer
            developer_order = DeveloperOrder.objects.filter(order=self).first()
            developer_webhooks = DeveloperConnectionWebhook.objects.filter(developer_connection__company=self.company, apply_to_all_orders=True, is_active=True)
            if developer_order:
                webhooks = DeveloperConnectionWebhook.objects.filter(developer_connection=developer_order.developer_connection, is_active=True)
                developer_webhooks = developer_webhooks | webhooks
            for webhook in developer_webhooks:
                webhook_log = WebhookLog.objects.create(webhook=webhook)
                try:
                    payload = {
                        'order_sequence': self.order_sequence,
                        'delivery_status': StatusMapSerializer(self.delivery_status).data,
                        'delivered_date': self.delivery_date.isoformat() if self.delivery_date else None,
                        'total_cod': self.total_cod,
                        'customer_payment': self.customer_payment,
                    }
                    if self.delivery_date:
                        if self.delivered_by:
                            payload['delivered_by'] = self.delivered_by
                        elif self.delivery_company:
                            payload['delivered_by'] = self.delivery_company.name if self.delivery_date else ''
                        elif self.driver:
                            payload['delivered_by'] = self.driver.name if self.delivery_date else ''
                        else:
                            payload['delivered_by'] = ''
                    payload = convert_lazy(payload)
                    webhook_log.request_body = payload
                    webhook_log.request_headers = webhook.headers
                    webhook_log.save()
                    try:
                        response = requests.post(webhook.url, json=payload, headers=webhook.headers)
                    except requests.RequestException as e:
                        webhook_log.status_code = 500
                        webhook_log.response_body = {'error': str(e)}
                        webhook_log.save()
                        continue
                    webhook_log.response_body = response.json()
                    webhook_log.status_code = response.status_code
                    webhook_log.save()
                except Exception as e:
                    webhook_log.status_code = 500
                    webhook_log.response_body = {'error': str(e)}
                    webhook_log.save()
                    continue
        except Exception as e:
            print(f"Error sending to webhook: {e}")
    
    def should_send_webhook(self):
        try:
            order_instance = Order.objects.get(id=self.id)
            return self.delivery_status and self.delivery_status != order_instance.delivery_status
        except Exception:
            return False

    def save(self, *args, **kwargs):
        self.handle_country_code()
        self.set_default_order_type()
        self.set_currency()
        self.set_delivery_status()
        self.set_store_stuck_reason()
        self.set_dates()
        self.is_paid()
        self.calculate_order_due_amount()
        self.set_default_warehouse()
        if self.should_recalculate_profit():
            self.calculate_total_profit()
        if self.should_send_webhook():
            self.send_to_webhook()
        attempts = 3
        for _ in range(attempts):
            try:
                with transaction.atomic():
                    self.generate_order_sequence()
                    super(Order, self).save(*args, **kwargs)
                break
            except IntegrityError:
                if _ == attempts - 1:
                    raise

class VhubStatus(models.Model):
    status = models.ForeignKey(Status, on_delete=models.CASCADE)
    vhub_status = models.CharField(max_length=200)

class VhubFieldMap(SuperModel):
    field = models.CharField(max_length=200)
    vhub_field = models.CharField(max_length=200)
    status = models.ForeignKey(StatusMap, on_delete=models.SET_NULL ,null=True, blank=True)
    is_foreign_key = models.BooleanField(default=False)
    company= models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True)

class VhubForeignKeyMap(SuperModel):
    field = models.CharField(max_length=200)
    model= models.CharField(max_length=200)
    model_key = models.CharField(max_length=200)
    model_value = models.CharField(max_length=200)

class ClientPayment(SuperModel):
    amount = models.DecimalField(decimal_places=2, max_digits=12)
    currency = models.CharField(max_length=10)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    order = models.ForeignKey(Order, on_delete=models.SET_NULL, null=True, related_name='client_payment')
    customer_name = models.CharField(max_length=255, null=True, blank=True)
    provider = models.CharField(max_length=255, null=True, blank=True)

from django.db import models

class OrderDeliveryStatusHistory(models.Model):
    order = models.ForeignKey("Order", related_name="previous_states", on_delete=models.CASCADE)
    status = models.ForeignKey(StatusMap, on_delete=models.SET_NULL, null=True)
    changed_at = models.DateTimeField(auto_now_add=True)
    updated_by = models.CharField(max_length=200, null=True)
