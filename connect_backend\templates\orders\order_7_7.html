<!DOCTYPE html>
{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_7_7.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}

    {% for doc in docs %}
    <div class="waybill-container page-break">
        <div class="header">
            <div class="business-logo">
                <img src="{{doc.company_logo}}" class="business-logo">
                {% if doc.company_registry %}
                <div class="text-center fw-bold">{{doc.company_registry}}</div>
                {% endif %}
            </div>
            <div class="date-time fw-bold">
                {{meta.formatted_now}}
            </div>
        </div>
        <div class="">
            <div class="left-cards-container">
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.RECIPIENT}}
                    </div>
                    <div>
                        <div class="info-block border-b-grey">
                            <span class="text-truncate">
                                {{doc.order.customer_name}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span class="">
                                {{doc.order.customer_mobile}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <div class="mb-2">
                                {{doc.order.area.name}},
                                {{doc.order.address}}
                            </div>
                        </div>
                        <div class="info-block">
                            <span class="text-truncate">
                                {{meta.labels.CREATOR}}: {{doc.order.created_by}}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card overflow-hidden">
                    <div class="card-header">
                        {{meta.labels.NOTES}}
                    </div>
                    <div class="notes-content overflow-hidden">
                        <span>{{doc.order.note[:100] if doc.order.note is not none else ""}}</span>
                    </div>
                </div>

                {% if doc.order.connection and doc.order.connection.delivery_company %}
                <div class="mt-1">
                    {{meta.labels.DELIVERY_COMPANY_NAME}}:
                    {{doc.order.connection.delivery_company.name}}
                </div>
                {% endif %}
            </div>
            <div class="right-cards-container">
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.PRODUCTS}}
                    </div>

                    <div class="products-container">
                        {% if doc.order_lines and doc.order_lines|length > 0 %}
                        <table class="o-table">
                            {% for item in doc.order_lines[:4] %}
                            <tr>
                                <td class="cell-truncate border-b-grey">
                                    {{item.product.reference_sequence if meta.print_product_sequence else item.product_variant.variant.name}}
                                </td>
                                <td class="border-b-grey">
                                    X{{item.quantity}}
                                </td>
                                <td class="border-b-grey">
                                    {{item.price}}
                                </td>
                            </tr>
                            {% endfor %}
                            {% if doc.order_lines|length >= 5  %}
                            <tr>
                                <td colspan="3" class="">
                                    <div class="more-icon"></div>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                        <div class="info-block total-row">
                            <span>
                                {{meta.labels.TOTAL}}:
                            </span>
                            <span>
                                {{doc.total}}
                            </span>
                        </div>
                        {% else %}
                        {{doc.order.product_info.replace('\n', '<br>')}}
                        {% endif %}
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.COD}}
                    </div>
                    <div class="fs-lg fw-bold">
                        {{doc.order.total_cod}}
                    </div>
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-around w-100">
            <div class="header-barcode-container">
                {{meta.labels.STORE_SEQUENCE}}
                <img class="barcode" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}"
                    alt="">
            </div>
            <div class="header-barcode-container">
                {{meta.labels.DELIVERY_SEQUENCE}}
                <img class="barcode" src="data:image/png;base64,{{ doc.barcode_olivery_base64 }}"
                    alt="">
            </div>
        </div>
    </div>
    {% endfor %}
</body>

</html>