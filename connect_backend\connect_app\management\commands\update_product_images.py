from django.core.management.base import BaseCommand
from api.products.product_model import Product, ProductsImage

class Command(BaseCommand):
    help = 'Update currency for orders based on customer mobile number or company currency'

    def handle(self, *args, **kwargs):
        products = Product.objects.all()

        for product in products:
            image = ProductsImage.objects.create(product=product, image=product.product_image)
            self.stdout.write(self.style.SUCCESS(f"Updated Product {product.id} with image {image.id}"))
