from django.urls import path
from . import user_apis

urlpatterns = [
    path('add_user', user_apis.add_user, name='add_user'),
    path('get_users', user_apis.get_users, name='get_users'),
    path('get_roles', user_apis.get_roles, name='get_roles'),
    path('get_conf', user_apis.get_conf, name='get_conf'),
    path('set_conf', user_apis.set_conf, name='set_conf'),
    path('update_user', user_apis.update_user, name='update_user'),
    path('generate_link', user_apis.generate_link, name='generate_link'),
    path('user_info', user_apis.get_user_info, name='user_info'),
    path('company_info', user_apis.get_company_info, name='company_info'),
    path('delete_user', user_apis.delete_user, name='delete_user'),
    path('update_company', user_apis.update_company, name='update_company'),
    path('get_system_configuration', user_apis.get_system_configuration, name='get_system_configuration'),
    path('set_user_preferences', user_apis.set_user_preferences, name='set_user_preferences'),
    path('get_user_preferences', user_apis.get_user_preferences, name='get_user_preferences'),
    path('reset_user_password', user_apis.reset_user_password, name='reset_user_password'),
    path('get_company_wallet', user_apis.get_company_wallet, name='get_company_wallet'),
    path('get_drivers', user_apis.get_drivers, name='get_drivers'),
]