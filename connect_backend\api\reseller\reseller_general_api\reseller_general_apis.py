from api.reseller.models import ResellerJWTAuthentication
from api.util_functions import *
from api.auth.auth_utils import generate_JWT
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from  api.error.error_model import *
from django.utils.translation import gettext as _
from django.db.models import Count,Q


@csrf_exempt
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
def read_group(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    user = request.user
    
    model = json_data.get('model')
    if not model:
        raise get_error_response('GENERAL_002', {'fields': 'model'})
    
    model_class = apps.get_model('api', model)
    if model_class is None:
        raise get_error_response('GENERAL_002', {'model': model})
    
    group_by = json_data.get('group_by', [])
    if not group_by:
        raise get_error_response('GENERAL_002', {'fields': 'group_by'})
            
    level = int(json_data.get('level'))
    filters = json_data.get('filters', [])
    search = json_data.get('search')
    q_filters = build_filters(filters)
    values = json_data.get('group_values', [])
    foreign_key_serializer = None

    if level >= len(group_by):
        raise get_error_response('GENERAL_005', {})

    dynamic_filters = q_filters
    search_filter = get_search_filter(search, model_class)
    if model == 'Order':
        dynamic_filters &= Q(warehouse__reseller=user)
    elif 'company' in [field.name for field in model_class._meta.get_fields()]:
        dynamic_filters &= Q(company__company_id__in=user.companies.exclude(company__isnull=True).values_list('company_id', flat=True))
    dynamic_filters &= search_filter

    for i in range(level):
        group_field = group_by[i]
        value = values[i]

        if group_field == "created_at":
            date_value = datetime.datetime.strptime(value, "%Y-%m-%d").date()
            start_of_day = datetime.datetime.combine(date_value, datetime.time.min)
            end_of_day = datetime.datetime.combine(date_value, datetime.time.max)
            dynamic_filters &= Q(created_at__range=(start_of_day, end_of_day))
        else:
            dynamic_filters &= Q(**{f"{group_field}__exact": value})

    current_group = group_by[level]
    field = model_class._meta.get_field(current_group)

    if isinstance(field, models.DateTimeField):
        results = (
            model_class.objects.filter(dynamic_filters).distinct()
            .annotate(group_date=TruncDate(current_group))
            .values("group_date")
            .annotate(count=Count("id", distinct=True))
            .order_by("-group_date")
        )
        group_field = "group_date"
    else:
        results = (
            model_class.objects.filter(dynamic_filters).distinct()
            .values(current_group)
            .annotate(count=Count("id", distinct=True))
            .order_by(current_group)
        )
        group_field = current_group

    foreign_key_serializer = None
    if field.is_relation and field.related_model:
        foreign_key_serializer = MODEL_SERIALIZER_MAP.get(field.related_model)

    serialized_results = []
    summary = {}
    for result in results:
        group_key = result[group_field]
        
        if isinstance(field, models.DateTimeField):
            filter_key = {
                "field": f"{current_group}__date",
                "operator": "exact",
                "value": group_key.strftime("%Y-%m-%d"),
            }
        else:
            filter_key = {
                "field": current_group,
                "operator": "exact",
                "value": group_key,
            }
        if model == 'Order':
            fields = {}
            
            if len(fields.keys()):
                filter_item = build_single_filter(filter_key)
                queryset = model_class.objects.filter(dynamic_filters).filter(filter_item).distinct()
                currency_aggregates = queryset.values('id', 'currency').annotate(**{
                    f'{sum_field}': Sum(sum_field, distinct=True) for sum_field in fields
                })
                summary = {key: Decimal(0) for key in fields.keys()}
                company_currency = user.country.currency

                for aggregate in currency_aggregates:
                    order_currency = aggregate['currency']
                    conversion_rate = get_conversion_rate(order_currency, company_currency)

                    for key in fields.keys():
                        value = Decimal(aggregate.get(key, 0))
                        converted_value = value * conversion_rate
                        summary[key] += round_two_decimals(converted_value)
                if summary.get('extra_delivery_fee', 0):
                    summary['delivery_fee'] += summary['extra_delivery_fee']
        if foreign_key_serializer and isinstance(group_key, int):
            foreign_instance = field.related_model.objects.get(pk=group_key)
            serialized_group = foreign_key_serializer(foreign_instance).data
        else:
            serialized_group = _(group_key) if group_key and isinstance(group_key, str) else group_key

        serialized_results.append({
            "group": serialized_group,
            "count": result["count"],
            "filter": filter_key,
            "sum_fields": summary
        })
    
    response_data = {
        "level": level,
        "group_by": current_group,
        "results": serialized_results,
    }

    return JsonResponse({"success": True, "data": response_data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
def impersonate_user(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    company = json_data.get('company')

    if not company:
        return JsonResponse({'success':False, 'error': 'company is required'}, status=400)
    
    try:
        comapny = Company.objects.get(company_id=company['company_id'])
    except Company.DoesNotExist:
        return JsonResponse({'success':False, 'error': 'Company not found'}, status=404)
    owner = comapny.owner
    if not owner:
        return JsonResponse({'success':False, 'error': 'Owner not found'}, status=400)
    if not owner.user:
        return JsonResponse({'success':False, 'error': 'User is not activated user and should be sent the invitation link to set password'}, status=400)
    
    try:
        token = generate_JWT(owner.user,connect_user_id=owner.id, impersonated_by=request.user.name)
        return JsonResponse({'success':True, 'token': token}, status=200)
    except Exception as e:
        return JsonResponse({'success':False, 'error': f'Failed to generate JWT: {str(e)}'}, status=500)
    