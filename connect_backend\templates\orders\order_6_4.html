<!DOCTYPE html>
{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_6_4.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}

    {% for doc in docs %}
    <div class="page-break">
        <div class="waybill-header">
            <img class="barcode-img" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}"
                alt="">
        </div>
        {% if doc.company_registry %}
            <div class="text-center fw-bold mb-2">{{ doc.company_name }}  -  {{ doc.company_registry }}</div>
        {% else %}
            <div class="text-center fw-bold mb-2">{{ doc.company_name }}</div>
        {% endif %}
        <table class="bordered-table w-100">
            <tr>
                <td>
                    {{meta.labels.RECIPIENT}}
                </td>
                <td>
                    {{doc.order.customer_name}}
                </td>
            </tr>
            <tr>
                <td>
                    {{meta.labels.MOBILE}}: 
                </td>
                <td>
                    {{doc.order.customer_mobile}}
                </td>
            </tr>
            <tr>
                <td>
                    {{meta.labels.ADDRESS}}:
                </td>
                <td class="">
                    <span class="oneline" style="max-width: 13rem;">
                        {{doc.order.area.name}},
                        {{doc.order.sub_area.name}},
                        {{doc.order.address}}
                    </span>
                </td>
            </tr>
            <tr>
                <td>
                    {{meta.labels.COD_SHORT}}:
                </td>
                <td>
                    {{doc.order.total_cod}}
                </td>
            </tr>
            {% if doc.order.note%}
            <tr>
                <td colspan="2">
                    <span class="oneline" style="max-width: 20rem;">{{doc.order.note if doc.order.note is not none else ""}}</span>
                </td>
            </tr>
            {% endif %}
        </table>
    </div>
    {% endfor %}
</body>

</html>