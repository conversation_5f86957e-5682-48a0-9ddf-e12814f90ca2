from django.db import models
from ..users.user_model import Company
from ..orders.order_model import StatusMap

class DelayedOrderFilters(models.Model):
    delivery_status = models.TextField()
    days_delayed = models.IntegerField(default=2)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

    def get_delivery_statuses(self):
        delivery_statuses = StatusMap.objects.filter(status_code__in=self.delivery_status.split(','))
        return delivery_statuses

    def set_delivery_statuses(self, statuses_list):
        self.delivery_status = ','.join(statuses_list)