# Standard library imports
import logging
import re

# Django imports
from django.db import models
from django.db.models import F, Value, FloatField
from django.db.models.functions import Greatest
from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank, TrigramSimilarity

# Local imports
from ..products.product_model import Product, ProductVariant

logger = logging.getLogger(__name__)


class ApiProducts(Product):
    """
    Proxy model for Product to handle public API search functionality.
    
    This class extends the Product model to provide specialized search methods
    for the public API without modifying the main Product model. It includes
    advanced search capabilities such as PostgreSQL full-text search, fuzzy matching,
    and Arabic language support.
    """
    
    class Meta:
        proxy = True
    
    @classmethod
    def get_best_match(cls, query, company_id, warehouse_id=None):
        """
        Get the single best matching product using PostgreSQL full-text search.
        
        This method performs a comprehensive search across product names, descriptions,
        variants, SKUs, and sequence numbers. It uses a multi-stage approach:
        1. First tries exact match (case insensitive)
        2. Then uses PostgreSQL full-text search with weighted ranking
        3. Falls back to icontains search if necessary
        
        When warehouse_id is provided, only products with variants in the specified
        warehouse will be considered in the search results.
        
        Args:
            query (str): Search query string to find matching products
            company_id (int): Company ID to filter products by company
            warehouse_id (int, optional): Warehouse ID to filter product variants by warehouse
            
        Returns:
            Product: Single best matching product or None if no match found
        """
        if not query or not query.strip() or not company_id:
            return None
        
        # Build base filter for all queries
        base_filter = {
            'active': True,
            'publish': True,
            'company_id': company_id
        }
        
        # If warehouse_id is provided, filter products that have variants in this warehouse
        if warehouse_id:
            from ..warehouse.warehouse_model import WarehouseVariant
            # Get products that have variants in the specified warehouse
            warehouse_products = WarehouseVariant.objects.filter(
                warehouse_id=warehouse_id
            ).values_list('variant__product_id', flat=True).distinct()
            
            # Add the warehouse filter
            base_filter['id__in'] = warehouse_products
            
        # First try exact match (case insensitive)
        exact_match = cls.objects.filter(
            models.Q(name__iexact=query) | 
            models.Q(variants__name__iexact=query) |
            models.Q(variants__sku__iexact=query),
            **base_filter
        ).select_related('category').prefetch_related(
            'images', 'variants', 'variants__attributes', 'tags'
        ).distinct().first()
        
        if exact_match:
            return exact_match
            
        # Try PostgreSQL full-text search if available
        try:
            search_vector = (
                SearchVector('name', weight='A') + 
                SearchVector('description', weight='B') +
                SearchVector('variants__name', weight='B') +
                SearchVector('variants__sku', weight='C') +
                SearchVector('product_sequence', weight='C') +
                SearchVector('reference_sequence', weight='C')
            )
            search_query = SearchQuery(query)
            
            best_match = cls.objects.annotate(
                search=search_vector,
                rank=SearchRank(search_vector, search_query)
            ).filter(
                search=search_query,
                **base_filter
            ).select_related('category').prefetch_related(
                'images', 'variants', 'variants__attributes', 'tags'
            ).order_by('-rank', '-successful_delivery_count').first()
            
            if best_match:
                return best_match
                
        except Exception as e:
            logger.warning(f"Full-text search not available: {e}")
            
        # Fallback to icontains search with comprehensive variant support
        icontains_match = cls.objects.filter(
            models.Q(name__icontains=query) | 
            models.Q(description__icontains=query) |
            models.Q(variants__name__icontains=query) |
            models.Q(variants__sku__icontains=query) |
            models.Q(product_sequence__icontains=query) |
            models.Q(reference_sequence__icontains=query),
            **base_filter
        ).select_related('category').prefetch_related(
            'images', 'variants', 'variants__attributes', 'tags'
        ).order_by('name', '-successful_delivery_count').distinct().first()
        
        return icontains_match

    @classmethod
    def get_best_match_fuzzy(cls, query, company_id, warehouse_id=None):
        """
        Get best match using multiple fuzzy matching strategies with comprehensive variant support.
        
        This method implements a sophisticated multi-stage fuzzy matching algorithm:
        1. First tries exact match (case insensitive)
        2. Then tries startswith matching for product names and variants
        3. Performs contains matching with variant attributes
        4. Uses PostgreSQL trigram similarity for fuzzy matching
        5. Falls back to character-by-character matching for non-Latin scripts
        
        Args:
            query (str): Search query string to find matching products
            company_id (int): Company ID to filter products by company
            warehouse_id (int, optional): Warehouse ID to filter product variants by warehouse
            
        Returns:
            Product: Best matching product or None if no good match found
        """
        if not query or not query.strip() or not company_id:
            return None
            
        # Build base filter for all queries
        base_filter = {
            'active': True,
            'publish': True,
            'company_id': company_id
        }
        
        # If warehouse_id is provided, filter products that have variants in this warehouse
        if warehouse_id:
            from ..warehouse.warehouse_model import WarehouseVariant
            # Get products that have variants in the specified warehouse
            warehouse_products = WarehouseVariant.objects.filter(
                warehouse_id=warehouse_id
            ).values_list('variant__product_id', flat=True).distinct()
            
            # Add the warehouse filter
            base_filter['id__in'] = warehouse_products
        
        # First try exact match (case insensitive)
        exact_match = cls.objects.filter(
            models.Q(name__iexact=query) | 
            models.Q(variants__name__iexact=query) |
            models.Q(variants__sku__iexact=query),
            **base_filter
        ).select_related('category').prefetch_related(
            'images', 'variants', 'variants__attributes', 'tags'
        ).distinct().first()
        
        if exact_match:
            return exact_match
            
        # Then try startswith for name (product or variant)
        startswith_match = cls.objects.filter(
            models.Q(name__istartswith=query) | 
            models.Q(variants__name__istartswith=query) |
            models.Q(variants__sku__istartswith=query),
            **base_filter
        ).select_related('category').prefetch_related(
            'images', 'variants', 'variants__attributes', 'tags'
        ).order_by('name').distinct().first()
        
        if startswith_match:
            return startswith_match
            
        # Try contains match with all relevant fields including variant attributes
        try:
            contains_match = cls.objects.filter(
                models.Q(name__icontains=query) | 
                models.Q(description__icontains=query) |
                models.Q(variants__name__icontains=query) |
                models.Q(variants__sku__icontains=query) |
                models.Q(variants__attributes__value__value__icontains=query) |
                models.Q(product_sequence__icontains=query) |
                models.Q(reference_sequence__icontains=query),
                **base_filter
            ).select_related('category').prefetch_related(
                'images', 'variants', 'variants__attributes', 'tags'
            ).order_by('name', '-successful_delivery_count').distinct().first()
        except Exception as e:
            logger.warning(f"Attribute search failed, trying without attributes: {e}")
            # Fallback without attribute search if field lookup fails
            contains_match = cls.objects.filter(
                models.Q(name__icontains=query) | 
                models.Q(description__icontains=query) |
                models.Q(variants__name__icontains=query) |
                models.Q(variants__sku__icontains=query) |
                models.Q(product_sequence__icontains=query) |
                models.Q(reference_sequence__icontains=query),
                **base_filter
            ).select_related('category').prefetch_related(
                'images', 'variants', 'variants__attributes', 'tags'
            ).order_by('name', '-successful_delivery_count').distinct().first()
        
        if contains_match:
            return contains_match
            
        # Try character-by-character fuzzy matching using trigram similarity if available
        try:
            similarity_threshold = 0.3
            
            fuzzy_match = cls.objects.annotate(
                similarity=Greatest(
                    TrigramSimilarity('name', query),
                    TrigramSimilarity('variants__name', query),
                    TrigramSimilarity('variants__sku', query),
                    TrigramSimilarity('description', query)
                )
            ).filter(
                similarity__gt=similarity_threshold,
                **base_filter
            ).select_related('category').prefetch_related(
                'images', 'variants', 'variants__attributes', 'tags'
            ).order_by('-similarity', '-successful_delivery_count').distinct().first()
            
            if fuzzy_match:
                return fuzzy_match
                
        except Exception as e:
            logger.warning(f"Trigram similarity not available: {e}")
            
        # Final fallback: try word-by-word matching for multi-word queries
        query_words = query.strip().split()
        if len(query_words) > 1:
            word_conditions = models.Q()
            for word in query_words:
                if len(word.strip()) > 1:  # Skip single characters
                    word_conditions |= models.Q(name__icontains=word.strip())
                    word_conditions |= models.Q(variants__name__icontains=word.strip())
                    word_conditions |= models.Q(description__icontains=word.strip())
                    # Try to include attribute search, but fallback gracefully if it fails
                    try:
                        word_conditions |= models.Q(variants__attributes__value__value__icontains=word.strip())
                    except Exception:
                        pass  # Skip attribute search if field lookup fails
            
            try:
                word_match = cls.objects.filter(
                    word_conditions,
                    active=True,
                    publish=True,
                    company_id=company_id
                ).select_related('category').prefetch_related(
                    'images', 'variants', 'variants__attributes', 'tags'
                ).order_by('name', '-successful_delivery_count').distinct().first()
                if word_match:
                    return word_match
            except Exception as e:
                logger.warning(f"Word-by-word search failed: {e}")
        
        return None

    @classmethod
    def get_character_match(cls, query, company_id, warehouse_id=None):
        """
        Advanced character-by-character matching for Arabic and Unicode text.
        
        This method provides the most comprehensive fallback when other methods fail.
        It's especially effective for Arabic and other non-Latin scripts where
        standard PostgreSQL text search may not perform optimally. The algorithm:
        1. Normalizes both the query and product text for better matching
        2. Performs character-by-character similarity calculation
        3. Handles Arabic text with special consideration for character variations
        4. Applies a scoring system that accounts for partial matches
        
        Args:
            query (str): Search query string to find matching products
            company_id (int): Company ID to filter products by company
            warehouse_id (int, optional): Warehouse ID to filter product variants by warehouse
            
        Returns:
            Product: Best matching product or None if no match found
        """
        if not query or not query.strip() or not company_id:
            return None
            
        # Build base filter for all queries
        base_filter = {
            'active': True,
            'publish': True,
            'company_id': company_id
        }
        
        # If warehouse_id is provided, filter products that have variants in this warehouse
        if warehouse_id:
            from ..warehouse.warehouse_model import WarehouseVariant
            # Get products that have variants in the specified warehouse
            warehouse_products = WarehouseVariant.objects.filter(
                warehouse_id=warehouse_id
            ).values_list('variant__product_id', flat=True).distinct()
            
            # Add the warehouse filter
            base_filter['id__in'] = warehouse_products
        
        # Get all products for the company with optional warehouse filtering
        all_products = cls.objects.filter(
            **base_filter
        ).select_related('category').prefetch_related(
            'images', 'variants', 'variants__attributes', 'tags'
        )
        normalized_query = cls._normalize_search_term(query).lower()
        
        # Extract potential size information (like XXL, XL, etc.)
        size_pattern = r'\b(xxs|xs|s|m|l|xl|xxl|xxxl|[0-9]+)\b'
        size_matches = re.findall(size_pattern, normalized_query.lower())
        size_info = size_matches[0] if size_matches else None
        
        # Remove size info from query for better base product matching
        base_query = re.sub(size_pattern, '', normalized_query).strip()
        
        best_match = None
        best_score = 0
        best_variant_match = None
        best_variant_score = 0
        
        # First pass: Check for exact matches on product variants with size
        if size_info:
            for product in all_products:
                for variant in product.variants.all():
                    # Check if variant name or attributes contain the size
                    variant_text = variant.name.lower() if variant.name else ''
                    
                    # Check variant attributes for size
                    for attr in variant.attributes.all():
                        if attr.value and attr.value.value:
                            attr_value = attr.value.value.lower()
                            if size_info.lower() == attr_value or size_info.lower() in attr_value:
                                # If size matches, check if base product name matches
                                product_score = cls._calculate_character_similarity(base_query, product.name.lower())
                                if product_score > 0.4:  # Good base product match with exact size match
                                    return product
        
        # Second pass: Comprehensive character matching
        for product in all_products:
            # Check product name
            score = cls._calculate_character_similarity(normalized_query, product.name.lower())
            if score > best_score:
                best_score = score
                best_match = product
                
            # Check product description
            if product.description:
                score = cls._calculate_character_similarity(normalized_query, product.description.lower())
                if score > best_score:
                    best_score = score
                    best_match = product
            
            # Check variants with higher weight for Arabic text
            for variant in product.variants.all():
                # Check variant name
                if variant.name:
                    # For Arabic text, give higher weight to variant matches
                    is_arabic = cls._is_arabic_text(normalized_query)
                    variant_score = cls._calculate_character_similarity(normalized_query, variant.name.lower())
                    if is_arabic:
                        variant_score *= 1.2  # 20% boost for Arabic variant matches
                    
                    if variant_score > best_variant_score:
                        best_variant_score = variant_score
                        best_variant_match = product
                    
                    if variant_score > best_score:
                        best_score = variant_score
                        best_match = product
                
                # Check variant SKU
                if variant.sku:
                    sku_score = cls._calculate_character_similarity(normalized_query, variant.sku.lower())
                    if sku_score > best_score:
                        best_score = sku_score
                        best_match = product
                
                # Check variant attributes with special handling for sizes
                for attr in variant.attributes.all():
                    if attr.value and attr.value.value:
                        attr_value = attr.value.value.lower()
                        attr_score = cls._calculate_character_similarity(normalized_query, attr_value)
                        
                        # Special boost for size matches
                        if size_info and (size_info.lower() == attr_value or size_info.lower() in attr_value):
                            attr_score = max(attr_score, 0.7)  # Strong boost for size match
                            
                        if attr_score > best_score:
                            best_score = attr_score
                            best_match = product
        
        # Return best variant match if it's good enough
        if best_variant_score > 0.4 and best_variant_match:
            return best_variant_match
            
        # Return match if similarity is above threshold
        if best_score > 0.25:  # Lower threshold to ensure we find something
            return best_match
            
        # If we still have no match but have products, return the first one
        # This ensures we always return something when products exist
        if all_products.exists():
            return all_products.first()
            
        return None
    @classmethod
    def _calculate_character_similarity(cls, query, text):
        """
        Calculate character-based similarity between two strings with Arabic language optimization.
        
        This method implements a specialized similarity algorithm that works exceptionally
        well with Arabic and other Unicode text. It normalizes both strings, removes spaces,
        and performs character-by-character matching with special handling for Arabic
        character variations.
        
        The algorithm calculates partial matches and weights longer matching sequences
        higher, which is particularly effective for product name matching where
        order and completeness matter.
        
        Args:
            query (str): Search query string to compare
            text (str): Text string to compare against
            
        Returns:
            float: Similarity score between 0.0 (no match) and 1.0 (perfect match)
        """
        if not query or not text:
            return 0.0
            
        # Normalize both strings for Arabic
        query = cls._normalize_arabic_text(query).lower().replace(' ', '')
        text = cls._normalize_arabic_text(text).lower().replace(' ', '')
        
        if query == text:
            return 1.0
            
        # Character-based matching with Arabic letter variants
        query_chars = set(query)
        text_chars = set(text)
        
        # Apply Arabic letter equivalency for better matching
        query_chars_normalized = cls._get_equivalent_arabic_chars(query_chars)
        text_chars_normalized = cls._get_equivalent_arabic_chars(text_chars)
        
        # Calculate Jaccard similarity (intersection over union)
        intersection = len(query_chars_normalized.intersection(text_chars_normalized))
        union = len(query_chars_normalized.union(text_chars_normalized))
        
        if union == 0:
            return 0.0
            
        jaccard_score = intersection / union
        
        # Bonus for exact substring matches (higher weight for Arabic)
        substring_bonus = 0.0
        if query in text or text in query:
            substring_bonus = 0.4 if cls._is_arabic_text(query) else 0.3
        elif any(word in text for word in query.split() if len(word) > 2):
            substring_bonus = 0.3 if cls._is_arabic_text(query) else 0.2
            
        # Bonus for similar length (more important for Arabic due to root structure)
        length_similarity = 1.0 - abs(len(query) - len(text)) / max(len(query), len(text))
        length_bonus = length_similarity * (0.15 if cls._is_arabic_text(query) else 0.1)
        
        # Arabic-specific root similarity bonus
        root_bonus = 0.0
        if cls._is_arabic_text(query) and cls._is_arabic_text(text):
            root_bonus = cls._calculate_arabic_root_similarity(query, text)
        
        return min(1.0, jaccard_score + substring_bonus + length_bonus + root_bonus)

    @classmethod
    def get_guaranteed_best_match(cls, query, company_id, warehouse_id=None):
        """
        Get the guaranteed best match product with detailed match information.
        
        This is the primary method used by the public API best match endpoint.
        It implements a comprehensive search strategy that guarantees a result
        whenever products exist in the system. The method returns both the
        best matching product and detailed information about the match quality.
        
        The matching algorithm uses multiple strategies:
        1. Tries PostgreSQL full-text search first
        2. Falls back to fuzzy matching if needed
        3. Uses character-by-character matching for Arabic and non-Latin scripts
        4. Extracts and handles size information separately for better matching
        5. Provides confidence scores and match type information
        
        When a warehouse_id is provided, the search will ensure that product variants
        exist in the specified warehouse. This is useful for filtering products
        that are available in a specific warehouse location.
        
        Args:
            query (str): Search query string to find matching products
            company_id (int): Company ID to filter products by company
            warehouse_id (int, optional): Warehouse ID to filter product variants by warehouse
            
        Returns:
            tuple: (Product, dict) - Product object and match information dictionary
                The match information includes match_type, confidence score,
                search term used, and reason for the match selection.
        """
        if not query or not company_id:
            # Base filter for active products
            product_filter = {
                'active': True, 
                'publish': True, 
                'company_id': company_id
            }
            
            # If warehouse_id is provided, filter products that have variants in this warehouse
            if warehouse_id:
                from ..warehouse.warehouse_model import WarehouseVariant
                # Get variants that exist in the specified warehouse
                warehouse_variants = WarehouseVariant.objects.filter(
                    warehouse_id=warehouse_id,
                    variant__product__active=True,
                    variant__product__publish=True,
                    variant__product__company_id=company_id
                ).values_list('variant__product_id', flat=True).distinct()
                
                # Add the warehouse filter
                product_filter['id__in'] = warehouse_variants
            
            # Check if any products exist with the applied filters
            any_product = cls.objects.filter(**product_filter).first()
            
            if any_product:
                return any_product, {
                    'match_type': 'fallback',
                    'confidence': 0.1,
                    'reason': 'Empty query',
                    'search_term': '',
                    'warehouse_filtered': bool(warehouse_id)
                }
            return None, {
                'match_type': 'no_products',
                'confidence': 0,
                'reason': 'No products available' + (' in specified warehouse' if warehouse_id else ''),
                'search_term': ''
            }
        
        # Normalize the search term with special handling for Arabic text
        normalized_query = cls._normalize_search_term(query)
        
        # Check if query is Arabic to apply special handling
        is_arabic = cls._is_arabic_text(normalized_query)
        
        # Try exact match first
        best_match = cls.get_best_match(normalized_query, company_id, warehouse_id)
        if best_match:
            confidence = 0.95 if is_arabic else 0.9  # Higher confidence for Arabic exact matches
            return best_match, {
                'match_type': 'full_text',
                'confidence': confidence,
                'search_term': normalized_query,
                'is_arabic': is_arabic
            }
            
        # For Arabic queries, try character matching before fuzzy matching
        # as it's optimized for Arabic character equivalence
        if is_arabic:
            character_match = cls.get_character_match(normalized_query, company_id, warehouse_id)
            if character_match:
                return character_match, {
                    'match_type': 'arabic_character',
                    'confidence': 0.8,  # Higher confidence for Arabic character matching
                    'search_term': normalized_query,
                    'is_arabic': True
                }
        
        # Try fuzzy matching if exact match fails
        fuzzy_match = cls.get_best_match_fuzzy(normalized_query, company_id, warehouse_id)
        if fuzzy_match:
            confidence = 0.75 if is_arabic else 0.7
            return fuzzy_match, {
                'match_type': 'fuzzy',
                'confidence': confidence,
                'search_term': normalized_query,
                'is_arabic': is_arabic
            }
            
        # For non-Arabic queries or if Arabic character matching failed
        if not is_arabic or not best_match:
            character_match = cls.get_character_match(normalized_query, company_id)
            if character_match:
                return character_match, {
                    'match_type': 'character',
                    'confidence': 0.6,
                    'search_term': normalized_query,
                    'is_arabic': is_arabic
                }
        
        # Check for products with similar variants, especially for size-related queries
        # This is particularly useful for queries like "هودي جنزاري xxL"
        # Check variant size matches
        size_pattern = r'\b(xxs|xs|s|m|l|xl|xxl|xxxl|[0-9]+)\b'
        size_matches = re.findall(size_pattern, normalized_query.lower())
        size_info = size_matches[0] if size_matches else None
        
        
        if size_info:
            base_query = re.sub(size_pattern, '', normalized_query).strip()
            all_products = cls.objects.filter(
                models.Q(variants__name__icontains=size_info) | 
                models.Q(variants__attributes__value__value__icontains=size_info),
                active=True, publish=True, company_id=company_id
            ).select_related('category').prefetch_related(
                'images', 'variants', 'variants__attributes', 'tags'
            ).distinct()
            
            if all_products.exists():
                best_product = None
                best_score = 0
                
                for product in all_products:
                    score = cls._calculate_character_similarity(base_query, product.name.lower())
                    if score > best_score:
                        best_score = score
                        best_product = product
                
                if best_product and best_score > 0.3:
                    return best_product, {
                        'match_type': 'size_variant_match',
                        'confidence': best_score,
                        'is_arabic': is_arabic,
                        'size_match': size_info,
                        'search_term': normalized_query
                    }
                elif all_products.first():  # Fallback to first product with matching size
                    return all_products.first(), {
                        'match_type': 'size_variant_fallback',
                        'confidence': 0.3,
                        'is_arabic': is_arabic,
                        'size_match': size_info,
                        'search_term': normalized_query
                    }
        
        # Final fallback: return any product
        # First try to find a product with a name containing any word from the query
        query_words = normalized_query.split()
        if len(query_words) > 0:
            for word in query_words:
                if len(word) > 2:  # Only use words with more than 2 characters
                    word_match = cls.objects.filter(
                        models.Q(name__icontains=word) | 
                        models.Q(variants__name__icontains=word),
                        active=True,
                        publish=True,
                        company_id=company_id
                    ).order_by('-successful_delivery_count').first()
                    
                    if word_match:
                        return word_match, {
                            'match_type': 'word_match_fallback',
                            'confidence': 0.3,
                            'search_term': normalized_query,
                            'matched_word': word,
                            'is_arabic': is_arabic
                        }
        
        # Absolute final fallback: any product
        fallback_product = cls.objects.filter(
            active=True,
            publish=True,
            company_id=company_id
        ).order_by('-successful_delivery_count').first()
        
        if fallback_product:
            return fallback_product, {
                'match_type': 'fallback',
                'confidence': 0.1,
                'reason': 'No matches found, returning popular product',
                'search_term': normalized_query,
                'is_arabic': is_arabic
            }
            
        # If no products exist at all
        return None, {
            'match_type': 'no_products',
            'confidence': 0,
            'reason': 'No products available for this company',
            'search_term': normalized_query,
            'is_arabic': is_arabic
        }

    @classmethod
    def _normalize_search_term(cls, query):
        """
        Normalize search terms for better matching with Arabic language support.
        
        This method performs comprehensive text normalization to improve search quality:
        1. Applies Arabic-specific character normalization
        2. Converts to lowercase and strips whitespace
        3. Removes extra spaces between words
        4. Handles common abbreviations and word forms in both English and Arabic
        5. Standardizes product terminology to improve match rates
        
        Args:
            query (str): Raw search query string from the user
            
        Returns:
            str: Normalized search query optimized for product matching
        """
        if not query:
            return ""
            
        # First apply Arabic-specific normalization
        normalized = cls._normalize_arabic_text(query)
        
        # Convert to lowercase
        normalized = normalized.lower().strip()
        
        # Remove extra spaces
        normalized = " ".join(normalized.split())
        
        # Handle common abbreviations and word forms (Arabic and English)
        replacements = {
            # English abbreviations
            "t-shirt": "tshirt", "t shirt": "tshirt",
            "smartphone": "phone", "cell phone": "phone",
            "laptop computer": "laptop",
            # Arabic common replacements
            "تي شيرت": "تيشيرت", "تي-شيرت": "تيشيرت",
            "هاتف ذكي": "هاتف", "جهاز كمبيوتر": "كمبيوتر",
            "حاسوب محمول": "لابتوب"
        }
        
        for old, new in replacements.items():
            if old in normalized:
                normalized = normalized.replace(old, new)
                
        return normalized

    @classmethod
    def _normalize_arabic_text(cls, text):
        """
        Normalize Arabic text by handling different forms of characters.
        
        This method provides specialized normalization for Arabic text to improve search quality.
        It handles the complexities of Arabic script including:
        1. Normalizing different forms of Alef (أ, إ, آ → ا)
        2. Standardizing Yeh variations (ى, ي)
        3. Converting Teh Marbuta to Heh (ة → ه)
        4. Normalizing Waw variations
        5. Removing diacritical marks (harakat)
        6. Handling other special Arabic characters
        
        This normalization is critical for effective Arabic text search as it reduces
        character variations that are semantically equivalent but syntactically different.
        
        Args:
            text (str): Arabic or mixed text to normalize
            
        Returns:
            str: Normalized text with standardized Arabic characters
        """
        if not text:
            return ""
            
        # Convert to string if not already
        text = str(text)
        
        # Common Arabic letter normalizations
        replacements = {
            # Alef variations
            '\u0622': '\u0627',  # Alef with madda above → Alef
            '\u0623': '\u0627',  # Alef with hamza above → Alef
            '\u0625': '\u0627',  # Alef with hamza below → Alef
            '\u0671': '\u0627',  # Alef Wasla → Alef
            # Yeh variations
            '\u064a': '\u064a',  # Arabic Yeh
            '\u06cc': '\u064a',  # Farsi Yeh → Arabic Yeh
            '\u0649': '\u064a',  # Alef Maksura → Yeh
            # Teh variations
            '\u0629': '\u0647',  # Teh Marbuta → Heh
            # Waw variations
            '\u0624': '\u0648',  # Waw with Hamza → Waw
            # Remove diacritics (harakat)
            '\u064b': '',  # Fathatan
            '\u064c': '',  # Dammatan
            '\u064d': '',  # Kasratan
            '\u064e': '',  # Fatha
            '\u064f': '',  # Damma
            '\u0650': '',  # Kasra
            '\u0651': '',  # Shadda
            '\u0652': '',  # Sukun
            '\u0653': '',  # Maddah
            '\u0654': '',  # Hamza above
            '\u0655': '',  # Hamza below
            '\u0670': ''   # Superscript Alef
        }
        
        # Apply replacements
        for old, new in replacements.items():
            text = text.replace(old, new)
            
        return text
    
    @classmethod
    def _get_equivalent_arabic_chars(cls, char_set):
        """
        Convert a set of characters to include equivalent Arabic characters.
        
        This method expands a set of characters to include all equivalent forms
        in Arabic. This is particularly important for search as Arabic has many
        character variations that are semantically equivalent but have different
        Unicode code points.
        
        The method handles several equivalence groups:
        1. Alef variations (ا, آ, أ, إ, ٱ)
        2. Yeh variations (ي, ی, ى)
        3. Teh Marbuta and Heh (ة, ه)
        4. Waw variations (و, ؤ)
        
        This expansion improves matching by treating all equivalent forms as the same
        character, which is essential for effective Arabic text search.
        
        Args:
            char_set (set): Original set of characters to expand
            
        Returns:
            set: Expanded set containing all equivalent Arabic characters
        """
        result = set(char_set)  # Create a copy
        
        # Define character equivalence groups
        equivalence_groups = [
            # Alef group
            {'\u0627', '\u0622', '\u0623', '\u0625', '\u0671'},
            # Yeh group
            {'\u064a', '\u06cc', '\u0649'},
            # Teh and Heh group
            {'\u0629', '\u0647'},
            # Waw group
            {'\u0648', '\u0624'}
        ]
        
        # For each character in the original set, add all its equivalents
        for char in list(char_set):
            for group in equivalence_groups:
                if char in group:
                    result.update(group)
                    break
                    
        return result
    
    @classmethod
    def _is_arabic_text(cls, text):
        """
        Check if text contains primarily Arabic characters.
        
        This method determines if a given text string contains a significant proportion
        of Arabic characters. It's used to apply specialized Arabic text handling
        algorithms when appropriate. The method:
        1. Counts characters in the Arabic Unicode blocks (؀-ۿ, ݐ-ݿ)
        2. Calculates the ratio of Arabic characters to total text length
        3. Returns True if at least 30% of characters are Arabic
        
        This threshold approach handles mixed text (containing both Arabic and Latin
        characters) appropriately.
        
        Args:
            text (str): Text string to analyze for Arabic character content
            
        Returns:
            bool: True if text is primarily Arabic (≥30% Arabic characters)
        """
        if not text:
            return False
            
        # Arabic Unicode block range
        arabic_count = sum(1 for c in text if '\u0600' <= c <= '\u06ff' or '\u0750' <= c <= '\u077f')
        
        # Consider text Arabic if at least 30% of characters are Arabic
        return arabic_count / len(text) >= 0.3
    
    @classmethod
    def _calculate_arabic_root_similarity(cls, text1, text2):
        """
        Calculate similarity based on the Arabic root system.
        
        This method implements a specialized algorithm for Arabic text similarity
        that takes into account the unique root-based structure of Arabic words.
        In Arabic, words are typically derived from 3-4 consonant roots, and
        this method attempts to identify and compare these roots.
        
        The algorithm:
        1. Extracts consonants from both texts (excluding vowels like alef, waw, yeh)
        2. Takes the first 3-4 consonants as potential word roots
        3. Compares these potential roots for similarity
        4. Returns a scaled similarity score as a bonus for the main similarity calculation
        
        This approach is particularly effective for Arabic product names where
        the root consonants carry the core meaning of the word.
        
        Args:
            text1 (str): First Arabic text to compare
            text2 (str): Second Arabic text to compare
            
        Returns:
            float: Root similarity score between 0.0 (no similarity) and 0.2 (high similarity)
        """
        # Extract potential root consonants (typically 3-4 letters)
        # This is a simplified approach focusing on consonants
        
        # Arabic consonants (excluding vowels alef, waw, yeh)
        consonants = set('بتثجحخدذرزسشصضطظعغفقكلمنه')
        
        # Extract consonants from both texts
        text1_consonants = [c for c in text1 if c in consonants]
        text2_consonants = [c for c in text2 if c in consonants]
        
        # If either text has no consonants, return 0
        if not text1_consonants or not text2_consonants:
            return 0.0
            
        # Take first 3-4 consonants as potential root
        root1 = text1_consonants[:min(4, len(text1_consonants))]
        root2 = text2_consonants[:min(4, len(text2_consonants))]
        
        # Calculate similarity
        matches = sum(1 for a, b in zip(root1, root2) if a == b)
        max_possible = min(len(root1), len(root2))
        
        if max_possible == 0:
            return 0.0
            
        # Scale to 0.0-0.2 range (as it's a bonus)
        return 0.2 * (matches / max_possible)
