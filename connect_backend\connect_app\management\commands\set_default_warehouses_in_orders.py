from django.core.management.base import BaseCommand
from api.orders.order_model import Order
from api.products.product_model import ProductVariant
from api.users.user_model import User
from collections import defaultdict
import time
from api.util_functions import get_company_conf

class Command(BaseCommand):
    def __init__(self):
        super().__init__()
        self.last_percent_printed = {}

    def handle(self, *args, **kwargs):
        start_time = time.time()
        
        # Get all orders (initial count)
        orders = Order.objects.select_related('company').filter(warehouse__isnull=True)
        total_orders = orders.count()

        variants = ProductVariant.objects.select_related('product__company').filter(warehouses__isnull=True)
        total_variants = variants.count()

        if not total_orders and not total_variants:
            self.stdout.write("No orders or variants to process.")
            return

        self.stdout.write(f"Processing {total_orders} orders...")
        
        company_orders = defaultdict(list)
        for i, order in enumerate(orders, 1):
            company_orders[order.company.company_id].append(order)
            self._print_progress(i, total_orders, "Grouping orders")
        
        total_to_process = sum(len(orders) for orders in company_orders.values())
        processed = 0
    
        for company_id, orders in company_orders.items():
            conf = get_company_conf(company=company_id)
            if conf:
                default_warehouse = conf.default_warehouse
                for order in orders:
                    order.warehouse = default_warehouse
                Order.objects.bulk_update(orders, ['warehouse'])
                processed += len(orders)
                self._print_progress(processed, total_to_process, "Updating orders")

        company_variants = defaultdict(list)
        for i, variant in enumerate(variants, 1):
            company_id = variant.product.company.company_id
            company_variants[company_id].append(variant)
            self._print_progress(i, total_variants, "Grouping variants")

        total_to_process_variants = sum(len(v) for v in company_variants.values())
        processed_variants = 0
        for company_id, variant_list in company_variants.items():
            conf = get_company_conf(company=company_id)
            if conf:
                default_warehouse = conf.default_warehouse
                for variant in variant_list:
                    variant.warehouses.set([default_warehouse])
                    processed_variants += 1
                    self._print_progress(processed_variants, total_to_process_variants, "Updating variants")


        elapsed_time = round(time.time() - start_time, 2)
        self.stdout.write(f"Completed in {elapsed_time} seconds.")

    def _print_progress(self, current, total, stage):
        percent = int((current / total) * 100)
        last_printed = self.last_percent_printed.get(stage, -5)

        if percent - last_printed >= 5 or percent == 100:
            self.stdout.write(f"{stage}: {current}/{total} ({percent}%)")
            self.stdout.flush()
            self.last_percent_printed[stage] = percent
