from django.urls import path
from .billing_apis import *

urlpatterns = [
    path('get_order_count_and_limit', get_order_count_and_limit, name='get_order_count_and_limit'),
    path('create_payment_intent', create_payment_intent, name='create_payment_intent'),
    path('get_plans', get_plans, name='get_plans'),
    path('stripe_webhook', stripe_webhook, name='stripe_webhook'),
    path('lahza_webhook', lahza_webhook, name='lahza_webhook'),
    path('current_plan', current_plan, name='current_plan'),
    path('get_prorated_charge', get_prorated_charge, name='get_prorated_charge'),
    path('get_conversion_rates', get_conversion_rates, name='get_conversion_rates'),
    path('get_clientpayment', get_clientpayment, name='get_clientpayment'),
    path('get_providers', get_providers, name='get_providers'),
    path('get_limit_upgrades', get_limit_upgrades, name='get_limit_upgrades'),
    path('cancel_subscription', cancel_subscription, name='cancel_subscription'),

]