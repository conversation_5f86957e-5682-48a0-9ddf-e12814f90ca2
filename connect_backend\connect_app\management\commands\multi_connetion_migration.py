from django.core.management.base import BaseCommand
from api.connection.connection_model import ConnectDeliveryCompany, ConnectionUser
from api.orders.order_model import Order
from api.users.user_model import CompanyConf
from api.pricelists.pricelist_model import Pricelist
from api.error.error_model import *
from api.util_functions import send_api_to_delivery_company

class Command(BaseCommand):
    help = 'Set variants in order lines where the product has at least one variant'

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.SUCCESS("Starting the command..."))

        connections = ConnectDeliveryCompany.objects.all()
        self.stdout.write(self.style.SUCCESS(f"Found {connections.count()} connections."))

        for connection in connections:
            if connection.connection_status != "pending":
                try:
                    connection_user = ConnectionUser.objects.get(
                        company=connection.company,
                        delivery_company=connection.delivery_company
                    )
                except ConnectionUser.DoesNotExist:
                    self.stdout.write(self.style.WARNING(
                        f"ConnectionUser does not exist for company {connection.company.company_name} - {connection.company.company_id} "
                        f"and delivery company {connection.delivery_company.name} - {connection.delivery_company.id} with status {connection.connection_status}. Skipping..."
                    ))
                    continue
                except ConnectionUser.MultipleObjectsReturned:
                    self.stdout.write(self.style.WARNING(
                        f"Multiple ConnectionUser does not exist for company {connection.company.company_name} - {connection.company.company_id} "
                        f"and delivery company {connection.delivery_company.name} - {connection.delivery_company.id} with status {connection.connection_status}. Skipping..."
                    ))
                    continue

                connection_user.connection = connection
                connection_user.save()
                params = {
                    "jsonrpc": "2.0",
                    "params": {
                        "db": connection.delivery_company.delivery_company_db,
                        "login": connection_user.company_username,
                        "password": connection_user.company_password,
                        'values':{
                            'connection_id': connection.id,
                            'connect_request_flag': True
                        }
                    }
                }
                try:
                    response = send_api_to_delivery_company(connection.delivery_company, '/connection/edit_connection', params)
                    self.stdout.write(self.style.SUCCESS(response))
                except Exception as e:
                    self.stdout.write(self.style.WARNING(str(e)))
                    continue
            connection.save()

        orders = Order.objects.all()
        total_orders = orders.count()
        self.stdout.write(self.style.SUCCESS(f"Found {total_orders} orders."))

        if total_orders == 0:
            self.stdout.write(self.style.WARNING("No orders to process. Exiting..."))
            return

        last_reported_progress = 0

        for index, order in enumerate(orders, start=1):
            if order.delivery_company:
                try:
                    connection = ConnectDeliveryCompany.objects.get(
                        company=order.company,
                        delivery_company=order.delivery_company
                    )
                except ConnectDeliveryCompany.DoesNotExist:
                    self.stdout.write(self.style.WARNING(
                        f"No connection found for order {order.id} "
                        f"(company: {order.company}, delivery company: {order.delivery_company}). Skipping..."
                    ))
                    continue
                except ConnectDeliveryCompany.MultipleObjectsReturned:
                    continue

                order.connection = connection
                order.save()

            progress = (index / total_orders) * 100
            if progress - last_reported_progress >= 5:  # Only print every 5% increment
                self.stdout.write(self.style.SUCCESS(f"Processed {progress:.0f}% of orders"))
                last_reported_progress = progress

        confs = CompanyConf.objects.all()
        total_confs = confs.count()
        self.stdout.write(self.style.SUCCESS(f"Found {total_confs} confs."))

        if total_confs == 0:
            self.stdout.write(self.style.WARNING("No confs to process. Exiting..."))
            return

        last_reported_progress = 0

        for index, conf in enumerate(confs, start=1):
            if conf.default_delivery_company:
                try:
                    connection = ConnectDeliveryCompany.objects.filter(
                        company=conf.company,
                        delivery_company=conf.default_delivery_company
                    ).first()
                except ConnectDeliveryCompany.DoesNotExist:
                    self.stdout.write(self.style.WARNING(
                        f"No connection found for conf {conf.id} "
                        f"(conf: {conf.company}, delivery company: {conf.default_delivery_company}). Skipping..."
                    ))
                    continue

                conf.default_connection = connection
                conf.save()

            progress = (index / total_confs) * 100
            if progress - last_reported_progress >= 5:  # Only print every 5% increment
                self.stdout.write(self.style.SUCCESS(f"Processed {progress:.0f}% of confs"))
                last_reported_progress = progress

        pricelists = Pricelist.objects.filter(delivery_company__isnull=False)
        total_pricelists = pricelists.count()
        self.stdout.write(self.style.SUCCESS(f"Found {total_pricelists} pricelists."))

        if total_pricelists == 0:
            self.stdout.write(self.style.WARNING("No pricelists to process. Exiting..."))
            return

        last_reported_progress = 0

        for index, pricelist in enumerate(pricelists, start=1):
            try:
                connection = ConnectDeliveryCompany.objects.filter(
                    company=pricelist.company,
                    delivery_company=pricelist.delivery_company
                ).first()
            except ConnectDeliveryCompany.DoesNotExist:
                self.stdout.write(self.style.WARNING(
                    f"No connection found for conf {conf.id} "
                    f"(conf: {conf.company}, delivery company: {conf.default_delivery_company}). Skipping..."
                ))
                continue

            pricelist.connection = connection
            pricelist.save()

            progress = (index / total_pricelists) * 100
            if progress - last_reported_progress >= 5:  # Only print every 5% increment
                self.stdout.write(self.style.SUCCESS(f"Processed {progress:.0f}% of pricelists"))
                last_reported_progress = progress

        self.stdout.write(self.style.SUCCESS("Command execution completed."))
