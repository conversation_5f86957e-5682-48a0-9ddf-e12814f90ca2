:root {
    --black: #1A1818;
    --grey: #ECEBEB;
    --white: #ffffff;
}

@font-face {
    font-family: Myriadpro;
    src: url("Myriadpro-font/MYRIADPRO-REGULAR.OTF") format("opentype");
}

@font-face {
    font-family: Myriadpro;
    font-weight: bold;
    src: url("Myriadpro-font/MYRIADPRO-BOLD.OTF") format("opentype");
}

@media print {
    .page-break {
        display: block;
        page-break-before: always;
    }
}

body,
html {
    font-size: 7.5pt;
    font-family: Myriadpro;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    color: var(--black)
}

div {
    box-sizing: border-box;
}

.text-truncate {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 0; /* Necessary for text truncation in WeasyPrint */
    flex-grow: 1; /* Allow the item to grow in flex container */
    min-width: 0; /* Necessary for text truncation in a flex container */
    max-width: 100%;
}

.fs-sm {
    font-size: 0.8rem;
}

.fs-lg {
    font-size: 1.65rem;
}

.fs-xlg {
    font-size: 2rem;
}

.fs-xxlg {
    font-size: 3rem;
}

.fw-bold {
    font-weight: 600;
}

.pb-1 {
    padding-bottom: .25rem;
}

.mt-1 {
    margin-top: .25rem;
}

.mt-2 {
    margin-top: .5rem;
}

.mt-3 {
    margin-top: .75rem;
}

.mt-4 {
    margin-top: 1rem;
}

.mt-5 {
    margin-top: 1.5rem;
}

.mb-1 {
    margin-bottom: .25rem;
}

.mb-2 {
    margin-bottom: .5rem;
}

.mb-3 {
    margin-bottom: .75rem;
}

.mb-4 {
    margin-bottom: 1rem;
}

.mb-5 {
    margin-bottom: 1.5rem;
}

.d-flex {
    display: flex;
}

.justify-content-around {
    justify-content: space-around;
}

.justify-content-between {
    justify-content: space-between;
}

.flex-column {
    flex-direction: column;
}

.more-icon {
    text-align: center;
}

.more-icon::before {
    content: '.....';
    transform: rotate(90deg);
    display: inline-block;
}

.info-block {
    padding: .5rem;
    margin-top: .3rem;
    display: flex;
    justify-content: space-between;
}

.o-table {
    border-collapse: separate;
    width: 100%;
}

.o-table th {
    white-space: nowrap;
}

.border-b-grey {
    border-bottom: solid 1px grey;
}

.need-filling {
    display: flex;
    margin-top: 1.8rem;
}

.dots {
    flex-grow: 1;
    border-bottom: dotted 1px;
    border-color: var(--black);
    /* margin-left: 2rem; */
    margin-right: 2rem;
}

.barcode {
    width: 100%;
    height: 100%;
}

.business-logo {
    display: inline-block;
    border-radius: .34rem;
    max-height: 3.39rem;
    max-width: 12.42rem;
}

.overflow-hidden {
    overflow: hidden;
}

.text-center {
    text-align: center;
}

.text-end {
    text-align: end;
}

.w-100 {
    width: 100% !important;
}

.m-auto {
    margin: auto;
}

.bordered-table, .bordered-table th, .bordered-table td {
    border: 1px solid black;
    border-collapse: collapse;
}

.position-relative {
    position: relative;
}

.page-number-container {
    vertical-align: bottom;
    display: inline-block;
    overflow: hidden;
    position: absolute;
    right: 0px;
    bottom: 0px;
}
