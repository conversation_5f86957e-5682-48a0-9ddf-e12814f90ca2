<!DOCTYPE html>
{% if meta.lang == 'ar' %}
<html lang='ar' dir="rtl">
{% else %}
<html lang='en' dir="ltr">
{% endif %}
<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_delivery_a6.css">
</head>
{% if meta.lang == 'ar' %}
<body class="rtl">
{% else %}
<body class="ltr">
{% endif %}
    {% for doc in docs %}
    <div class="d-flex align-items-center padding-10 page-break">
        <div class="flex-1-3">
            <label class="company_name">{{doc.company_name}}</label>
        </div>
        <div class="text-center flex-2-3">
            <img class="barcode-img" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}" alt="">
        </div> 
    </div>

    <div class="table-wrapper">
        <table class="table-condensed order-table">
            <tr>
                <td colspan="2">
                    {{meta.labels.TOTAL_COD_AMOUNT}} <span>{{doc.order.total_cod}}</span>
                </td>
            </tr>
            <tr>
                <td>
                    {{meta.labels.COD}}:<br/>{{meta.labels.DELIVERY_FEE}}:
                </td>
                <td>
                    <span>{{doc.order.total_cod}}</span> <br/>
                    <span>{{doc.order.delivery_fee}}</span>
                </td>
            </tr>
            {% if doc.order.weight != 0 %}
                <tr>
                    <td colspan="2">
                        {{meta.labels.WEIGHT}}: <span>{{ doc.order.weight }}</span>
                    </td>
                </tr>
            {% endif %}
            <tr>
                <td colspan="2">
                    {{meta.labels.ORDER_TYPE}}: {{doc.order.order_type.name}}
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    {{meta.labels.NOTES}}: <span>{{ doc.order.note }}</span>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    {{meta.labels.RECIPIENT}}: <span>{{doc.order.customer_name}}</span><br/>
                    {{meta.labels.ADDRESS}}: <span>{{doc.order.address}}</span><br/>
                    {{meta.labels.MOBILE}}: <span>{{doc.order.customer_mobile}}</span>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    {{meta.labels.COMPANY_NAME}}: <span>{{doc.company_name}}</span><br/>
                    {{meta.labels.MOBILE}}: <span>{{doc.company.company_mobile}}</span>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    {{meta.labels.REFERENCE}} #: <span>{{doc.order.order_sequence}}</span>
                </td>
            </tr>
            <tr>
                <td>
                    {{meta.labels.CITY}}: <span>{{doc.order.area.name}}</span>
                </td>
                <td>
                    {{meta.labels.AREA}}: <span>{{doc.order.sub_area.name}}</span>
                </td>
            </tr>
        </table>
    </div>

    <div class="delivery-container">
       <div class="delivery-barcode-container">
           <img class="barcode-img" src="data:image/png;base64,{{ doc.barcode_olivery_base64 }}" alt="">
       </div> 
       <div class="delivery-logo-container">
           <img class="delivery-logo" src="{{doc.delivery_company_logo}}" alt="">
       </div>
    </div>
    {% endfor %}
</body>
</html>
