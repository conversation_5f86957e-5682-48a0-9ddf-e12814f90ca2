from ..models import *
from ..users.user_model import Company
from ..orders.order_model import Order
from ..lookups.lookup_model import Country
from ..category.category_model import *

class Tags(SuperModel):
    name = models.CharField(max_length=50)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=False)

    class Meta:
        unique_together = ('name', 'company')

class Attribute(SuperModel):
    name = models.CharField(max_length=200, unique=True)

class AttributeValue(SuperModel):
    attribute = models.ForeignKey(Attribute, on_delete=models.CASCADE)
    value = models.CharField(max_length=200)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

class Product(SuperModel):
    name = models.CharField(max_length=200)
    cost = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    description = models.CharField(max_length=500, null=True, blank=True)
    product_sequence = models.CharField(max_length=15, null=True, blank=True)
    reference_sequence = models.CharField(max_length=100, null=True, blank=True)
    publish = models.BooleanField(default=True)
    active = models.BooleanField(default=True)
    tags = models.ManyToManyField(Tags, related_name='products')
    category = models.ForeignKey(Category, on_delete=models.SET_NULL , null=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=False)
    virtual_stock = models.IntegerField(default=0)
    physical_stock = models.IntegerField(default=0)
    reserved_stock = models.IntegerField(default=0)
    delivery_attempt_count = models.IntegerField(default=0) 
    successful_delivery_count = models.IntegerField(default=0)
    return_attempt_count = models.IntegerField(default=0)
    

    def save(self, *args, **kwargs):
        if self.pk:
            original = Product.objects.get(pk=self.pk)
            if original.name != self.name:
                variants = self.variants.all()
                for variant in variants:
                    variant.name = variant.name.replace(original.name, self.name)
                    variant.save()
        if not self.product_sequence:
            if not self.pk:
                last_product = Product.objects.filter(company=self.company).order_by('-product_sequence').first()
                if last_product:
                    last_sequence_number = int(last_product.product_sequence.split("-")[1])
                else:
                    last_sequence_number = 1000000
                self.product_sequence = f"P-{last_sequence_number + 1}"
        if self.physical_stock is not None and self.virtual_stock is not None:
            self.reserved_stock = self.physical_stock - self.virtual_stock
        super(Product, self).save(*args, **kwargs)
        
    def delete(self):
        self.active = False
        self.save()

    def restore(self):
        self.active = True
        self.save()
class ProductPrice(models.Model):
    product = models.ForeignKey(Product, related_name='prices', on_delete=models.CASCADE)
    country = models.ForeignKey(Country, on_delete=models.CASCADE, null=False)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    
    class Meta:
        unique_together = ('product', 'country')

class ProductVariant(SuperModel):
    product = models.ForeignKey(Product, related_name='variants', on_delete=models.CASCADE)
    name = models.CharField(max_length=500)
    sku = models.CharField(max_length=200)
    # ! image to be deleted after migration
    variant_image = models.ImageField(upload_to='product_images/',null=True,blank=True)
    variant_image_url = models.CharField(max_length=200, null=True, blank=True)
    delivery_attempt_count = models.IntegerField(default=0) 
    successful_delivery_count = models.IntegerField(default=0)
    return_attempt_count = models.IntegerField(default=0)
    warehouses = models.ManyToManyField('Warehouse')

    def save(self, *args, **kwargs):
        attribute_values = self.attributes.all().values_list('value__value', flat=True)
        self.name = f"{self.product.name}-{'-'.join(attribute_values)}" if attribute_values else self.product.name
        super(ProductVariant, self).save(*args, **kwargs)
    
class VariantAttribute(SuperModel):
    variant = models.ForeignKey(ProductVariant, related_name='attributes', on_delete=models.CASCADE)
    value = models.ForeignKey(AttributeValue, on_delete=models.CASCADE)
    
    def save(self, *args, **kwargs):
        super(VariantAttribute, self).save(*args, **kwargs)
        self.variant.save()

class OrderLine(SuperModel):
    order = models.ForeignKey(Order, related_name='order_lines', on_delete=models.CASCADE)
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    quantity = models.PositiveIntegerField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    product_variant = models.ForeignKey('WarehouseVariant', on_delete=models.SET_NULL, null=True)
    virtual_deducted = models.IntegerField(null=True, blank=True, default=0)
    physical_deducted = models.IntegerField(null=True, blank=True, default=0)

    def save(self, *args, **kwargs):
        super(OrderLine, self).save(*args, **kwargs)
        self.order.calculate_total_profit()
        self.order.save()

class ProductsImage(models.Model):
    product = models.ForeignKey(Product, on_delete=models.CASCADE,related_name='images')
    # ! image to be deleted after migration
    image = models.ImageField(upload_to='product_images/',null=True,blank=True)
    product_image_url = models.CharField(max_length=200, null=True, blank=True)