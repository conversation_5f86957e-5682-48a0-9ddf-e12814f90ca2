from django.db import models
from django.utils import timezone
from ..models import SuperModel

class Country(SuperModel):
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=50)
    mobile_intro = models.CharField(max_length=10)
    country_flag = models.CharField(max_length=10,null=True)
    currency = models.CharField(max_length=20, null=True)
    mobile_placeholder = models.CharField(max_length=50, null=True)
    mobile_number_length = models.IntegerField(default=10)
    
class AreaGroup(SuperModel):
    code = models.CharField(max_length=50)
    name = models.CharField(max_length=50)
    country = models.ForeignKey(Country, on_delete=models.CASCADE)
    
class Area(SuperModel):
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=50, null=True)
    country = models.ForeignKey(Country,on_delete=models.CASCADE, related_name='areas')
    area_group = models.ForeignKey(AreaGroup, on_delete=models.SET_NULL, null=True)

    def save(self, *args, **kwargs):
        is_new = not self.pk
        super(Area, self).save(*args, **kwargs)
        
        if not self.code:
            self.code = self.id
            Area.objects.filter(pk=self.pk).update(code=self.code)

class SubArea(SuperModel):
    name = models.CharField(max_length=50)
    code = models.CharField(max_length=50, null=True)
    area = models.ForeignKey(Area,on_delete=models.CASCADE, related_name='subareas')
    is_default_for_area = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        is_new = not self.pk
        super(SubArea, self).save(*args, **kwargs)
        if not self.code:
            self.code = self.id
            SubArea.objects.filter(pk=self.pk).update(code=self.code)

class FuzzyArea(SuperModel):
    area_name_arabic = models.CharField(max_length=255, unique=True)
    area_name_english = models.CharField(max_length=255, null=True, blank=True)
    area_name_hebrew = models.CharField(max_length=255, null=True, blank=True)

class FuzzySubArea(SuperModel):
    sub_area_name_arabic = models.CharField(max_length=255)
    sub_area_name_english = models.CharField(max_length=255, null=True, blank=True)
    sub_area_name_hebrew = models.CharField(max_length=255, null=True, blank=True)

class FuzzyCountry(SuperModel):
    country_name_arabic = models.CharField(max_length=255)
    country_name_english = models.CharField(max_length=255, null=True, blank=True)
    country_name_hebrew = models.CharField(max_length=255, null=True, blank=True)