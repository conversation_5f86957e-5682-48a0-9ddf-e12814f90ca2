<!DOCTYPE html>

{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_a6.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}

    {% for doc in docs %}
    <div class="waybill-container page-break">
        <div class="w-100 d-flex justify-content-around">
            <div class="qrcode-container">
                <img class="barcode" src="data:image/png;base64,{{ doc.qrcode_olivery_sequence_base64 }}"
                    alt="">
            </div>
        </div>

        <div class="text-center fs-xxlg mb-4">
            {{doc.order.olivery_sequence}}
        </div>
        <div class="text-center fs-xlg mb-4">
            <span class="fw-bold">{{meta.labels.BUSINESS_NAME}}:</span>  {{doc.company_name}}
        </div>
        <div class="text-center fs-xlg mb-4">
            <span class="fw-bold">{{meta.labels.DELIVERY_COMPANY_NAME}}:</span>  
            {% if doc.order.connection and doc.order.connection.delivery_company %}
                {{doc.order.connection.delivery_company.name}}
            {% endif %}
        </div>
        <div class="text-center fs-xlg mb-5">
            <span class="fw-bold">{{meta.labels.ADDRESS}}:</span> {{doc.order.area.name}}, {{doc.order.sub_area.name}}, {{doc.order.address}}
        </div>
        <div class="text-center fs-xlg mb-5">
            <span class="fw-bold">{{meta.labels.RECIPIENT}}:</span> {{doc.order.customer_name}}
        </div>
        <div class="text-center fs-xlg">
            <span class="fw-bold">{{meta.labels.CREATED_DATE}}:</span> {{meta.formatted_now}}
        </div>
        
    </div>
    {% endfor %}
</body>

</html>