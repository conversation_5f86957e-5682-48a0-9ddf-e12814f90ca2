from django.core.management.base import BaseCommand
from api.users.user_model import UserPreferences

class Command(BaseCommand):
    help = "Update UserPreferences to have order_due_amount and amount_received"

    def handle(self, *args, **kwargs):
        updated_count = 0

        for user_preferences in UserPreferences.objects.all():
            if user_preferences.order_tree_view:
                # Check if 'order_due_amount' and 'amount_received' are already present
                if 'order_due_amount' not in user_preferences.order_tree_view:
                    user_preferences.order_tree_view = f"{user_preferences.order_tree_view},order_due_amount"
                if 'amount_received' not in user_preferences.order_tree_view:
                    user_preferences.order_tree_view = f"{user_preferences.order_tree_view},amount_received"
            else:
                user_preferences.order_tree_view = "order_due_amount,amount_received"
            
            user_preferences.save()
            updated_count += 1

        self.stdout.write(self.style.SUCCESS(f"Successfully updated {updated_count} records."))
