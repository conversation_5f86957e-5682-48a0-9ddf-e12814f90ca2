from .user_model import *
from ...users.user_model import Company
from ..mystore_model import MyStore
from ...util_functions import *

def validate_comapny_store(origin):
    mystore = MyStore.objects.get(name=origin)
    return mystore.company , mystore


def create_user_store_util(values):
    
    user = StoreUser.objects.create_user(**values)
    return user


def store_validate_credentials(mobile_number,store):
    user = StoreUser.objects.get(mobile_number=mobile_number, store_id = store.id)
    return user


def get_store_user_from_token(request,store):
    token = request.headers.get('Authorization')
    if not token or not token.startswith('Token '):
        return None
    token_key = token.split(' ')[1]
    try:
        payload = jwt.decode(token_key, settings.SECRET_KEY, algorithms=['HS256'])
        user_id = payload.get('user_id')
        if user_id is None:
            return None

        # Validate token expiration
        exp = payload.get('exp')
        if exp is None:
            return None

        # Convert expiration time to timezone-aware datetime
        exp_datetime = datetime.datetime.fromtimestamp(exp, datetime.timezone.utc)
        if exp_datetime < timezone.now():
            return None

        try:
            auth_user = StoreUser.objects.get(id = user_id)
            if auth_user.store.id == store.id:
                return auth_user
        except StoreUser.DoesNotExist:
            return None
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def build_store_user_values(json_data, foreign_key_objects, company, store ):
    values = {**json_data, **foreign_key_objects, 'company': company , 'store':store}
    values['created_by'] = values['updated_by'] = json_data['name']
    return values


def validate_store_user(user,values,required_fields):
    validation_errors = []
    foreign_key_objects= {}

    for field in required_fields:
        if values.get(field) is None: 
            validation_errors.append(f'Missing field: {field}')
        if validation_errors:
            return validation_errors, foreign_key_objects
    foreign_key_objects.update(validate_foreign_keys(user, required_fields, validation_errors))
    
    return validation_errors, foreign_key_objects

def update_store_user_utils(values):
    store_user = StoreUser.objects.get(id=values['id'], store = values['store'])
    for key,value in values.items():
            setattr(store_user,key,value)
    store_user.save()
    return store_user

def validate_fields(values,required_fields):
    validation_errors = []

    for field in required_fields:
        if not values.get(field):
            validation_errors.append(f'Missing field: {field}')
    return validation_errors

def build_billing_address_values(json_data, foreign_key_objects, user, mystore):
    values = {**json_data, **foreign_key_objects, 'user': user, 'store': mystore}
    values['created_by'] = values['updated_by'] = user.name
    return values

def update_billing_address_utils(values):
    billing_address = StoreBillingAddress.objects.get(id=values['id'], user = values['user'], store = values['store'])
    for key,value in values.items():
            setattr(billing_address,key,value)
    billing_address.save()
    return billing_address
