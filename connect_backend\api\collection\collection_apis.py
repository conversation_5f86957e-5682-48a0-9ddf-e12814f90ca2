from rest_framework.decorators import *
from ..permissions import *
from django.http import JsonResponse
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt
from ..util_functions import *
from .collection_model import *
from ..auth.auth_model import *
from .collection_utils import *

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_collection'])])
def get_collections(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'User not found'}, status=404)
    
    fields_to_include = [
        'amount',
        'collection_status',
        'collection_type',
        'currency',
        'created_at',
        'collection_bank',
        'iban'
    ]

    model_map = {
        'collection_status': CollectionStatus,
        'collection_type': CollectionType,
        'collection_bank': CollectionBank,
    }

    objects_to_handle = ['collection_status', 'collection_type', 'collection_bank']

    related_field = {}

    query = Collection.objects.filter(company=user.company)

    collections = search_filter_group(query, json_data, fields_to_include, related_field)
    collections_dict = object_to_json(collections, objects_to_handle, model_map)
    response_data = {
        'success': True,
        'collections': collections_dict
    }

    return JsonResponse(response_data, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_collection'])])
def request_collection(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'User not found'}, status=404)
    
    required_fields = ['collection_status', 'collection_type']

    json_data = set_default_collection_fields(json_data, ['collection_status', 'collection_type'], {'collection_status': 'draft', 'collection_type': 'direct_cash'})

    filters = json_data.pop('filters', {})
    exclude = json_data.pop('exclude', {})
    collection = get_existing_collection('draft')

    validation_errors, foreign_key_objects = validate_collection(json_data, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)

    values = prepare_collection_values(json_data, foreign_key_objects, user)
    
    try:
        collection = process_collection(collection, values)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

    if json_data.get('status') != 'draft':
        update_orders_with_collection(filters, exclude, collection, user.company)

    response_data = {
        'success': True,
        'collection': CollectionSerializer(collection).data
    }
    return JsonResponse(response_data, status=201)


@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_conversion_rates(request):
    try:
        rates = fetch_conversion_rates()
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
    if not rates:
        return JsonResponse({'success': False, 'error': 'Failed to fetch conversion rates'}, status=500)
    
    serializer = ConversionRateSerializer(rates, many=True)
    return JsonResponse({'success': True, 'rates': serializer.data}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_collection_statuses(request):
    statuses = CollectionStatus.objects.all()
    serializer = CollectionStatusSerializer(statuses, many=True)
    return JsonResponse({'success': True, 'statuses': serializer.data}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_collection_banks(request):
    banks = CollectionBank.objects.all()
    serializer = CollectionBankSerializer(banks, many=True)
    return JsonResponse({'success': True, 'banks': serializer.data}, status=200)