from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType

class Command(BaseCommand):
    help = 'Create custom dashboard permissions'

    def handle(self, *args, **kwargs):
        content_type = ContentType.objects.get(app_label='api', model='user')
        custom_permissions = [
            {
                'codename': 'view_dashboard',
                'name': 'Can View Dashboard',
                'description': 'Permission to view the dashboard',
            },
            {
                'codename': 'view_financial_dashboard',
                'name': 'Can View Money in Dashboard',
                'description': 'Permission to view money totals',
            },
            {
                'codename': 'view_self_dashboard',
                'name': 'Can View Self Dashboard',
                'description': 'Can View Dashboard with his orders only',
            },
            {
                'codename': 'view_all_orders',
                'name': 'Can View All Orders',
                'description': 'Can View all orders',
            },
            {
                'codename': 'view_pos',
                'name': 'Can View pos',
                'description': 'Can View pos',
            },
            {
                'codename': 'view_self_order',
                'name': 'Can View Self Made Orders',
                'description': 'Can View orders created by him only',
            },
            {
                'codename': 'view_profit_in_order',
                'name': 'Can View Profit in Orders',
                'description': 'Can Profit in Orders',
            },
            {
                'codename': 'view_delivery_fee_in_order',
                'name': 'View Delivery Fee in Orders',
                'description': 'Can View Delivery Fee in Orders',
            },
            {
                'codename': 'view_package_cost_in_order',
                'name': 'View Package Cost in Orders',
                'description': 'Can View Package Cost in Orders',
            },
            {
                'codename': 'view_due_amount_in_order',
                'name': 'View Due Amount in Orders',
                'description': 'Can View Due Amount in Orders',
            },
            {
                'codename': 'view_customer_payment_in_order',
                'name': 'View Customer Payment in Orders',
                'description': 'Can View Customer Payment in Orders',
            },
            {
                'codename': 'view_amount_received_in_order',
                'name': 'View Amount Received in Orders',
                'description': 'Can Amount Received Payment in Orders',
            },
            {
                'codename': 'deactivate_user',
                'name': 'Can deactivate user',
                'description': 'Can deactivate user',
            },
            {
                'codename': 'reset_user_password',
                'name': 'Can reset user password',
                'description': 'Can reset user password',
            },
            {
                'codename': 'import_excel_order',
                'name': 'Can Import Excel Order',
                'description': 'Can Import Excel Order',
            },
            {
                'codename': 'view_self_client_order',
                'name': 'Can Clients for orders made by that user only',
                'description': 'Can Clients for orders made by that user only',
            },
            {
                'codename': 'send_orders_to_delivery_company',
                'name': 'Can Send Orders to Delivery Company',
                'description': 'Can Send Orders to Delivery Company',
            },
            {
                'codename': 'view_total_cod_sum_when_group_orders',
                'name': 'Can View Total COD Sum When Group Orders',
                'description': 'Can View Total COD Sum When Group Orders',
            },
            {
                'codename': 'view_total_package_cost_when_group_orders',
                'name': 'Can View Total Package Cost Sum When Group Orders',
                'description': 'Can View Package Cost Sum When Group Orders',
            },
            {
                'codename': 'view_total_delivery_fee_when_group_orders',
                'name': 'Can View Total Delivery Fee Sum When Group Orders',
                'description': 'Can View Delivery Fee Sum When Group Orders',
            },
            {
                'codename': 'view_total_profit_when_group_orders',
                'name': 'Can View Total Profit Sum When Group Orders',
                'description': 'Can View Profit Sum When Group Orders',
            },
            {
                'codename': 'view_total_order_due_amount_when_group_orders',
                'name': 'Can View Total Due Amount Sum When Group Orders',
                'description': 'Can View Due Amount Sum When Group Orders',
            },
            {
                'codename': 'view_total_amount_received_when_group_orders',
                'name': 'Can View Amount Received Sum When Group Orders',
                'description': 'Can View Amount Received Sum When Group Orders',
            },
            {
                'codename': 'change_store_status',
                'name': 'Can Change Store Status',
                'description': 'Can Change Store Status',
            },
            {
                'codename': 'change_delivery_status',
                'name': 'Can Change Delivery Status',
                'description': 'Can Change Delivery Status',
            },
            {
                'codename': 'allow_login_web',
                'name': 'Can Login using web',
                'description': 'Can Login using web',
            },
            {
                'codename': 'view_store_dashboard',
                'name': 'View Store Dashboard Blocks',
                'description': 'View Store Dashboard Blocks',
            },
            {
                'codename': 'view_delivery_dashboard',
                'name': 'View Delivery Dashboard Blocks',
                'description': 'View Delivery Dashboard Blocks',
            },
            {
                'codename': 'view_delayed_order_dashboard',
                'name': 'View Delayed Dashboard Blocks',
                'description': 'View Delayed Dashboard Blocks',
            },
            {
                'codename': 'view_billing_and_payment_page',
                'name': 'View Billing and Payment Page',
                'description': 'View Billing and Payment Page',
            },
            {
                'codename': 'view_delivery_company_page',
                'name': 'View Delivery Company Page',
                'description': 'View Delivery Company Page',
            },
            {
                'codename': 'view_driver_orders',
                'name': 'View Driver Orders',
                'description': 'View Driver Orders',
            },
            {
                'codename': 'view_collection_dashboard',
                'name': 'View Collection Dashboard',
                'description': 'View Collection Dashboard',
            },
            {
                'codename': 'change_stock_without_approval',
                'name': 'Change Stock Without Approval',
                'description': 'Change Stock Without Approval',
            },
            {
                'codename': 'view_drivers',
                'name': 'View Active Drivers',
                'description': 'View Active Drivers',
            },
            {
                'codename': 'view_warehouse_dashboard',
                'name': 'Can View Warehouse Dashboard',
                'description': 'Can View Warehouse Dashboard',
            },
            {
                'codename': 'import_excel_warehouse_variants',
                'name': 'Can Import Excel Warehouse Variants',
                'description': 'Can Import Excel Warehouse Variants',
            },
            {
                'codename': 'can_change_delivery_company_or_driver_for_sent_orders',
                'name': 'Can Change Delviery Company Or Driver For Sent Orders',
                'description': 'Can Change Delviery Company Or Driver For Sent Orders',
            },
            {
                'codename': 'cancel_subscription',
                'name': 'Cancel Subscription',
                'description': 'Can Cancel Subscription',
            },
            {
                'codename': 'view_order_driver',
                'name': 'Can View Order Driver',
                'description': 'Can View Order Driver',
            },
            {
                'codename': 'view_order_creator',
                'name': 'Can View Order Creator',
                'description': 'Can View Order Creator',
            },
            {
                'codename': 'edit_order_driver_note',
                'name': 'Edit Order Driver Note',
                'description': 'Edit Order Driver Note',
            },
            {
                'codename': 'import_excel_products',
                'name': 'Import Excel Products',
                'description': 'Can Import Excel Products',
            },
            {
                'codename': 'connect_reseller_warehouse',
                'name': 'Connect to Reseller Warehouse',
                'description': 'Can Connect to Reseller Warehouse',
            },
            {
                'codename': 'import_excel_products',
                'name': 'Import Excel Products',
                'description': 'Can Import Excel Products',
            },
        ]

        for perm in custom_permissions:
            permission, created = Permission.objects.get_or_create(
                codename=perm['codename'],
                defaults={'name': perm['name'], 'content_type': content_type}
            )

            if created:
                self.stdout.write(self.style.SUCCESS(f"Created permission: {perm['name']}"))

        self.stdout.write(self.style.SUCCESS('Custom permissions created successfully!'))
