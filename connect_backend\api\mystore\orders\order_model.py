from django.db import models
from ...models import SuperModel
from django.contrib.auth.models import User as DjUser
from ...users.user_model import Company
from ...lookups.lookup_model import *
from ..users.user_model import *
from ..mystore_model import MyStore
from ...products.product_model import *
from ...orders.order_model import *


class StoreOrder(SuperModel):
    user = models.ForeignKey(StoreUser, related_name='orders', on_delete=models.CASCADE)
    store = models.ForeignKey(MyStore , related_name='orders', on_delete=models.CASCADE)
    order = models.ForeignKey(Order, related_name='store_orders', on_delete=models.CASCADE)
