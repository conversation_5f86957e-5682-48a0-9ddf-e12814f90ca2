from django.core.management.base import BaseCommand
from django.db import transaction
from api.users.user_model import CompanyConf
from api.pricelists.pricelist_model import Pricelist
from api.pricelists.pricelist_utils import create_default_pricelist

class Command(BaseCommand):
  help = "Update company configuration with default pricelist"

  def handle(self, *args, **kwargs):
    self.stdout.write(self.style.NOTICE("Starting company conf update..."))

    with transaction.atomic():
        
      for conf in CompanyConf.objects.all():
        price = Pricelist.objects.filter(company=conf.company, connection__isnull=True).first()
        if not price:
            price = create_default_pricelist(conf.company)
        
        conf.default_pricelist = price
        conf.save()


    self.stdout.write(self.style.SUCCESS("Company configuration update completed."))
