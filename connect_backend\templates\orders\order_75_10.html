<!DOCTYPE html>
{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_75_10.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}

    {% for doc in docs %}
    {% set products = doc.order_lines %}
    {% set product_count = products|length %}
    {% set chunks = (product_count / 5)|round(0, 'ceil')|int or 1 %}

    {% for i in range(chunks) %}
    <div class="waybill-container page-break">
        <div class="header">
            <div class="header-barcode-container">
                {{meta.labels.STORE_SEQUENCE}}
                <img class="barcode" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}"
                    alt="">
            </div>
            <div class="business-logo">
                <img src="{{doc.company_logo}}" class="business-logo">
                {% if doc.company_registry %}
                <div class="text-center fw-bold">{{doc.company_registry}}</div>
                {% endif %}
            </div>
            <div class="date-time fw-bold">
                {{meta.formatted_now}}
            </div>
        </div>
        <div class="">
            <div class="left-cards-container">
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.RECIPIENT}}
                    </div>
                    <div>
                        <div class="info-block border-b-grey">
                            <span class="text-truncate">
                                {{doc.order.customer_name}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span class="">
                                {{doc.order.customer_mobile}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <div class="text-small">
                                {{doc.order.area.name}},
                                {{doc.order.address}}
                            </div>
                        </div>
                        <div class="info-block">
                            <div class="">
                                {{meta.labels.COD}}: 
                                {{doc.order.total_cod}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card overflow-hidden">
                    <div class="notes-content overflow-hidden text-small">
                        <span>{{doc.order.note[:100] if doc.order.note is not none else ""}}</span>
                    </div>
                </div>
            </div>
            <div class="right-cards-container">
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.PRODUCTS}}
                        {% if chunks > 1 %}
                        <span style="font-size: 0.8rem;">(Page {{i+1}} of {{chunks}})</span>
                        {% endif %}
                    </div>

                    <div class="products-container">
                        {% if products and products|length > 0 %}
                        <table class="o-table">
                            {% for item in products[i*5 : (i+1)*5] %}
                            <tr>
                                <td class="cell-truncate border-b-grey">
                                    {{item.product.reference_sequence if doc.print_product_sequence else item.product_variant.variant.name}}
                                </td>
                                <td class="border-b-grey">
                                    X{{item.quantity}}
                                </td>
                                <td class="border-b-grey">
                                    {{item.price}}
                                </td>
                            </tr>
                            {% endfor %}
                        </table>
                        {% else %}
                        {{doc.order.product_info.replace('\n', '<br>')}}
                        {% endif %}
                    </div>
                </div>
                <div class="header-barcode-container">
                    {{meta.labels.DELIVERY_SEQUENCE}}
                    <img class="barcode" src="data:image/png;base64,{{ doc.barcode_olivery_base64 }}"
                        alt="">
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-around w-100">
        </div>
    </div>
    {% endfor %}
    {% endfor %}
</body>

</html>