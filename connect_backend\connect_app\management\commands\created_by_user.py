from django.core.management.base import BaseCommand
from api.orders.order_model import Order
from api.users.user_model import User
from collections import defaultdict
import time
import math

class Command(BaseCommand):
    def __init__(self):
        super().__init__()
        self.last_percent_printed = {}

    def handle(self, *args, **kwargs):
        start_time = time.time()
        
        # Get all orders (initial count)
        orders = Order.objects.select_related('company').all()
        total_orders = orders.count()
        
        if not total_orders:
            self.stdout.write("No orders to process.")
            return

        self.stdout.write(f"Processing {total_orders} orders...")
        
        # Group orders by company
        company_orders = defaultdict(list)
        for i, order in enumerate(orders, 1):
            company_orders[order.company.company_id].append(order)
            self._print_progress(i, total_orders, "Grouping orders")

        # Fetch all users for each company
        company_users = {}
        company_ids = list(company_orders.keys())
        total_companies = len(company_ids)
        
        for j, company_id in enumerate(company_ids, 1):
            users = User.objects.filter(company_id=company_id)
            company_users[company_id] = {user.name: user for user in users}
            self._print_progress(j, total_companies, "Fetching users")

        # Prepare orders for bulk update
        orders_to_update = []
        processed_orders = 0
        total_to_process = sum(len(orders) for orders in company_orders.values())
        
        for company_id, orders in company_orders.items():
            user_ids = company_users[company_id]
            for order in orders:
                if order.created_by in user_ids:
                    order.created_by_user = user_ids[order.created_by]
                    orders_to_update.append(order)
                processed_orders += 1
                self._print_progress(processed_orders, total_to_process, "Preparing updates")

        # Bulk update orders
        if orders_to_update:
            self.bulk_update_in_batches(orders_to_update, ['created_by_user'])
            self.stdout.write(self.style.SUCCESS(f"\nSuccessfully updated {len(orders_to_update)} orders."))
        else:
            self.stdout.write("\nNo orders were updated.")
            
        end_time = time.time()
        execution_time = end_time - start_time
        self.stdout.write(f"Command executed in {execution_time:.2f} seconds")

    def bulk_update_in_batches(self, queryset, fields, batch_size=1000):
        total = len(queryset)
        for i in range(0, total, batch_size):
            batch = queryset[i:i+batch_size]
            Order.objects.bulk_update(batch, fields)
            self.stdout.write(f"Updated {i + len(batch)} of {total} orders...")

    def _print_progress(self, current, total, stage):
        percent = int((current / total) * 100)
        last_printed = self.last_percent_printed.get(stage, -5)

        if percent - last_printed >= 5 or percent == 100:
            self.stdout.write(f"{stage}: {current}/{total} ({percent}%)")
            self.stdout.flush()
            self.last_percent_printed[stage] = percent
