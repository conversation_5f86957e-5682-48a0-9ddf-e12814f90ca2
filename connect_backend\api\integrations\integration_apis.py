from django.http import JsonResponse
import json
from ..util_functions import *
from ..users.user_model import Company
from ..connection.connection_model import ConnectDeliveryCompany
from ..lookups.lookup_model import Country
from ..pricelists.pricelist_model import *
from ..connection.connection_model import ConnectDeliveryCompany
from ..orders.order_utils import *
from ..billing.billing_utils import *
from .integration_utils import *
from ..permissions import *
from ..permissions import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt
from ..error.error_model import *
from django.utils.translation import gettext as _
import requests
from ..mystore.mystore_utils import validate_missing_field 

@csrf_exempt
def generate_integration_token(request, ecommerce):
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)
    
    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    if user.role.name == 'Sales':
        return JsonResponse({'success': False, 'error': 'Unauthorized'}, status=403)

    company = user.company
    ecommerce_token = create_ecommerce_token(company, ecommerce, user.name)
    token = encode_token(ecommerce_token, ecommerce)
    
    return JsonResponse({'token': token}, status=200)

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def add_orders(request):
    try:
        json_body = parse_request_body(request)
        if not json_body:
            raise get_error_response('GENERAL_001', {})

        integration_token = validate_token(request)
        if not integration_token:
            raise get_error_response('AUTH_407', {})
    
        company = get_company(integration_token.get('company'))
        if not company:
            raise get_error_response('AUTH_407', {})
    
        orders_list = json_body.get('orders_list', [])
        
        required_fields = [
            'customer_mobile', 'customer_name', 'address',
            'area', 'sub_area', 'country', 'order_reference', 'package_cost'
        ]
        validation_errors, foreign_key_objects_list = validate_orders(orders_list, company, required_fields)
        if validation_errors:
            raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
        
        values = {
            'orders_list': orders_list,
            'foreign_key_objects_list': foreign_key_objects_list,
            'company': company,
            'integration_token': integration_token
        }

        order_sequences, order_references, auto_send_list = process_integration_orders(values)
        auto_send_info = ''
        if auto_send_list:
            try:
                success_meessages, fail_messages = auto_send_orders(auto_send_list, company, integration_token.get('channel'), status=Status.objects.get(code='new_order'))
                auto_send_info = json.dumps(success_meessages, ensure_ascii=False) + '\n'
                auto_send_info += json.dumps(fail_messages, ensure_ascii=False)
            except Exception as e:
                auto_send_info = str(e)
                print(str(e))
                pass

        try:
            handle_send_notification(order_sequences,integration_token.get('channel'))
            IntegrationLogs.objects.create(company=company, integration=integration_token.get('channel'), request_body=json_body, response_body={'success': True, 'message': 'Orders added successfully', 'order_sequences': order_sequences, 'order_references': order_references}, status_code=201, additional_information=auto_send_info)
        except Exception as e:
            print(str(e))
            pass

        return JsonResponse({'success': True, 'message': 'Orders added successfully', 'order_sequences': order_sequences, 'order_references': order_references}, status=201)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_407':
            status=401
        else:
            status=400
        try:
            IntegrationLogs.objects.create(company=company, integration=integration_token.get('channel'), request_body=json_body, response_body=response, status_code=status)
        except Exception as e:
            pass
        return JsonResponse({'success':False, 'error': response['error']['message']}, status=status)
    except Exception as e:
        try:
            IntegrationLogs.objects.create(company=company, integration=integration_token.get('channel'), request_body=json_body, response_body={'success': False, 'error': str(e)}, status_code=500)
        except Exception as e:
            pass
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
def get_areas(request):
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    countries = Country.objects.prefetch_related('area_set__subarea_set').all()
    
    data = [
        {
            'id': country.id,
            'name': country.name,
            'code': country.code,
            'mobile_intro': country.mobile_intro,
            'country_flag': country.country_flag,
            'currency': country.currency,
            'areas': [
                {
                    'id': area.id,
                    'name': area.name,
                    'code': area.code,
                    'subareas': [
                        {
                            'id': subarea.id,
                            'name': subarea.name,
                            'code': subarea.code
                        }
                        for subarea in area.subarea_set.all()
                    ]
                }
                for area in country.area_set.all()
            ]
        }
        for country in countries
    ]

    return JsonResponse({'success': True, 'countries': data}, status=200)
    
@csrf_exempt
def get_delivery_companies(request):
    if request.method != 'GET':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    payload = validate_token(request)
    if not payload:
        return JsonResponse({'success': False, 'error': 'Invalid token'}, status=401)

    company_id = payload.get('company')
    try:
        company = Company.objects.get(company_id=company_id)
    except Company.DoesNotExist:
        return JsonResponse({'success': False, 'message': 'Invalid company'}, status=400)

    delivery_companies = ConnectDeliveryCompany.objects.filter(
        company=company, connection_status='connected'
    ).select_related('delivery_company')

    data = [
        {
            'id': delivery_company.delivery_company.id,
            'name': delivery_company.delivery_company.name,
            'delivery_company_url': delivery_company.delivery_company.delivery_company_url
        }
        for delivery_company in delivery_companies
    ]

    return JsonResponse({'success': True, 'delivery_companies': data}, status=200)

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def update_order_status(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    order_sequence = json_data.get('order_sequence')
    if '-' in order_sequence:
        order_sequence, company_id = order_sequence.split('-')
    else:
        raise get_error_response('GENERAL_003', {'fields': 'order_sequence'})
    status_code = json_data.get('status_code')
    if not order_sequence or not status_code:
        raise get_error_response('GENERAL_003', {'fields': ','.join(key in json_data.keys() if not json_data.get(key) else '')})
    
    try:
        company = Company.objects.get(company_id=company_id)
    except Company.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Company', 'field':'company_id', 'value': company_id})
    
    try:
        order = Order.objects.get(order_sequence=order_sequence, company=company)
    except Order.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Order', 'field':'order_sequence', 'value': order_sequence})
    
    try:
        status = StatusMap.objects.get(status_code=status_code)
    except Status.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Status', 'field':'code', 'value': status_code})

    try:
        if order.delivery_company and order.delivery_company.name == 'Cliq':
            order.delivery_company_status = status.delivery_company_status
            order.updated_by = 'Cliq'
            order.save()
        else:
            return JsonResponse({'success': False, 'message': 'You have no access to edit this order'}, status=401)
    except Exception as e:
        print(e)
        pass

    return JsonResponse({'success': True, 'message': 'Status Updated Successully'}, status=200)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_products(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    integration_token = validate_token(request)
    if not integration_token:
        raise get_error_response('AUTH_407', {})

    company = get_company(integration_token.get('company'))
    if not company:
        raise get_error_response('GENERAL_002', {'model': 'Company', 'field': 'company_id', 'value': integration_token.get('company')})
    
    response = get_records('Product', json_data, False, company_filter=Q(company=company))
    return JsonResponse(response, status=200)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_product_variants(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    integration_token = validate_token(request)
    if not integration_token:
        raise get_error_response('AUTH_407', {})

    company = get_company(integration_token.get('company'))
    if not company:
        raise get_error_response('GENERAL_002', {'model': 'Company', 'field': 'company_id', 'value': integration_token.get('company')})
    
    response = get_records('WarehouseVariant', json_data, False, company_filter=Q(variant__product__company=company))
    return JsonResponse(response, status=200)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_connected_companies(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    integration_token = validate_token(request)
    if not integration_token:
        raise get_error_response('AUTH_407', {})
    
    company = get_company(integration_token.get('company'))

    response = get_records('ConnectDeliveryCompany', json_data, False, company_filter=Q(company=company, connection_status='connected'))
    return JsonResponse(response, status=200)
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_delivery_fee(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    integration_token = validate_token(request)
    if not integration_token:
        raise get_error_response('AUTH_407', {})
    
    company = get_company(integration_token.get('company'))

    connection_id = json_data.get('connection')
    area_id = json_data.get('area')
    if not connection_id or not area_id:
        raise get_error_response('GENERAL_003', {'fields': 'connection, area'})
    
    try:
        connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    except ConnectDeliveryCompany.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'ConnectDeliveryCompany', 'field': 'id', 'value': connection_id})
    
    try:
        area = Area.objects.get(id=area_id)
    except Area.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Area', 'field': 'id', 'value': area_id})

    delivery_fee = get_delivery_fee_util(company, connection, area)
    return JsonResponse({'success': True, 'delivery_fee': delivery_fee}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_area_sub_area(request):
    country_code = request.GET.get('code', None)
    
    if country_code:
        countries = Country.objects.filter(code=country_code).prefetch_related('areas__subareas')
    else:
        countries = Country.objects.prefetch_related('areas__subareas').all()
    
    serializer = CountrySerializer(countries, many=True, context={'request': request, 'show_areas': True, 'show_subareas': True})
    return JsonResponse({'success': True, 'records': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_woocommerceinformation'])])
def add_woocommerce_information(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    required_fields = [
        'consumer_key', 'consumer_secret', 'woocommerce_url'
    ]

    values = json_data
    values['company'] = user.company

    validation_errors = validate_missing_field(values,required_fields)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
    try:
        with transaction.atomic():
            woo_info = WoocommerceInformation.objects.create(**values)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    response = {
        'success': True,
        'message': 'Woocommerce Information added successfully'
    }
    return JsonResponse(response, status=201)


@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_woocommerceinformation'])])
def get_woocommerce_information(request):
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    try:
        woo_info = WoocommerceInformation.objects.get(company=user.company)
        response_data = WoocommerceInformationSerializer(woo_info).data
    except WoocommerceInformation.DoesNotExist:
        response_data = {"response_data":None}
        return JsonResponse(response_data, status=200)
    response_data = {"response_data":response_data}
    return JsonResponse(response_data, status=200)


@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_woocommerceinformation'])])
def update_woocommerce_information(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    values = json_data
    values['company'] = user.company
    values['updated_by'] = user.name

    try:
        with transaction.atomic():
            woo_info = update_woocommerce_information_util(values)
    except Exception as e:
        raise get_error_response('GENERAL_007', {'error': str(e)})
    response = {
        'success': True,
        'message': 'Woocommerce Information update successfully'
    }
    return JsonResponse(response, status=201)
