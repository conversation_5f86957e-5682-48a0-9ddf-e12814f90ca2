from ...util_functions import *
from ...dashboard.dashboard_utils import *
from .admin_dashboard_utils import *
from ..admin_utils import *

def get_total_orders():

    return {
        "count": Order.objects.count()
    }

def get_orders_last_three_months():
    three_months_ago = datetime.datetime.now() - relativedelta(months=3)
    orders_in_last_three_months = Order.objects.filter(created_at__date__gte = three_months_ago).count()
    return {
        "count" : orders_in_last_three_months
    }

def get_total_companies():
    companies = Company.objects.exclude(company_mobile__startswith='01111').count()
    filters = [{"operator":"and","filters":[{"field": "company_mobile", "operator": "not_startswith", "value": "01111"}]}]
    summary = {
        "count" : companies,
        "filters":filters
    }

    return summary

def get_active_companies():
    active_time = datetime.datetime.now() - relativedelta(days=7)
    active_companies_ids  = Order.objects.filter(created_at__date__gte=active_time).exclude(company__company_mobile__startswith='01111').values_list('company',flat=True).distinct()
    active_companies =active_companies_ids.count()
    filters = [
        {"operator": "and", "filters": [
            {"field": "company_id", "operator": "in", "value": list(active_companies_ids)},
        ]}
    ]
    summary = {
        "count" : active_companies,
        "filters":filters
    }
    return summary

def get_subscribed_companies():
    companies = Company.objects.all()
    companies = get_is_subscribed_filter(companies, 'subscribed')
    company_ids = list(companies.values_list('company_id', flat=True))
    filters = [
        {"operator": "and", "filters": [
            {"field": "company_id", "operator": "in", "value": company_ids},
        ]}
    ]
    summary = {
        "count" : companies.count(),
        "filters": filters
    }
    return summary

def get_not_active_companies():
    active_time = datetime.datetime.now() - relativedelta(days=7)
    active_companies_ids  = Order.objects.filter(created_at__date__gte=active_time).exclude(company__company_mobile__startswith='01111').values_list('company',flat=True).distinct()
    not_active_companies = Company.objects.exclude(company_id__in=list(active_companies_ids)).exclude(company_mobile__startswith='01111').count()
    filters = [
        {"operator": "and", "filters": [
            {"field": "company_mobile", "operator": "not_startswith", "value": "01111"},
            {"field": "company_id", "operator": "not_in", "value": list(active_companies_ids)}
        ]}
    ]
    summary = {
        "count" : not_active_companies,
        "filters":filters
    }
    return summary

def get_total_delivery_companies():
    return {
        "count" :DeliveryCompany.objects.count()
    }
