from ..util_functions import *
from ..logs.logs_utils import set_default_tracked_fields
from ..pricelists.pricelist_utils import create_default_pricelist
import re
from ..dashboard.dashboard_model import *
from django.utils.translation import gettext as _
from api.auth.auth_utils import extract_username

def validate_mobile_number(mobile_number):
    return bool(re.match(r'^0[0-9]{8,10}$', mobile_number))

def validate_user(user, required_fields):
    validation_errors = []
    foreign_key_objects = {}

    for field in required_fields:
        if not user.get(field):
            validation_errors.append(f'Missing field: {field}')
        elif field in ['mobile_number', 'second_phone']:
            if not validate_mobile_number(user[field]):
                validation_errors.append(f'Invalid {field} format')

    if user.get('id', None):
        user_instance = User.objects.get(id=user.get('id', None))
        if user.get('mobile_number', None) != user_instance.mobile_number or user.get('country_code', None) != user_instance.country_code:
            if User.objects.filter(mobile_number=user.get('mobile_number'), country_code=user.get('country_code')).exists():
                validation_errors.append(_('User with same mobile number already exists'))

    foreign_key_objects.update(validate_foreign_keys(user, required_fields, validation_errors))

    if foreign_key_objects.get('role', None) and user.get('company'):
        role = foreign_key_objects['role']
        company=user['company']
        
        user_to_edit = None
        if user.get('id'):
            try:
                user_to_edit = User.objects.get(id=user['id'])
            except User.DoesNotExist:
                pass
        
        if role.name == 'Driver' and (not user_to_edit or user_to_edit.role.name != 'Driver'):
            number_of_drivers = User.objects.filter(company=company, role=role).count()
            business_drivers_limit = SystemConfiguration.objects.first().business_drivers_limit
            if number_of_drivers >= business_drivers_limit:
                validation_errors.append(_('You have reached your drivers limit'))

    return validation_errors, foreign_key_objects

def validate_company(company, required_fields):
    validation_errors = []
    foreign_key_objects = {}
    for field in required_fields:
        if not company.get(field):
            validation_errors.append(f'Missing field: {field}')
        elif field == 'company_mobile':
            if not validate_mobile_number(company[field]):
                validation_errors.append(f'Invalid {field} format')

    company['area'] = company.pop('company_area')
    required_fields.append('area')
    foreign_key_objects.update(validate_foreign_keys(company, required_fields, validation_errors))

    return validation_errors, foreign_key_objects

def build_user_values(json_data, foreign_key_objects, company):
    values = {**json_data, **foreign_key_objects, 'company': company}
    return values

def get_company_values(json_data):
    values = {}
    values['company_name'] = json_data.pop('company_name')
    values['created_by'] = values['updated_by'] = json_data['name']
    values['company_mobile'] = json_data['mobile_number'] #TODO: Should seperate values of company and business Super
    values['company_area'] = json_data['area']
    values['company_social_link'] =  json_data.pop('company_social_link', None)

    return values

def build_company_values(json_data, foreign_key_objects):
    json_data.pop('country', None)
    foreign_key_objects['company_area'] = foreign_key_objects.pop('area')
    json_data.pop('area')
    values = {**json_data, **foreign_key_objects}
    return values

def build_company_conf_values(foreign_key_objects):
    values = {}
    company = foreign_key_objects.pop('company')
    values['company'] = company
    values['default_connection'] = None
    values['auto_send_orders'] = False
    values['is_international_business'] = False
    values['auto_send_on_status'] = None
    values['default_area'] = company.company_area
    values['default_country'] = company.company_area.country
    values['delivery_request_status'] = Status.objects.get(code='with_delivery_company')
    values['commercial_number'] = company.company_mobile
    if 'country_code' in foreign_key_objects and foreign_key_objects['country_code']:
        values['default_country_code'] = foreign_key_objects['country_code']
    else:
        mobile_intro = company.company_area.country.mobile_intro
        if mobile_intro and len(mobile_intro) > 6 and '|' in mobile_intro:
            values['default_country_code'] = mobile_intro.split('|')[0]
    return values

def create_company(values, country_code=None):

    from ..category.category_utils import create_default_category

    company = Company.objects.create(**values)
    conf_values = build_company_conf_values({'company': company, 'country_code': country_code})
    conf_values['created_by'] = conf_values['updated_by'] = values['created_by']
    default_category = create_default_category(company)
    conf_values['default_category'] = default_category
    default_pricelist = create_default_pricelist(company)
    conf_values['default_pricelist'] = default_pricelist
    company_conf = create_company_conf(conf_values)
    log = create_log('Company', company, 'create', values['created_by'], company.company_id)
    fields = get_tracked_fields(company, 'Company')
    log_tracked_fields(log, values, fields)
    delete_log_if_no_changes(log)
    delayed_filters = DelayedOrderFilters.objects.create(delivery_status=','.join(['in_progress', 'waiting']), days_delayed=2, company=company)
    return company, company_conf

def create_user(values):
    with transaction.atomic():
        password = values.pop('password', None)
        user = User.objects.create(**values)
        username = extract_username(user)
        user.user = DjUser.objects.create(username = username)
        user.save()

        if password:
            user.user.set_password(password)

        group_name = values['role'].name
        user.user.groups.add(Group.objects.get(name=group_name))
        user.user.save()
        user.save()
        log = create_log('User', values['company'], 'create', values['created_by'], user.id)
        fields = get_tracked_fields(values['company'], 'User')
        log_tracked_fields(log, values, fields)
        delete_log_if_no_changes(log)
        return user

def update_company_util(values, company):
    log = create_log('Company', company, 'update', values['updated_by'], company.company_id)
    update_company_fields(company, values, log)
    delete_log_if_no_changes(log)
    company.save()
    return company

def update_company_fields(company, values, log):
    fields = get_tracked_fields(company, 'Company')
    for key, value in values.items():
        if key == 'company_mobile' and company.company_mobile != values['company_mobile']:
            if Company.objects.filter(company_mobile=values.get('company_mobile')).exists():
                raise get_error_response('AUTH_418', {'mobile_number': values.get('company_mobile')})
        if key == 'company_name' and company.company_name != values['company_name']:
            if Company.objects.filter(company_name=values.get('company_name')).exists():
                raise get_error_response('AUTH_419', {'company_name': values.get('company_name')})
        old_value = getattr(company, key, None)
        if old_value!= value:
            log_and_update_field(company, key, value, old_value, log, bool(key in fields))

def get_user(id):
    try:
        user = User.objects.get(id=id)
        return user
    except User.DoesNotExist:
        return None
    
def update_user_util(values):
    user = get_user(values['id'])
    if not user:
        raise ValidationError('User not found')
    log = create_log('User', values['company'], 'update', values['updated_by'], user.id)
    update_user_fields(user, values, log)
    delete_log_if_no_changes(log)
    user.save()
    return user

def update_user_fields(user, values, log):
    fields = get_tracked_fields(user.company, 'User')
    for key, value in values.items():
        old_value = getattr(user, key, None)
        if old_value != value:
            log_and_update_field(user, key, value, old_value, log, bool(key in fields))

def create_company_conf(values):
    company_conf = CompanyConf.objects.create(**values)
    company_conf.delayed_order_statuses.set(StatusMap.objects.filter(status_code__in=['in_progress', 'waiting']))
    company_conf.save()
    log = create_log('CompanyConf', values['company'], 'create', values['created_by'], company_conf.id)
    fields = get_tracked_fields(values['company'], 'CompanyConf')
    log_tracked_fields(log, values, fields)
    delete_log_if_no_changes(log)
    warehouse = Warehouse.objects.create(name='Default Warehouse', company=company_conf.company, area=company_conf.company.company_area, country=company_conf.company.company_area.country, address=company_conf.company.company_area.name)
    company_conf.default_warehouse = warehouse
    company_conf.save()
    return company_conf

def set_default_vhub_fields(company):
    vhub_fields = {
        'total_cod': {
            'vhub_field': 'copy_total_cost',
            'is_foreign_key': False,
        },
        'note': {
            'vhub_field': 'note',
            'is_foreign_key': False,
        },
        'area': {
            'vhub_field': 'customer_area',
            'is_foreign_key': True,
        },
        'sub_area': {
            'vhub_field': 'customer_sub_area',
            'is_foreign_key': True,
        },
        'status': {
            'vhub_field': 'state',
            'is_foreign_key': True,
        },
        'paid': {
            'vhub_field': 'paid',
            'is_foreign_key': False,
        },
        'customer_mobile': {
            'vhub_field': 'customer_mobile',
            'is_foreign_key': False,
        },
        'address': {
            'vhub_field': 'customer_address',
            'is_foreign_key': False,
        }
    }

    delivery_statuses = ['in_branch', 'picked_up', 'picking_up', 'waiting']

    for delivery_status in delivery_statuses:
        status_map = StatusMap.objects.get(status_code=delivery_status)
        for key, value in vhub_fields.items():
            vhub_field = VhubFieldMap.objects.update_or_create(
                company=company,
                field=key,
                status= status_map,
                defaults={
                    'vhub_field': value['vhub_field'],
                    'is_foreign_key': value['is_foreign_key']
                }
            )

def setup_new_company(company):
    set_default_tracked_fields(company)
    set_default_vhub_fields(company)

def validate_conf(data, company):
    validation_errors = []
    foreign_key_objects = {}

    required_fields = ['default_country', 'default_area']
    for field in required_fields:
        value = data.get(field)
        if value is None or value == '':
            validation_errors.append(_('Missing field: ') + field)

    foreign_key_objects.update(validate_conf_foreign_keys(data, validation_errors))

    return validation_errors, foreign_key_objects

def validate_conf_foreign_keys(data, validation_errors):
    foreign_key_objects = {}
    temp_data = {}

    if data.get('default_delivery_company', None):
        foreign_key_objects['default_delivery_company'] = get_delivery_company(data.get('default_delivery_company'))
        if not foreign_key_objects['default_delivery_company']:
            validation_errors.append(_('Invalid default_delivery_company ID'))

    if data.get('default_connection', None):
        foreign_key_objects['default_connection'] = get_connection(data.get('default_connection'))
        if not foreign_key_objects['default_connection']:
            validation_errors.append(_('Invalid default_connection ID'))

    if data.get('default_warehouse', None):
        foreign_key_objects['default_warehouse'] = get_warehouse(data.get('default_warehouse'))
        if not foreign_key_objects['default_warehouse']:
            validation_errors.append(_('Invalid default_warehouse ID'))

    if data.get('default_country', None):
        temp_data['country'] = data.get('default_country')
        foreign_key_objects['default_country'] = get_country(temp_data, validation_errors)

    if data.get('default_area', None):
        temp_data['area'] = data.get('default_area')
        foreign_key_objects['default_area'] = get_area(temp_data, validation_errors)

    if data.get('default_category', None):
        category_id = data.get('default_category')
        foreign_key_objects['default_category'] = Category.objects.get(id=category_id)
        if not foreign_key_objects['default_category']:
            validation_errors.append(_('Invalid Category assigned'))

    if data.get('default_pricelist', None):
        pricelist_id = data.get('default_pricelist')
        foreign_key_objects['default_pricelist'] = Pricelist.objects.get(id=pricelist_id)
        if not foreign_key_objects['default_pricelist']:
            validation_errors.append(_('Invalid Pricelist assigned'))

    if data.get('deduct_virtual_quantity', None):
        foreign_key_objects['deduct_virtual_quantity'] = get_status(data.get('deduct_virtual_quantity', None))
        if not foreign_key_objects['deduct_virtual_quantity']:
            validation_errors.append(_('Invalid deduct_virtual_quantity ID'))

    if data.get('deduct_physical_quantity', None):
        foreign_key_objects['deduct_physical_quantity'] = get_status(data.get('deduct_physical_quantity', None))
        if not foreign_key_objects['deduct_physical_quantity']:
            validation_errors.append(_('Invalid deduct_physical_quantity ID'))

    if data.get('delivery_request_status', None):
        foreign_key_objects['delivery_request_status'] = get_status(data.get('delivery_request_status'))
        if not foreign_key_objects['delivery_request_status']:
            validation_errors.append(_('Invalid delivery_request_status ID'))

    foreign_key_objects['providers'] = []
    if data.get('providers', None) and isinstance(data['providers'], list):
        providers = Provider.objects.filter(id__in=data['providers'])
        foreign_key_objects['providers'] = list(providers)

    foreign_key_objects['additional_countries'] = []
    if data.get('additional_countries', None) and isinstance(data['additional_countries'], list):
        countries = Country.objects.filter(code__in=data['additional_countries'])
        foreign_key_objects['additional_countries'] = list(countries)

    foreign_key_objects['delayed_order_statuses'] = []
    if data.get('delayed_order_statuses', None) and isinstance(data['delayed_order_statuses'], list):
        delayed_order_statuses = StatusMap.objects.filter(status_code__in=data['delayed_order_statuses'])
        foreign_key_objects['delayed_order_statuses'] = list(delayed_order_statuses)

    return foreign_key_objects

def update_conf_util(values, user):
    company_conf = get_company_conf(user.company)
    if not company_conf:
        return None
    
    with transaction.atomic():
        log = create_log('CompanyConf', user.company, 'update', values['updated_by'], company_conf.id)
        update_conf_fields(company_conf, values, log)
        delete_log_if_no_changes(log)
        company_conf.save()
        return company_conf

def update_conf_fields(company_conf, values, log):
    for key, value in values.items():
        if hasattr(company_conf, key):
            field = company_conf._meta.get_field(key)
            old_value = getattr(company_conf, key, None)

            if isinstance(field, models.ManyToManyField):
                new_value_set = set(value)
                old_value_set = set(company_conf.__getattribute__(key).all())

                to_add = new_value_set - old_value_set
                to_remove = old_value_set - new_value_set
                
                if to_add or to_remove:
                    old_value_str = ''
                    new_value_str = ''
                    getattr(company_conf, key).set(value)
                    if key in ['providers', 'additional_countries']:
                        old_value_str = ','.join([old_value.name for old_value in old_value_set])
                        new_value_str = ','.join([new_value.name for new_value in new_value_set])
                    elif key == 'delayed_order_statuses':
                        old_value_str = ','.join([old_value.delivery_company_status for old_value in old_value_set])
                        new_value_str = ','.join([new_value.delivery_company_status for new_value in new_value_set])
                    create_log_info(log, key, old_value_str, new_value_str)
            else:
                if not value and isinstance(field, models.ForeignKey):
                    value = None
                if key in ['order_form_fields', 'order_waybill_templates', 'order_mobile_card_fields']:
                    cleaned_value = ",".join(value) if isinstance(value, list) else value
                    if old_value != cleaned_value:
                        log_and_update_field(company_conf, key, cleaned_value, old_value, log, True)
                elif old_value != value:
                    log_and_update_field(company_conf, key, value, old_value, log, True)
