from django.urls import path
from .dashboard_apis import *

urlpatterns = [
    path('get_reseller_info', get_reseller_info, name='get_reseller_info'),
    path('get_true_buyer', get_wallet, name='get_true_buyer'),
    path('update_profile', update_profile, name='update_profile'),
    path('add_company', add_company, name='add_company'),
    path('update_company', update_company, name='update_company'),
    path('get_debit', get_debit, name='get_debit'),
    path('get_companies', get_companies, name='get_companies'),
    path('get_delivery_companies', get_delivery_companies, name='get_delivery_companies'),
    path('get_invitation_link', get_invitation_link, name='get_invitation_link'),
    path('activate_account', activate_account, name='activate_account'),
    path('set_business_delivery_companies', set_business_delivery_companies, name='set_business_delivery_companies'),

]