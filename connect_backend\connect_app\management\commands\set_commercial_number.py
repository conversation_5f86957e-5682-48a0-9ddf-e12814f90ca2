from django.core.management.base import BaseCommand
from api.users.user_model import CompanyConf

class Command(BaseCommand):
    help = 'Fill missing commercial_number in existing CompanyConf records'

    def handle(self, *args, **kwargs):
        updated = 0
        for conf in CompanyConf.objects.filter(commercial_number__isnull=True):
            if conf.company and conf.company.owner and conf.company.owner.mobile_number:
                conf.commercial_number = conf.company.owner.mobile_number
                conf.save()
                updated += 1
        self.stdout.write(self.style.SUCCESS(f'Updated {updated} CompanyConf records.'))