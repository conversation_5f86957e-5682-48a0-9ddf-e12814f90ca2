import requests
import base64
import uuid
from django.core.management.base import BaseCommand
from api.delivery_company.delivery_company_model import DeliveryCompany, AreaMap, SubAreaMap

class Command(BaseCommand):
    help = 'Save Ids of Areas and Sub Areas in the db'

    def handle(self, *args, **kwargs):
        delivery_companies = DeliveryCompany.objects.all()
        for delivery_company in delivery_companies:
            base_url = delivery_company.delivery_company_url
            json_body = {
                "jsonrpc": "2.0"
            }
            areas_endpoint = f'{base_url}/connection/get_areas'
            areas_response = requests.post(areas_endpoint, json=json_body)
            if areas_response.status_code == 200:
                response = areas_response.json()
                areas = response.get('result', {}).get('areas', [])
                for map in AreaMap.objects.filter(delivery_company=delivery_company, imported_area__gte=''):
                    for area in areas:
                        if map.imported_area == area.get('name'):
                            map.imported_area_id = area.get('id')
                            map.save()
            subareas_endpoint = f'{base_url}/connection/get_subareas'
            subareas_response = requests.post(subareas_endpoint, json=json_body)
            if subareas_response.status_code == 200:
                response = subareas_response.json()
                subareas = response.get('result', {}).get('sub_areas', [])
                for map in SubAreaMap.objects.filter(delivery_company=delivery_company, imported_sub_area__gte=''):
                    for subarea in subareas:
                        if map.imported_sub_area == subarea.get('name'):
                            map.imported_sub_area_id = subarea.get('id')
                            map.parent_area_name = subarea.get('parent_area_name')
                            map.parent_area_id = subarea.get('parent_area_id')
                            map.save()