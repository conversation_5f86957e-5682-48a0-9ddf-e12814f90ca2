from django.urls import path
from .integration_apis import *

urlpatterns = [
    path('generate_integration_token/<str:ecommerce>', generate_integration_token, name='generate_integration_token'),
    path('add_orders', add_orders, name='add_orders'),
    path('get_areas', get_areas, name='get_areas'),
    path('get_delivery_companies', get_delivery_companies, name='get_delivery_companies'),
    path('update_order_status', update_order_status, name='update_order_status'),
    path('get_products', get_products, name='get_products'),
    path('get_product_variants', get_product_variants, name='get_product_variants'),
    path('get_connected_companies', get_connected_companies, name='get_connected_companies'),
    path('get_delivery_fee', get_delivery_fee, name='get_delivery_fee'),
    path('get_area_sub_area', get_area_sub_area, name='get_area_sub_area'),
    path('add_woocommerce_information', add_woocommerce_information, name='add_woocommerce_information'),
    path('get_woocommerce_information', get_woocommerce_information, name='get_woocommerce_information'),
    path('update_woocommerce_information', update_woocommerce_information, name='update_woocommerce_information'),
]