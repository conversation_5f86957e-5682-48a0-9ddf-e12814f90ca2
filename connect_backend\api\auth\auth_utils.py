from .auth_model import *
import datetime
from django.contrib.auth import authenticate
from django.utils import timezone
from django.core.exceptions import ValidationError
import random
import string
from api.classes import Whatsapp
from django.http import JsonResponse
from django.conf import settings
import jwt
from ..users.user_model import User
from ..slack_otp import send_slack_otp
from ..discord_otp import send_discord_otp
from ..error.error_model import *
from ..middleware import get_current_request
from ..reseller.models import *

def generate_otp_code():
    return ''.join(random.choices(string.digits, k=6))

def get_or_create_otp(mobile_number, values):
    try:
        otp = OtpCodes.objects.get(mobile_number=mobile_number)
        if otp.number_of_trials >= 3:
            if otp.created_at + timedelta(hours=1) <= timezone.now():
                otp.number_of_trials = 0
                otp.code = values['code']
                otp.error_trials = 0
                otp.save()
            else:
                dt = otp.created_at + datetime.timedelta(hours=1)
                raise Exception('OTP_LIMIT_EXCEEDED', dt.strftime('%Y-%m-%d %H:%M:%S.') + f'{dt.microsecond:06d}{dt.strftime("%z")}')
        otp.code = values['code']
        otp.error_trials = 0
        otp.expire_date = timezone.now() + datetime.timedelta(minutes=3)
        otp.save()
        return otp
    except OtpCodes.DoesNotExist:
        values['number_of_trials'] = 0
        return OtpCodes.objects.create(**values)
    except ValidationError as e:
        raise e

def send_message_and_log(full_mobile_number, message):
    try:
        send_slack_otp(f"Mobile Number={full_mobile_number}, Message={message}")
    except Exception as e:
        print(f"Failed to send Slack OTP: {e}")

    try:
        send_discord_otp(f"Mobile Number={full_mobile_number}, Message={message}")
    except Exception as e:
        print(f"Failed to send Discord OTP: {e}")
    
    response = Whatsapp.send_message(mobile_number=full_mobile_number, message=message)
    if response.status_code != 200:
        raise get_error_response('AUTH_405', {'error': response.text})

def send_otp_message(country_code ,mobile_number, otp_code):
    message = f"Your Olivery Connect OTP Code: {otp_code}"
    
    if country_code in ['+972', '+970']:
        for specific_code in ['+972', '+970']:
            refined_number = Whatsapp.refine_mobile_number(specific_code, mobile_number)
            send_message_and_log(refined_number, message)
    else:
        full_mobile_number = Whatsapp.refine_mobile_number(country_code, mobile_number)
        send_message_and_log(full_mobile_number, message)

def update_otp_trials(otp):
    otp.number_of_trials += 1
    otp.save()

def validate_token(token):
    try:
        token_obj = OtpToken.objects.get(token=token)
    except OtpToken.DoesNotExist:
        raise get_error_response('AUTH_407', {})
    if token_obj.expires_at < timezone.now():
        expires_at = token_obj.expires_at.strftime('%Y-%m-%d %H:%M:%S.%f%z')
        raise get_error_response('AUTH_406', {'expires_at': expires_at})
    return token_obj
    
def get_otp_code(mobile_number, otp_code):
    try:
        return OtpCodes.objects.get(mobile_number=mobile_number, code=otp_code, expire_date__gt=timezone.now())
    except OtpCodes.DoesNotExist:
        return None

def create_or_update_token(mobile_number):
    token, created = OtpToken.objects.get_or_create(mobile_number=mobile_number)
    token.expires = timezone.now() + timedelta(minutes=30)
    token.save()
    return token

def handle_invalid_otp(mobile_number, code):
    code = OtpCodes.objects.get(mobile_number=mobile_number)
    code.error_trials += 1
    code.save()
    if code.error_trials >= 3:
        code.number_of_trials += 1
        code.save()
        raise get_error_response('AUTH_410', {})
    raise get_error_response('AUTH_407', {})
    
def generate_JWT(user, connect_user_id=None, impersonated_by=None):
    if not impersonated_by:
        request = get_current_request()
        token_payload = getattr(request, 'auth', {}) if request else {}
        if token_payload:
            impersonated_by = token_payload.get('impersonated_by', None)
    # Generate JWT token
    now = timezone.now()
    payload = {
        'user_id': user.id,
        'exp': int((now + datetime.timedelta(days=14)).timestamp()),
        'iat': int(now.timestamp()),
        'connect_user_id': connect_user_id
    }

    if connect_user_id is not None:
        payload['connect_user_id'] = connect_user_id

    if impersonated_by is not None:
        payload['impersonated_by'] = impersonated_by

    token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')

    return token

def validate_credentials(mobile_number, password):
    user = authenticate(username=mobile_number, password=password)
    if not user:
        raise get_error_response('AUTH_412', {})
    my_user = User.objects.get(user=user)
    if not my_user.is_active:
        raise get_error_response('AUTH_413', {})
    return user

def extract_username(connect_user):
    country_code = connect_user.country_code
    mobile_number = connect_user.mobile_number
    if mobile_number.startswith("0"):
       mobile_number = mobile_number[1:]
    return country_code + mobile_number


def handle_reseller(token, company, company_conf):
    token_obj = ResellerToken.objects.get(token=token)
    if token_obj.expires_at is not None:
        if token_obj.expires_at < timezone.now():
            return None
    reseller_company = token_obj.reseller_company
    if not reseller_company:
        reseller_company = ResellerCompany.objects.create(reseller=token_obj.reseller, company=company, mobile_number=company.company_mobile, name=company.company_name)
    reseller_company.status = 'Waiting Activation'
    reseller_company.status_code = 'waiting_activation'
    reseller_company.company = company
    reseller_company.save()
    apply_configured_fields(company, company_conf)

def apply_configured_fields(company, conf_values):
    reseller_company = ResellerCompany.objects.filter(company=company).first()
    reseller_company_delivery_companies = ResellerCompanyDeliveryCompany.objects.filter(reseller_company=reseller_company)
    reseller_company_warehouses = ResellerCompanyWarehouse.objects.filter(reseller_company=reseller_company)
    # Set available delivery companies
    for reseller_company_delivery_company in reseller_company_delivery_companies:
        CompanyDeliveryCompany.objects.get_or_create(
            company=company,
            delivery_company=reseller_company_delivery_company.delivery_company
        )
    ResellerCompanyDeliveryCompany.objects.filter(reseller_company=reseller_company).delete()
    # Set available Warehouses
    for reseller_company_warehouse in reseller_company_warehouses:
        CompanyResellerWarehouse.objects.get_or_create(
            company=company,
            warehouse=reseller_company_warehouse.warehouse
        )
    ResellerCompanyWarehouse.objects.filter(reseller_company=reseller_company).delete()
    if reseller_company.default_warehouse:
        CompanyResellerWarehouse.objects.get_or_create(
            company=company,
            warehouse=reseller_company.default_warehouse
        )
        # Set default warehouse connection
        warehouse_connection, created = CompanyWarehouseConnection.objects.get_or_create(
            company=company, 
            warehouse=reseller_company.default_warehouse
        )
        warehouse_connection.connection_status = 'connected'
        warehouse_connection.save()
        conf_values.default_warehouse = reseller_company.default_warehouse
        conf_values.save()
