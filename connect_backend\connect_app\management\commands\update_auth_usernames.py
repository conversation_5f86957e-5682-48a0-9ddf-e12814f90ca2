from django.core.management.base import BaseCommand
from django.db import transaction
from api.users.user_model import User

class Command(BaseCommand):
    help = "Update auth_user.username based on api_user.country_code and mobile_number"

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.NOTICE("Starting username update..."))

        with transaction.atomic():
            users_to_update = User.objects.all()
            updated_count = 0

            for api_user in users_to_update:
                auth_user = api_user.user  # Reference to auth_user

                if auth_user and api_user.country_code and api_user.mobile_number:
                    new_username = f"{api_user.country_code}{api_user.mobile_number[1:]}"
                    
                    if auth_user.username != new_username:  # Avoid unnecessary updates
                        auth_user.username = new_username
                        auth_user.save(update_fields=["username"])
                        updated_count += 1

            self.stdout.write(self.style.SUCCESS(f"Updated {updated_count} usernames."))

        self.stdout.write(self.style.SUCCESS("Username update completed."))
