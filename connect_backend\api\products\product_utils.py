from ..logs.logs_model import *
from .product_model import *
from ..util_functions import * 
from django.db import transaction
from ..category.category_utils import *
from django.utils.translation import gettext as _
from uuid import uuid4
from django.db.models import Count, Q

def create_log(model, company, action, created_by, model_id):
    return Logs.objects.create(
        model=model,
        company=company,
        action=action,
        created_by=created_by,
        model_id=model_id
    )

def create_log_info(log, field_name, old_value, new_value):
    return LogsInfo.objects.create(
        log=log,
        field_name=field_name,
        old_value=old_value,
        new_value=new_value
    )

def log_tracked_fields(log, values, fields):
    for key, value in values.items():
        if key in fields:
            if isinstance(value, models.Model):
                if key == 'company':
                    value = value.company_name
                else:
                    value = value.name
            create_log_info(log, key, None, value)

def delete_log_if_no_changes(log):
    if not LogsInfo.objects.filter(log=log).exists():
        log.delete()

def create_product(values, product_prices):
    with transaction.atomic():
        
        category = None
        tags = None

        if 'category' in values:
            category = values.pop('category')

        if 'tags' in values:
            tags = values.pop('tags')

        if 'images' in values and  values['images'] != "":
            images = values.pop('images')
            product = Product.objects.create(**values)
            images = create_products_image(product, images, values["company"], values["updated_by"])
        else:
            product = Product.objects.create(**values)
            
        if tags:
            tags = handle_tags(tags,values['company'])
            for tag in tags:
                product.tags.add(tag)

        if category:
            try:
                category = get_category(category, values["company"])
                product.category = category
                product.save()
            except Category.DoesNotExist:
                raise ValidationError(_('Category not found'))
        
        log = create_log('Product', values['company'], 'create', values['created_by'], product.id)
        fields = get_tracked_fields(values['company'], 'Product')
        log_tracked_fields(log, values, fields)
        delete_log_if_no_changes(log)
        create_product_prices(product, product_prices, values['updated_by'])
        return product
    
def get_category(input, company):
    if is_number(input):
        return get_categor_by_id(input)
    else:
        return Category.objects.get(name=input, company=company, active=True)

def handle_tags(tags,company):
    tags_instance = []
    for tag in tags:
        tag , created= Tags.objects.get_or_create(name=tag,company=company)
        tags_instance.append(tag)
    return tags_instance

def validate_product(product, company, required_fields):
    validation_errors = []
    foreign_key_objects = {}

    for field in required_fields:
        if not product.get(field):
            validation_errors.append(f'Missing field: {field}')
            
    if not product.get('reference_sequence', ''):
        product['reference_sequence'] = str(uuid4())

    numeric_fields = ['price', 'cost']
    for key in numeric_fields:
        value = product.get(key)
        if value is not None:
            try:
                product[key] = float(value) if '.' in str(value) else int(value)
                if product[key] < 0:
                    validation_errors.append(f'{key} should be positive')
            except ValueError:
                validation_errors.append(f'{key} should be a positive number')

    description = product.get('description')
    if description and len(description) > 500:
        validation_errors.append('Description should not exceed 500 characters')

    foreign_key_objects = get_product_foreign_keys(product, validation_errors, company)
    if product.get('reference_sequence'):
        existing_products = Product.objects.filter(company=company, reference_sequence=product.get('reference_sequence'), active=True).exclude(id=product.get('id', None))
        if existing_products.exists():
            raise get_error_response('PRODUCT_503', {'reference_sequence': product.get('reference_sequence'), 'product_name': existing_products[0].name})


    return validation_errors, foreign_key_objects

def get_product_foreign_keys(values, validation_errors, company):
    foreign_key_objects = {}
    if values.get('warehouses', []):
        foreign_key_objects['warehouses'] = Warehouse.objects.filter(id__in=values.get('warehouses', []))
    else:
        conf = get_company_conf(company)
        foreign_key_objects['warehouses'] = [conf.default_warehouse]
        
    if values.get('category'):
        try:
            foreign_key_objects['category'] = get_category(values['category'], company).id
        except Category.DoesNotExist:
            validation_errors.append(_("Category not found"))

    return foreign_key_objects

def build_product_values(values, foreign_key_objects):
    values = {**values, **foreign_key_objects}
    return values

def get_product(id):
    try:
        product = Product.objects.get(id=id)
        return product
    except Product.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Product', 'field': 'id', 'value': id})

def update_product_util(values, product_images, product_prices):
    category = None
    tags = None

    if 'category' in values:
        category = values.pop('category')

    if 'tags' in values:
        tags = values.pop('tags')

    warehouses = values.pop('warehouses', [])

    product = get_product(values['id'])
    if not product:
        return None
    try:
        with transaction.atomic():
            log = create_log('Product', values['company'], 'update', values['updated_by'], product.id)
            update_product_fields(product, values, log)
            
            if category:
                try:
                    category = get_categor_by_id(category)
                    product.category = category
                    product.save()
                except Category.DoesNotExist:
                    raise ({'success': False,'error': 'Category not found'})
                
            if tags:
                tags = handle_tags(tags,values['company'])
            
            if tags:
                old_tags = product.tags.all() 
                for tag in tags:
                    if tag not in old_tags:
                        product.tags.add(tag)

                for old_tag in old_tags:
                    if old_tag not in tags:
                        product.tags.remove(old_tag)

            delete_log_if_no_changes(log)
            create_product_prices(product, product_prices, values['updated_by'])
            handle_product_images(values, product_images, product)
            product.save()
            return product
    except Exception as e:
        raise ValidationError(f"Error updating product: {str(e)}")


def update_product_fields(product, values, log):
    fields = get_tracked_fields(product.company, 'Product')

    for key, value in values.items():
        old_value = getattr(product, key, None)
        if old_value != value:
            log_and_update_field(product, key, value, old_value, log, bool(key in fields))

def validate_and_prepare_prices(product_price):
    validation_errors = []
    item_foreign_key_objects = {}
    new_items = set()

    for index, item in enumerate(product_price):
        errors, fk_objects = validate_product_price(item)
        validation_errors.extend(errors)

        item_id = str(item.get('id', ''))

        if not item_id:
            temp_key = f'new_item_{index}'
            new_items.add(temp_key)
            item_foreign_key_objects[temp_key] = fk_objects
        else:
            if item_id.isdigit():
                item_foreign_key_objects[item_id] = fk_objects
            else:
                validation_errors.append(f'Invalid item ID: {item_id}')

    return validation_errors, item_foreign_key_objects, new_items

def validate_product_price(product_price):
    validation_errors = []
    foreign_key_objects = {}
    
    required_fields = ['country', 'price']
    for field in required_fields:
        if not product_price.get(field):
            validation_errors.append(f'Missing field: {field}')

    foreign_key_objects['country'] = get_country(product_price, validation_errors)
    
    return validation_errors, foreign_key_objects

def create_product_prices(product, product_prices, name):
    validation_errors, item_foreign_key_objects, new_items = validate_and_prepare_prices(product_prices)

    if validation_errors:
        raise ValidationError(validation_errors)

    with transaction.atomic():
        existing_item_ids = get_existing_prices_ids(product)
        item_ids = set(item_foreign_key_objects.keys())
        ids_to_delete = existing_item_ids - item_ids

        if ids_to_delete:
            delete_product_prices(product, ids_to_delete, product.company, name)

        process_prices(product, product_prices, item_foreign_key_objects, new_items, name)

def get_existing_prices_ids(product):
    return set(map(str, ProductPrice.objects.filter(product=product).values_list('id', flat=True)))

def delete_product_prices(product ,item_ids, company, name):
    for item_id in item_ids:
        product_price = ProductPrice.objects.get(id=item_id)
        log = create_log('Product', company, f'REMOVE&{product_price.country.name}&{product_price.price}', name, product.id)
        product_price.delete()

def process_prices(product, product_prices, item_foreign_key_objects, new_items, name):
    for item_id, fk_objects in item_foreign_key_objects.items():
        if item_id.startswith('new_item_'):
            create_product_price(product, product_prices, item_id, fk_objects, name)
        else:
            update_product_price(item_id, product, product_prices, fk_objects, name)

def create_product_price(product, product_prices, item_id, fk_objects, name):
    index = int(item_id.split('_')[-1])
    item = product_prices[index]
    values = build_product_prices_values(item, fk_objects, product)
    product_price = ProductPrice.objects.create(**values)
    log = create_log('ProductPrice', product.company, 'create', name, product_price.id)
    log_tracked_fields(log, values, get_tracked_fields(product.company, 'ProductPrice'))
    delete_log_if_no_changes(log)

def build_product_prices_values(product_prices, foreign_key_objects, product):
    return {
        'country': foreign_key_objects.get('country'),
        'price': product_prices.get('price'),
        'product': product
    }

def update_product_price(item_id, product, product_prices, fk_objects, name):
    try:
        product_price = ProductPrice.objects.get(id=item_id)
    except ProductPrice.DoesNotExist:
        raise ValidationError([f'ProductPrice with ID {item_id} does not exist.'])

    item = next((item for item in product_prices if str(item.get('id')) == item_id), None)
    if item is None:
        raise ValidationError([f'Item with ID {item_id} not found in the provided items.'])

    values = build_product_prices_values(item, fk_objects, product)
    log = create_log('ProductPrice', product.company, 'update', name, item_id)

    for key, value in values.items():
        old_value = getattr(product_price, key, None)
        if old_value != value:
            log_and_update_field(product_price, key, value, old_value, log, bool(key in get_tracked_fields(product.company, 'ProductPrice')))
    
    product_price.save()
    delete_log_if_no_changes(log)

def get_image(id):
    return ProductsImage.objects.get(id=id)

def delete_images_util(product, item_ids, company, name):
    for item_id in item_ids:
        try:
            product_image = ProductsImage.objects.get(id = item_id)
            log = create_log('Product', company, 'delete images', name, product["id"])
            delete_log_if_no_changes(log)
            product_image.delete()
        except Exception as e:
            raise e

def create_products_image(product, images, company, name):
    for image_item in images:
        try:

            image = ProductsImage.objects.create(
                product_image_url = image_item["product_image_url"],
                product = product
            )
            image.save()
            log = create_log('Product', company, 'create', name, image.id)
            delete_log_if_no_changes(log)
        except Exception as e:
            raise e
    return images

def validate_product_variant(values):
    validation_errors = []
    foreign_key_objects = {}

    required_fields = ['product']
    for field in required_fields:
        if not values.get(field):
            validation_errors.append(f'Missing field: {field}')
            
    if not values.get('sku', ''):
        sku = str(uuid4())
        values['sku'] = sku

    foreign_key_objects.update(update_variant_foreign_keys(values))
    product = foreign_key_objects.get('product', None)
    if values.get('sku') and product and ProductVariant.objects.filter(product__company=product.company, sku=values.get('sku'), product__active=True).exclude(id=values.get('id', None)).exists():
        existing_products = ProductVariant.objects.filter(product__company=product.company, sku=values.get('sku'), product__active=True).exclude(id=values.get('id', None))
        raise get_error_response('PRODUCT_501', {'sku': values.get('sku'), 'product_name': ','.join([variant.name for variant in existing_products])})

    return validation_errors, foreign_key_objects

def update_variant_foreign_keys(values):
    foreign_key_objects = {}

    if 'product' in values:
        foreign_key_objects['product'] = get_product(values.get('product'))

    if 'attributes' in values:
        attributes = []
        for attribute in values.get('attributes'):
            attribute = get_attribute(attribute)
            attributes.append(attribute)

        foreign_key_objects['attributes'] = attributes

    if values.get('warehouses', []):
        foreign_key_objects['warehouses'] = Warehouse.objects.filter(id__in=values.get('warehouses', []))
    else:
        conf = get_company_conf(foreign_key_objects.get('product').company)
        foreign_key_objects['warehouses'] = [conf.default_warehouse]

    return foreign_key_objects

def get_attribute(id):
    try:
        return AttributeValue.objects.get(id=id)
    except Attribute.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'AttributeValue', 'field': 'id', 'value': id})
    
def create_variant(values, attributes):
    try:
        with transaction.atomic():
            warehouses = values.pop('warehouses', [])
            variant = ProductVariant.objects.create(**values)
            variant.warehouses.set(warehouses)
            log = create_log('ProductVariant', variant.product.company, 'create', values['created_by'], variant.id)
            log_tracked_fields(log, values, get_tracked_fields(variant.product.company, 'ProductVariant'))
            for attribute_value in attributes:
                VariantAttribute.objects.create(variant=variant, value=attribute_value)
                create_log_info(log, 'attributes', attribute_value, None)
            delete_log_if_no_changes(log)
            return variant
    except Exception as e:
        raise get_error_response('GENERAL_007', {'error': str(e)})
    
def update_variant_util(values, attributes):
    variant = get_variant(values['id'])
    if not variant:
        return None
    with transaction.atomic():
        warehouses = values.pop('warehouses', [])
        log = create_log('ProductVariant', variant.product.company, 'update', values['updated_by'], variant.id)
        update_variant_fields(variant, values, log)
        current_warehouses = variant.warehouses.all()
        current_attributes = set(variant.attributes.values_list('value', flat=True))
        attributes_set = set(attr.id for attr in attributes)
        to_add = attributes_set - current_attributes
        to_remove = current_attributes - attributes_set
        for attribute_value in to_add:
            VariantAttribute.objects.create(variant=variant, value_id=attribute_value)
            create_log_info(log, 'attributes', None, attribute_value)
        for attribute_value in to_remove:
            VariantAttribute.objects.filter(variant=variant, value_id=attribute_value).delete()
            create_log_info(log, 'attributes', attribute_value, None)
        warehouses_set = set(warehouse.id for warehouse in current_warehouses)
        new_warehouses = set(warehouse.id for warehouse in warehouses)
        to_add = new_warehouses - warehouses_set
        to_remove = warehouses_set - new_warehouses
        for warehouse in to_remove:
            variant_to_remove = WarehouseVariant.objects.get(variant=variant, warehouse_id=warehouse)
            if variant_to_remove.physical_quantity != 0 and variant_to_remove.virtual_quantity != 0:
                raise get_error_response('WAREHOUSE_1001', {'variant': variant_to_remove})
            WarehouseVariant.objects.filter(variant=variant, warehouse_id=warehouse).delete()
        for warehouse in to_add:
            WarehouseVariant.objects.create(variant=variant, warehouse_id=warehouse)
        variant.warehouses.set(warehouses)
        delete_log_if_no_changes(log)
        variant.save()
        return variant
    
def get_variant(variant_id):
    try:
        return ProductVariant.objects.get(id=variant_id)
    except ProductVariant.DoesNotExist:
        raise ValidationError(f'Variant with ID {variant_id} does not exist.')
    
def update_variant_fields(variant, values, log):
    fields = get_tracked_fields(variant.product.company, 'ProductVariant')

    for key, value in values.items():
        old_value = getattr(variant, key, None)
        if old_value != value:
            log_and_update_field(variant, key, value, old_value, log, bool(key in fields))

def handle_product_images(values, product_images, product):
    company = values["company"]
    with transaction.atomic():
        existing_item_ids = get_existing_product_images_ids(values)
        item_ids = {str(item['id']) for item in product_images if 'id' in item}
        ids_to_delete = existing_item_ids - item_ids

        if ids_to_delete:
            delete_images_util(values, ids_to_delete, company, values["updated_by"])

        # Filter out images without an "id"
        new_images = [item for item in product_images if item.get('id') in [None, 0, '']]
        return create_products_image(product, new_images, company, values["updated_by"])

def get_existing_product_images_ids(product):
    product_id = product['id'] 
    return set(map(str, ProductsImage.objects.filter(product=product_id).values_list('id', flat=True)))

def process_excel_products(file, user):
    header_mapping = {
        'name': 'name',
        'cost': 'cost',
        'price': 'price',
        'description': 'description',
        'reference_sequence': 'reference_sequence',
        'category': 'category',
        'Size Kids': 'Size Kids',
        'Size SML': 'Size SML',
        'النوع': 'النوع',
        'اللون': 'اللون',
        'الحجم': 'الحجم',
        'sku': 'sku',
    }
    
    required_fields = ['name', 'cost', 'price', 'category', 'reference_sequence']
    df = read_and_validate_excel(file, header_mapping, required_fields)
    products, errors, warnings = process_products_from_df(df, user, header_mapping)
    return (products, errors, warnings) if not errors else (None, errors, warnings)

def process_products_from_df(df, user, header_mapping):
    products, errors, warnings = [], [], []
    grouped_products = {}
    skus = {}
    references = {}
    last_reference = None

    for index, row in df.iterrows():
        row_data = row.to_dict()
        reference = row_data.get("reference_sequence", '')
        name = row_data.get('name')
            
        if not reference or pd.isna(reference):
            if name and not pd.isna(name):
                reference = str(uuid4())
                row_data['reference_sequence'] = str(reference)
                last_reference = reference
            elif not last_reference:
                errors.append({
                    'index': index + 2,
                    'errors': [_("Missing 'reference_sequence' and no previous product to attach this variant.")]
                })
                continue

            reference = last_reference
        else:
            if reference in references:
                previous_index = references[reference]
                errors.append({
                    'index': index + 2,
                    'errors': [_(
                        "Reference '{reference}' is duplicated in the Excel file. First seen at line {line}."
                    ).format(reference=reference, line=previous_index + 2)]
                })
                continue
            last_reference = reference

        if reference not in grouped_products:
            product_data = row_data.copy()
            product_data = normalize_product_data(product_data, user)

            for key in ['الحجم', 'اللون', 'النوع', 'Size SML', 'Size Kids']:
                product_data.pop(key, None)
            product_data.pop('sku', None)

            product_data['variants'] = []
            grouped_products[reference] = product_data
            references[reference] = index
        else:
            product_data = grouped_products[last_reference]

        variant, line_errors = process_excel_variant(row_data, user, index, skus)
        if variant:
            product_data['variants'].append(variant)

        if line_errors:
            errors.append({'index': index + 2, 'errors': line_errors})

    for reference, product_data in grouped_products.items():
        required_fields = ['name', 'cost', 'price', 'category']
        validation_errors, foreign_key_objects = validate_product(
            product_data, user.company, required_fields
        )
        product_data = {**product_data, **foreign_key_objects, 'company': user.company}
        product_data.pop('warehouses', None)
        if reference in references:
            first_occurrence_line = references[reference] + 2
            duplicates = [
                idx for idx, ref in enumerate(df['reference_sequence']) 
                if ref == reference and idx != references[reference]
            ]
            if duplicates:
                validation_errors.append(
                    _("Reference '{reference}' is duplicated in the Excel file. First seen at line {line}.").format(
                        reference=reference, line=first_occurrence_line
                    )
                )

        if validation_errors:
            errors.append({'index': references[reference] + 2, 'errors': validation_errors})
            continue

        products.append(product_data)

    return products, errors, warnings

def normalize_product_data(product_data, user):
    if product_data.get('created_by', None) and not pd.isna(product_data.get('created_by')):
        product_data['created_by'] = product_data.get('created_by')
        product_data['updated_by'] = product_data['created_by']
    else:
        product_data['created_by'] = user.name
        product_data['updated_by'] = user.name 
    
    for key, value in product_data.items():
        if pd.isna(value):
            if key in ['price', 'cost']:
                product_data[key] = 0
            else:
                product_data[key] = None
    
    return product_data

def process_excel_variant(row_data, user, index, skus):
    line_errors = []
    attributes = {}
    variant_data = {
        'attributes': [],
        'sku': None,
        'created_by': user.name,
        'updated_by': user.name,
    }
    sku = row_data.get('sku', None)
    if sku and not pd.isna(sku):
        if ProductVariant.objects.filter(product__company=user.company, sku=sku).exists():
            error = get_error_response('PRODUCT_501', {'sku': sku, 'product_name': ProductVariant.objects.filter(product__company=user.company, sku=sku).first().name}).to_dict()
            line_errors.append(error.get('error').get('message'))
        elif sku in skus:
            first_occurrence_line = skus[sku][0] + 2
            line_errors.append(
                _("SKU '{sku}' is duplicated in the Excel file. First seen at line {line}.").format(
                    sku=sku, line=first_occurrence_line
                )
            )
        else:
            skus[sku] = [index]
        variant_data['sku'] = sku
    else:
        sku = str(uuid4())
        skus[sku] = [index]
        variant_data['sku'] = sku
    for key in ['الحجم', 'اللون', 'النوع', 'Size SML', 'Size Kids']:
        attributes[key] = Attribute.objects.get(name=key)
    for key in ['الحجم', 'اللون', 'النوع', 'Size SML', 'Size Kids']:
        value = row_data.get(key, None)
        if value and not pd.isna(value):
            value = str(value).strip()
            try:
                attribute_value = AttributeValue.objects.get(attribute=attributes.get(key), value__iexact=value, company=user.company)
                variant_data['attributes'].append(attribute_value)
            except (AttributeValue.DoesNotExist, AttributeValue.MultipleObjectsReturned) as e:
                line_errors.append(
                    _("Invalid attribute value '{value}' for attribute '{key}' on line {line}.").format(
                        value=value, key=key, line=index + 2
                    )
                )
    if not variant_data['attributes']:
        variant_data = None
    return variant_data, line_errors

def check_duplicate_variants(product,attribute_ids):
    duplicate_variants = product.variants.annotate(
        attr_count=Count('attributes__value', distinct=True),
        match_count=Count('attributes__value', filter=Q(attributes__value__in=attribute_ids), distinct=True)
    ).filter(
        attr_count=len(attribute_ids),
        match_count=len(attribute_ids)
    )
    return duplicate_variants.exists();
