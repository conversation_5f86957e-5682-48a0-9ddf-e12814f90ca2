# Add this import at the top of your settings file
from datetime import timed<PERSON>ta

from pathlib import Path
from corsheaders.defaults import default_headers

import os
from dotenv import load_dotenv

load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

import os

# Media settings
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

BASEURL = os.getenv('BASEURL')
FRONT_BASEURL = os.getenv('FRONT_BASEURL')

#Stripe Keys
STRIPE_TEST_PUBLIC_KEY = os.getenv('STRIPE_TEST_PUBLIC_KEY')
STRIPE_TEST_SECRET_KEY = os.getenv('STRIPE_TEST_SECRET_KEY')
STRIPE_WEBHOOK_SECRET = os.getenv('STRIPE_WEBHOOK_SECRET')

LAHZA_TEST_PUBLIC_KEY = os.getenv('LAHZA_TEST_PUBLIC_KEY')
LAHZA_TEST_SECRET_KEY = os.getenv('LAHZA_TEST_SECRET_KEY')
RELEASE_NOTE_AI_DOMAIN = os.getenv('RELEASE_NOTE_AI_DOMAIN')
RELEASE_NOTE_AI_TOKEN = os.getenv('RELEASE_NOTE_AI_TOKEN')

# One Signal Keys
ONESIGNAL_APP_ID = os.getenv('ONESIGNAL_APP_ID')
ONESIGNAL_API_KEY = os.getenv('ONESIGNAL_API_KEY')

CONVERSION_RATE_KEY = os.getenv('CONVERSION_RATE_KEY')

CONVERT_RATE_API_URL = f"https://v6.exchangerate-api.com/v6/{CONVERSION_RATE_KEY}/latest" 

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/3.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-q+kbglrevlo4wn^8wnwqttlt91w+wnad=+wy8!q3po=&^wmcf9'

DATABASE_NAME = 'connect-app'

# Update the REST_FRAMEWORK setting to include token authentication
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'rest_framework.authentication.SessionAuthentication',  # You can also keep SessionAuthentication if needed
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.AllowAny',
    ],
    'EXCEPTION_HANDLER': 'api.exceptions.custom_exception_handler',
}
DEBUG = True

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
}

# Update the INSTALLED_APPS setting to include 'rest_framework.authtoken'
# Application definition
ALLOWED_HOSTS = ['*']

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.postgres',
    'rest_framework',
    'rest_framework.authtoken',
    'api',
    'connect_app',
    'corsheaders',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'api.middleware.DynamicCORSOriginMiddleware',
    'api.middleware.LanguageMiddleware',
    'api.middleware.ConnectErrorMiddleware',
    'api.middleware.ThreadLocalMiddleware',
    'django.middleware.locale.LocaleMiddleware',
]

ROOT_URLCONF = 'connect_app.urls'

LOCALE_PATHS = [
    os.path.join(BASE_DIR, 'locale'),
]

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'connect_app.wsgi.application'

# Database
# https://docs.djangoproject.com/en/3.2/ref/settings/#databases

DATABASES = {

    'default': {
        'ENGINE': os.getenv('DB_ENGINE', 'django.db.backends.sqlite3'),  # Default to SQLite if not set
        'NAME': os.getenv('DB_NAME', 'mydefaultdb'),
        'USER': os.getenv('DB_USER', 'mydefaultuser'),
        'PASSWORD': os.getenv('DB_PASSWORD', 'mydefaultpassword'),
        'HOST': os.getenv('DB_HOST', 'localhost'),  # Default host
        'PORT': os.getenv('DB_PORT', '5432'),  # Default PostgreSQL port
    }

}


# Password validation
# https://docs.djangoproject.com/en/3.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/3.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

LANGUAGES = [
    ('en-us', 'English'),
    ('ar', 'Arabic'),
]

TIME_ZONE = 'UTC'

USE_I18N = True

USE_L10N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/3.2/howto/static-files/

STATIC_URL = '/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/3.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Add token expiration time (optional)
REST_FRAMEWORK['DEFAULT_AUTHENTICATION_CLASSES'] = [
    'api.auth.auth_model.CustomJWTAuthentication',
]
REST_FRAMEWORK['DEFAULT_RENDERER_CLASSES'] = [
    'rest_framework.renderers.JSONRenderer',
]
REST_FRAMEWORK['DEFAULT_PARSER_CLASSES'] = [
    'rest_framework.parsers.JSONParser',
]
REST_FRAMEWORK['DEFAULT_PERMISSION_CLASSES'] = [
    'rest_framework.permissions.IsAuthenticated',
]
REST_FRAMEWORK['DEFAULT_THROTTLE_CLASSES'] = [
    'rest_framework.throttling.AnonRateThrottle',
    'rest_framework.throttling.UserRateThrottle',
]
REST_FRAMEWORK['DEFAULT_THROTTLE_RATES'] = {
    'anon': '100000000/day',
    'user': '100000000/day',
}
AUTHENTICATION_BACKENDS = [
    'api.mystore.users.backends.StoreUserBackend',  # Custom backend for StoreUser model
    'django.contrib.auth.backends.ModelBackend',  # Default authentication for User model
]

# AWS S3 configurations
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME')
AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME')