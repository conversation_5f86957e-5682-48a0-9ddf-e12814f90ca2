from django.urls import path
from .warehouse_apis import *

urlpatterns = [
    path('add_warehouse', add_warehouse, name='add_warehouse'),
    path('get_warehouses', get_warehouses, name='get_warehouses'),
    path('get_available_warehouses', get_available_warehouses, name='get_available_warehouses'),
    path('connect_with_warehouse', connect_with_warehouse, name='connect_with_warehouse'),
    path('get_warehouse_variants', get_warehouse_variants, name='get_warehouse_variants'),
    path('get_warehouse_variants_store', get_warehouse_variants_store, name='get_warehouse_variants_store'),
    path('update_warehouse_stock', update_warehouse_stock, name='update_warehouse_stock'),
    path('get_stock_approval', get_stock_approval, name='get_stock_approval'),
    path('update_stock_approval', update_stock_approval, name='update_stock_approval'),
    path('get_warehouse_logs', get_warehouse_logs, name='get_warehouse_logs'),
    path('get_dashboard', get_dashboard, name='get_dashboard'),
    path('import_excel', import_excel, name='import_excel'),
    path('add_stocktaking', add_stocktaking, name='add_stocktaking'),
    path('get_stocktaking', get_stocktaking, name='get_stocktaking'),
    path('create_location', create_location, name='create_location'),
    path('get_locations', get_locations, name='get_locations'),
    path('print_location', print_location, name='print_location'),
    path('add_locations_to_products', add_locations_to_products, name='add_locations_to_products'),
    path('get_locations_with_products', get_locations_with_products, name='get_locations_with_products'),
    path('upload_location_excel', upload_location_excel, name='upload_location_excel'),
    path('upload_location_excel_with_products', upload_location_excel_with_products, name='upload_location_excel_with_products'),
    path('change_products_location', change_products_location, name='change_products_location'),
    path('get_location_products', get_location_products, name='get_location_products'),
]