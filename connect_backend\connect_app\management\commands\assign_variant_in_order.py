from django.core.management.base import BaseCommand
from api.products.product_model import OrderLine
from api.warehouse.warehouse_model import WarehouseVariant

class Command(BaseCommand):
    help = 'Set variants in order lines where the product has at least one variant'

    def handle(self, *args, **kwargs):
        order_lines = OrderLine.objects.all()
        updated_count = 0

        for order_line in order_lines:
            product = order_line.product
            variant = product.variants.first()
            warehouse_variant = WarehouseVariant.objects.get(variant=variant)
            
            if variant:
                order_line.product_variant = warehouse_variant
                order_line.save()
                updated_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f"Updated OrderLine {order_line.id} with variant {variant.id} for product {product.id}")
                )
            else:
                self.stdout.write(
                    self.style.WARNING(f"Skipped OrderLine {order_line.id} as product {product.id} has no variants")
                )

        self.stdout.write(self.style.SUCCESS(f"Total OrderLines updated: {updated_count}"))
