from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import *
from ...util_functions import *
from ...permissions import *
from ...dashboard.dashboard_utils import *
from .admin_dashboard_utils import *
from django.utils.timezone import make_aware

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_dashboard(request):
    try:
        json_data = parse_request_body(request)
        if not json_data and json_data !={}:
            raise get_error_response('GENERAL_001',{})

        context = {
            'total_orders': get_total_orders(),
            'orders_last_three_months': get_orders_last_three_months(),
            'total_companies':get_total_companies(),
            'active_companies':get_active_companies(),
            'not_active_companies':get_not_active_companies(),
            'total_delivery_companies':get_total_delivery_companies(),
            'subscribed_companies': get_subscribed_companies(),
        }

        return JsonResponse({'success': True, 'context': context}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)



