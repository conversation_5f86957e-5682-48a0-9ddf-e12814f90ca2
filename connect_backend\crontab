# Example of a cron job: Run a script every hour
*/20 * * * * export DB_PORT=5432 && /usr/local/bin/python /code/manage.py update_status_cron >> /var/log/cron.log 2>&1
0 4 * * * export DB_PORT=5432 && /usr/local/bin/python /code/manage.py send_subscription_notifications >> /var/log/cron.log 2>&1
0 4 * * * export DB_PORT=5432 && /usr/local/bin/python /code/manage.py send_delayed_order_notification >> /var/log/cron.log 2>&1
0 3 * * 4 export DB_PORT=5432 && /usr/local/bin/python /code/manage.py complete_opened_orders >> /var/log/cron.log 2>&1
0 23 * * * export DB_PORT=5432 && /usr/local/bin/python /code/manage.py calculate_reseller_wallet >> /var/log/cron.log 2>&1


