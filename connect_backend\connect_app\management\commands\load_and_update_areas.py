import json
from django.core.management import BaseCommand, call_command
from django.apps import apps
from django.db import transaction, models
from api.lookups.lookup_model import Area, SubArea, AreaGroup, Country
from api.serializers import *

class Command(BaseCommand):
    help = 'Update areas, subareas, and area groups from fixtures and update references'

    def handle(self, *args, **kwargs):
        # Load fixtures
        fixtures = [
            'fixtures/pal_areas.json',
            'fixtures/pal_subareas.json',
            'fixtures/jod_areas.json',
            'fixtures/jod_subareas.json'
        ]

        areas_data = {}
        subareas_data = {}

        # Load the new data
        for fixture in fixtures:
            with open(fixture) as f:
                data = json.load(f)
                for item in data:
                    if item['model'] == 'api.area':
                        # Initialize the key if it doesn't exist
                        if item['fields']['name'] not in areas_data:
                            areas_data[item['fields']['name']] = []
                        areas_data[item['fields']['name']].append(item)
                        
                    elif item['model'] == 'api.subarea':
                        # Initialize the key if it doesn't exist
                        if item['fields']['name'] not in subareas_data:
                            subareas_data[item['fields']['name']] = []
                        subareas_data[item['fields']['name']].append(item)
                        
                    elif item['model'] == 'api.areagroup':
                        country_instance = Country.objects.get(pk=item['fields']['country'])
                        AreaGroup.objects.update_or_create(
                            id=item['pk'],
                            defaults={'name': item['fields']['name'], 'country': country_instance}
                        )

        # Start a transaction to ensure data integrity
        with transaction.atomic():
            self.update_references_to_new_areas(areas_data)
            self.update_references_to_new_subareas(subareas_data)

            confirm = input("Do you want to commit these changes? (yes/no): ")
            if confirm.lower() == 'yes':
                self.load_data_from_fixtures(fixtures)
            else:
                print("Changes have not been committed.")
                raise Exception("Changes have not been committed")

    def update_references_to_new_areas(self, areas_data):

        # Update any ForeignKey references in other models
        for model in apps.get_models():
            if model == SubArea:
                continue
            for field in model._meta.get_fields():
                if isinstance(field, models.ForeignKey) and field.related_model == Area:
                    for instance in model.objects.all():
                        current_area_id = getattr(instance, field.name)
                        if current_area_id:
                            if current_area_id.name in areas_data:
                                new_area_list = areas_data[current_area_id.name]
                                for area in new_area_list:
                                    if area['fields']['country'] == current_area_id.country.id:
                                        try:
                                            dummy_area = Area.objects.get(id=area['pk'])
                                        except Exception as e:
                                            dummy_area = Area.objects.create(id=area['pk'], name='dummy', code=area['pk'], country=current_area_id.country)
                                        setattr(instance, field.name, dummy_area)
                                        instance.save()
                            else:
                                serializer = MODEL_SERIALIZER_MAP[model]
                                model_serialzied = serializer(instance).data
                                new_id = input(f"{model_serialzied} has {current_area_id.name} that doesn't exist in new areas, input new area id: ")
                                try:
                                    new_area = Area.objects.get(id=int(new_id))
                                except Area.DoesNotExist:
                                    new_area = Area.objects.create(id=int(new_id), name='dummy', code=new_id, country=current_area_id.country)
                                areas_data[current_area_id.name] = []
                                areas_data[current_area_id.name].append({
                                    'pk': new_id,
                                    'fields': {
                                        'name': self.search_area_by_pk(areas_data, new_id),
                                        'code': new_id,
                                        'country': new_area.country.id
                                    }
                                })
                                setattr(instance, field.name, new_area)
                                instance.save()

    def search_area_by_pk(self, areas_data, pk):
        pk = int(pk)
        for area_list in areas_data.values():
            for area in area_list:
                if area['pk'] == pk:
                    return area['fields']['name']
        return 'dummy'

    def update_references_to_new_subareas(self, subareas_data):

        for model in apps.get_models():
            for field in model._meta.get_fields():
                if isinstance(field, models.ForeignKey) and field.related_model == SubArea:
                    for instance in model.objects.all():
                        current_subarea_id = getattr(instance, field.name)
                        if current_subarea_id:
                            if current_subarea_id.name in subareas_data:
                                new_subarea_list = subareas_data[current_subarea_id.name]
                                for subarea in new_subarea_list:
                                    if subarea['fields']['area'] == current_subarea_id.area.id:
                                        try:
                                            dummy_subarea = SubArea.objects.get(id=subarea['pk'])
                                        except SubArea.DoesNotExist:
                                            dummy_subarea = SubArea.objects.create(
                                                id=subarea['pk'], 
                                                name='dummy', 
                                                code=subarea['pk'], 
                                                area=current_subarea_id.area
                                            )
                                        setattr(instance, field.name, dummy_subarea)
                                        instance.save()
                            else:
                                serializer = MODEL_SERIALIZER_MAP[model]
                                model_serialized = serializer(instance).data
                                new_id = input(f"{model_serialized} has {current_subarea_id.name} that doesn't exist in new subareas, input new subarea id: ")
                                try:
                                    new_subarea = SubArea.objects.get(id=new_id)
                                except SubArea.DoesNotExist:
                                    new_subarea = SubArea.objects.create(
                                        id=new_id, 
                                        name='dummy',
                                        code=new_id, 
                                        area=current_subarea_id.area
                                    )
                                subareas_data[current_subarea_id.name] = []
                                subareas_data[current_subarea_id.name].append({
                                    'pk': new_id,
                                    'fields': {
                                        'name': self.search_area_by_pk(subareas_data, new_id),
                                        'code': new_id,
                                        'area': new_subarea.area
                                    }
                                })
                                setattr(instance, field.name, new_subarea)
                                instance.save()

    def load_data_from_fixtures(self, fixtures):
        for fixture in fixtures:
            call_command('loaddata', fixture)
