from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

class SuperModel(models.Model):
    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    created_by = models.CharField(max_length=200, default=None, null=True, editable=False)
    updated_by = models.CharField(max_length=200, default=None, null=True, editable=False)
    created_by_user = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='created_%(class)s_set',
        editable=False
    )

    class Meta:
        abstract = True

    def save(self, *args, **kwargs):
        default_username = 'System'
        if not self.pk:  # Object is being created
            if not self.created_by:
                self.created_by = default_username
        if not self.updated_by:
            self.updated_by = default_username

        super().save(*args, **kwargs)

    def get_authenticated_user(self):
        try:
            from django.contrib.auth import get_user
            from django.http import HttpRequest
            request = HttpRequest()
            user = get_user(request)
            if user.is_authenticated:
                return user
        except:
            pass
        return None