<!DOCTYPE html>

{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %} 

<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_a5.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}


    {% for doc in docs %}
    <div class="waybill-container">
        <div class="header">
            <div class="header-info">
                <div class="info-item">
                    {{doc.company_name}}
                </div>
                <div class="info-item">
                    {{meta.labels.DATE}}:
                    <span class="fw-bold">
                        {{meta.formatted_now}}
                    </span>
                </div>
            </div>
            <div class="header-barcode-container">
                <img class="barcode" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}"
                    alt="">
            </div>
            <div class="business-logo">
                <img src="{{doc.company_logo}}" class="business-logo">
                {% if doc.company_registry %}
                <div class="text-center fw-bold">{{doc.company_registry}}</div>
                {% endif %}
            </div>
        </div>
        <div class="">
            <div class="cards-container">
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.RECIPIENT}}
                    </div>
                    <div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.NAME}}
                            </span>
                            <span class="fw-bold text-truncate">
                                {{doc.order.customer_name}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.MOBILE_NUMBER}}
                            </span>
                            <span class="fw-bold">
                                {{doc.order.customer_mobile}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.SECOND_MOBILE_NUMBER}}
                            </span>
                            <span class="">
                                {{doc.order.customer_second_mobile}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.ADDRESS}}
                            </span>
                            <span class="fw-bold">
                                {{doc.order.area.name}}, 
                                {{doc.order.sub_area.name}},
                                {{doc.order.address}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.CHANNEL}}
                            </span>
                            <span class=" text-truncate">
                                {{doc.order.channel}}
                            </span>
                        </div>
                        <div class="info-block">
                            <span>
                                {{meta.labels.CREATOR}}
                            </span>
                            <span class=" text-truncate">
                                {{doc.order.created_by}}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="card-header-container">
                        <div class="card-header">
                            {{meta.labels.DELIVERY_DETAILS}}
                        </div>

                        <div class="card-barcode-container">
                            <img class="barcode" src="data:image/png;base64,{{ doc.barcode_olivery_base64 }}"
                                alt="">
                        </div>
                    </div>
                    <div>
                        <div class="info-block border-b-grey">
                            <span>
                                {{meta.labels.DELIVERY_COMPANY_NAME}}
                            </span>
                            <span class=" text-truncate">
                                {% if doc.order.connection and doc.order.connection.delivery_company %}
                                {{doc.order.connection.delivery_company.name}}
                                {% else %}
                                
                                {% endif %}
                            </span>
                        </div>
                        <div class="info-block">
                            <span>
                                {{meta.labels.TOTAL_AMOUNT}}
                            </span>
                            <span class="">
                                {{doc.order.total_cod}}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card">
                    <div class="d-flex justify-content-between">
                        <div>{{meta.labels.NOTES}}</div>
                        <div class="notes-content">{{ doc.order.note if doc.order.note is not none else "" }}</div>
                    </div>
                </div>
            </div><div class="cards-container">
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.PRODUCTS}}
                    </div>

                    <div class="products-container">
                        {% if doc.order_lines and doc.order_lines|length > 0 %}
                        <table class="o-table">
                            <thead>
                                <tr>
                                    <th class="item-sequence">
                                        #
                                    </th>
                                    <th class="item-name">
                                        {{meta.labels.PRODUCT_SEQUENCE if doc.print_product_sequence else meta.labels.PRODUCT_NAME}}
                                    </th>
                                    <th class="item-quantity">
                                        {{meta.labels.QUANTITY}}
                                    </th>
                                    <th class="item-price">
                                        {{meta.labels.PRICEـUNIT}}
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in doc.order_lines %}
                                <tr>
                                    <td class="border-b-grey item-sequence">
                                        {{loop.index}}
                                    </td>
                                    <td class="fw-bold border-b-grey item-name">
                                        {{item.product.reference_sequence if doc.print_product_sequence else item.product_variant.variant.name}}
                                    </td>
                                    <td class="fw-bold border-b-grey item-quantity">
                                        X{{item.quantity}}
                                    </td>
                                    <td class="fw-bold border-b-grey item-price">
                                        {{item.price}}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        <div class="info-block total-row">
                            <span>
                                {{meta.labels.TOTAL}}:
                            </span>
                            <span>
                                {{doc.total}}
                            </span>
                        </div>
                        {% else %}
                        <span class="fw-bold">
                            {{doc.order.product_info.replace('\n', '<br>')}}
                        </span>
                        {% endif %}
                    </div>
                </div>
                <div class="need-filling">
                    <div>
                        {{meta.labels.RECIPIENT_NAME_SIGNATURE}}:
                    </div>
                    <div class="dots">
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</body>

</html>