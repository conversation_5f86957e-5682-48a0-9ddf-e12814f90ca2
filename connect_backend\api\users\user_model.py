from django.db import models
from ..lookups.lookup_model import Area, Country
from django.contrib.auth.models import User as DjUser, Group
from ..models import SuperModel
from ..warehouse.warehouse_model import Warehouse
from ..delivery_company.delivery_company_model import DeliveryCompany

class Role(SuperModel):
    name = models.CharField(max_length=200, unique=True)
    role_id = models.AutoField(primary_key=True)
    group = models.ForeignKey(Group, null=True, blank=True, on_delete=models.CASCADE)
    active=models.BooleanField(default=True)
    code = models.CharField(max_length=200, unique=True,null=True)

class Company(SuperModel):
    company_id = models.AutoField(primary_key=True)
    company_name = models.CharField(max_length=200)
    company_mobile = models.CharField(max_length=15)
    company_area = models.ForeignKey(Area, on_delete=models.SET_NULL, null=True)
    stripe_customer_id = models.CharField(max_length=255, blank=True, null=True)
    used_free_trial = models.BooleanField(default=False, null=True)
    free_trial_end_date = models.DateField(null=True, blank=True)
    # ! company_image to be deleted after migration
    company_image = models.ImageField(upload_to='company_images/', null=True, blank=True)
    company_social_link = models.URLField(max_length=200, null=True, blank=True)
    company_image_url = models.CharField(max_length=200, null=True, blank=True)
    follow_up_link = models.CharField(max_length=200, null=True, blank=True)
    follow_up_priorities = models.CharField(
        max_length=20,
        choices=[
            ("LOW", "LOW"),
            ("MEDIUM", "MEDIUM"),
            ("HIGH", "HIGH")
        ],
        default='LOW')    
    follow_up_note = models.CharField(max_length=200, null=True, blank=True)
    owner = models.ForeignKey('User', on_delete=models.SET_NULL, null=True, blank=True, related_name='owned_companies')
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.company_area:
            self.country = self.company_area.country
        super(Company, self).save(*args, **kwargs)

class User(SuperModel):
    user = models.OneToOneField(DjUser,on_delete=models.CASCADE,null=True)
    mobile_number = models.CharField(max_length=15, unique=True)
    # ! user_image to be deleted after migration
    user_image = models.ImageField(upload_to='user_images/', null=True, blank=True)
    user_image_url = models.CharField(max_length=200, null=True, blank=True)
    name = models.CharField(max_length=200)
    email = models.EmailField(null=True, blank=True)
    role = models.ForeignKey(Role,on_delete=models.CASCADE,null=True)
    company = models.ForeignKey(Company,on_delete=models.CASCADE,null=True, related_name='users')
    second_phone = models.CharField(max_length=15, null=True, blank=True)
    address = models.CharField(max_length=200)
    area = models.ForeignKey(Area,on_delete=models.CASCADE,null=True)
    country = models.ForeignKey(Country,on_delete=models.CASCADE,null=True)
    country_code = models.CharField(max_length=6)
    is_active = models.BooleanField(default=False)
    waiting_confirmation = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    player_id = models.CharField(max_length=200, null=True, blank=True)
    lang = models.CharField(max_length=20, null=True, blank=True)
    driver_pricelist = models.ForeignKey('PriceList', on_delete=models.SET_NULL, null=True, related_name='driver_pricelist')


    def save(self, *args, **kwargs):
        if self.user:
            self.user.is_active = self.is_active
            self.user.save()
        if self.user and self.mobile_number and self.country_code:
            user = User.objects.get(id=self.id)
            if self.mobile_number != user.mobile_number:
                country_code = self.country_code
                mobile_number = self.mobile_number
                if mobile_number.startswith("0"):
                    mobile_number = mobile_number[1:]
                username = country_code + mobile_number
                self.user.username = username
                self.user.save()
        super(User, self).save(*args, **kwargs)

class CountriesConf(SuperModel):
    country_ids = models.CharField(max_length=100, null=True, blank=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)

    def set_countries(self, country_ids):
        self.country_ids = ','.join(country_ids)

    def get_countries(self):
        if self.country_ids:
            countries = Country.objects.filter(code__in=self.country_ids.split(','))
            return countries

class CompanyConf(SuperModel):

    from ..category.category_model import Category
    
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    default_delivery_company = models.ForeignKey(DeliveryCompany,on_delete=models.SET_NULL, null=True)
    default_connection = models.ForeignKey('ConnectDeliveryCompany', on_delete=models.SET_NULL, null=True)
    auto_send_orders = models.BooleanField(default=False)
    auto_send_on_status = models.CharField(max_length=200, null=True, blank=True)
    default_country_code = models.CharField(max_length=20, null=True, blank=True)
    default_country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True)
    default_area = models.ForeignKey(Area, on_delete=models.SET_NULL, null=True)
    is_international_business = models.BooleanField(default=False)
    show_page_in_form = models.BooleanField(default=False, null=True)
    default_warehouse = models.ForeignKey(Warehouse, on_delete=models.SET_NULL, null=True)
    use_stock = models.BooleanField(default=False, null=True)
    deduct_virtual_quantity = models.ForeignKey('Status', on_delete=models.SET_NULL, null=True, related_name='virtual_quantity_deductions')
    deduct_physical_quantity = models.ForeignKey('Status', on_delete=models.SET_NULL, null=True, related_name='physical_quantity_deductions')
    raise_error_on_stock = models.BooleanField(default=False, null=True)
    show_reference_in_waybills = models.BooleanField(default=False, null=True)
    show_extra_delivery_fee = models.BooleanField(default=False)
    rounding_up_integration_cod = models.BooleanField(default=False)
    print_product_sequence = models.BooleanField(default=False)
    is_fixed_total = models.BooleanField(default=False)
    order_form_fields = models.CharField(max_length=500, null=True, blank=True, default='channel,payment_method,social_message_link,products,logistics_information')
    order_waybill_templates = models.CharField(max_length=500, null=True, blank=True, default='order_a5,order_10_10,order_10_15,order_75_10,order_a6,order_preparing_collection') 
    order_mobile_card_fields = models.CharField(max_length=500, null=True, blank=True, default='product_info')
    product_note_length = models.IntegerField(null=True, blank=True, default=10)
    order_note_length = models.IntegerField(null=True, blank=True, default=10)
    providers = models.ManyToManyField('Provider')
    enable_driver = models.BooleanField(default=False, null=True)
    critical_stock_number = models.IntegerField(null=True,blank=True, default=0)
    force_send_on_area_mismatch = models.BooleanField(default=False)
    additional_countries = models.ManyToManyField(Country, related_name='company_conf_additional_countries')
    delayed_order_statuses = models.ManyToManyField('StatusMap', related_name="delayed_order_configs")
    days_delayed = models.IntegerField(default=2)
    delivery_request_status = models.ForeignKey('Status', on_delete=models.SET_NULL, null=True, related_name='delivery_request_status')
    products_column_length = models.IntegerField(null=True, blank=True, default=15)
    company_registry = models.CharField(max_length=500, null=True, blank=True)
    default_category = models.ForeignKey(Category, on_delete=models.PROTECT)
    default_pricelist = models.ForeignKey('PriceList', related_name='default_pricelist', on_delete=models.PROTECT, default=2)
    commercial_number = models.CharField(max_length=15, null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.default_connection:
            self.default_delivery_company = self.default_connection.delivery_company
        if ( not self.commercial_number and self.company and self.company.owner and self.company.owner.mobile_number):
            self.commercial_number = self.company.owner.mobile_number
        super(CompanyConf, self).save(*args, **kwargs)
        
class UserPreferences(SuperModel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True)
    order_tree_view = models.TextField(null=True, blank=True, default='_item_sequence,order_sequence,customer_name,order_type,status,delivery_company,delivery_company_status,olivery_sequence,area,total_cod,created_by,channel,note,product_info,package_cost,delivery_fee,order_due_amount,amount_received')

    def get_order_tree_view(self):
        order_tree_view = self.order_tree_view.split(',')
        return order_tree_view

    def set_order_tree_view(self, field_list):
        self.order_tree_view = ','.join(field_list)

class CompanyWallet(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, related_name='wallet')
    jod_balance = models.FloatField(null=True, blank=True, default=0)
    ils_balance = models.FloatField(null=True, blank=True, default=0)
