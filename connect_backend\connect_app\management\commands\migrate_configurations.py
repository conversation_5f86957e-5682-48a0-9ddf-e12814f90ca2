from django.core.management.base import BaseCommand
from api.users.user_model import CompanyConf, CountriesConf
from api.dashboard.dashboard_model import DelayedOrderFilters
from api.lookups.lookup_model import Country
from api.orders.order_model import StatusMap

class Command(BaseCommand):
    help = "Migrate existing data to the company_confs"

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.NOTICE("Starting migration of CountriesConf and DelayedOrderFilters to CompanyConf..."))

        for country_conf in CountriesConf.objects.all():
            company_conf, created = CompanyConf.objects.get_or_create(company=country_conf.company)
            country_codes = country_conf.country_ids.split(',') if country_conf.country_ids else []
            countries = Country.objects.filter(code__in=country_codes)

            if countries.exists():
                company_conf.additional_countries.set(countries)
                company_conf.save()

            self.stdout.write(self.style.SUCCESS(f"Migrated countries for Company ID {company_conf.company.company_id}"))

        for delayed_filter in DelayedOrderFilters.objects.all():
            company_conf, created = CompanyConf.objects.get_or_create(company=delayed_filter.company)
            status_codes = delayed_filter.delivery_status.split(',') if delayed_filter.delivery_status else []
            statuses = StatusMap.objects.filter(status_code__in=status_codes)

            if statuses.exists():
                company_conf.delayed_order_statuses.set(statuses)

            company_conf.days_delayed = delayed_filter.days_delayed
            company_conf.save()

            self.stdout.write(self.style.SUCCESS(f"Migrated delayed orders for Company ID {company_conf.company.company_id}"))

        self.stdout.write(self.style.SUCCESS("Migration completed successfully."))
