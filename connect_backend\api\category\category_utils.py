from ..util_functions import *


# return error when requird_fields empty
def validate_missing_field(category, required_fields):
    validation_errors = []
    for field in required_fields:
        if category.get(field) is None: 
            validation_errors.append(f'Missing field: {field}')
    return validation_errors

# create category
def create_category(values):
    try:
        category = Category.objects.create(**values)
    except IntegrityError:
        raise get_error_response('PRODUCT_500')
    return category

def get_category_by_id(id):
    return Category.objects.get(id=id)


def update_category_util(values):
    category = get_category_by_id(values['id'])
    if not category:
        return None

    log = create_log('Category', values['company'], 'update', values['updated_by'], category.id)
    update_category_fields(category, values, log)
    delete_log_if_no_changes(log)
    category.save()
    return category

def update_category_fields(category, values, log):
    fields = get_tracked_fields(category.company, 'Category')

    for key, value in values.items():
        old_value = getattr(category, key, None)
        if old_value != value:
            log_and_update_field(category, key, value, old_value, log, bool(key in fields))

def log_and_update_field(category, key, value, old_value, log, is_tracked):
    if isinstance(old_value, models.Model):
        old_value = old_value.name
    setattr(category, key, value)
    if isinstance(value, models.Model):
        value = value.name
    if is_tracked:
        create_log_info(log, key, old_value, value)

def get_categor_by_id(id):
    return Category.objects.get(id = id)

def create_default_category(company):
    default_category = Category.objects.create(
        name='Default Category',
        company=company,
        created_by='System',
        updated_by='System'
    )
    return default_category

def handle_delete_category(category, company):     
    company_conf = CompanyConf.objects.filter(company=company).first()

    if not company_conf or not company_conf.default_category:
        raise get_error_response('CATEGORY_1500')
    default_category = company_conf.default_category

    if category.id == default_category.id:
        raise get_error_response('CATEGORY_1501')
    
    for product in Product.objects.filter(category=category):
        product.category = default_category
        product.save()
        
    category.delete()