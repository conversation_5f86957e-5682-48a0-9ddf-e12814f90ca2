from ..orders.order_model import Order, Status
from ..delivery_company.delivery_company_model import DeliveryCompany
from django.utils.dateparse import parse_date
from django.db.models import Q, Sum, Count, F
from django.http import JsonResponse
from ..collection.collection_model import ConversionRate
from decimal import Decimal, ROUND_HALF_UP
from ..collection.collection_utils import *
from .dashboard_model import *
from datetime import timedelta

def get_date_filter(start_date_str, end_date_str):
    now = timezone.now().date()
    start_date = parse_date(start_date_str) if start_date_str else None
    end_date = parse_date(end_date_str or now.strftime('%Y-%m-%d'))
    
    if start_date and end_date:
        return Q(created_at__date__gte=start_date, created_at__date__lte=end_date)
    return Q()

def get_delivery_company_filter(id):
    if id:
        try:
            connection = ConnectDeliveryCompany.objects.get(id=id)
            return Q(connection=connection)
        except ConnectDeliveryCompany.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'ConnectDeliveryCompany', 'field': 'id', 'value': id})
    return Q()

def round_two_decimals(value):
    return value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

def get_dashboard_statuses():
    try:
        return {
            'new_order': Status.objects.get(code='new_order'),
            'returned': Status.objects.get(code='returned'),
            'preparing': Status.objects.get(code='preparing'),
            'ready_for_delivery': Status.objects.get(code='ready_for_delivery'),
            'with_delivery_company': Status.objects.get(code='with_delivery_company'),
            'with_driver': Status.objects.get(code='with_driver'),
            'expected_return_package': Status.objects.get(code='expected_return_package'),
        }
    except Status.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Error getting statuses'}, status=400)
    
def get_conversion_rate(from_currency, to_currency):
    fetch_conversion_rates()
    if from_currency == to_currency:
        return Decimal(1)
    
    try:
        rate = ConversionRate.objects.filter(
            currency_from=from_currency,
            currency_to=to_currency
        ).order_by('-date').first()
        
        if rate:
            return rate.rate
        else:
            return Decimal(1)
    except ConversionRate.DoesNotExist:
        return Decimal(1)
    
def get_order_summary(company, date_filter, delivery_company_filter, status=None, delivery_company_status=None, 
                      order_type=None, start_date=None, end_date=None, connection_id=None,driver_id=None, status_code=None, collection_status=None, 
                      delayed_time=None, area_mismatch=None, show_money=False, created_by=None, exclude_statuses_codes=None,
                      delivery_date=None, return_date=None, is_paid=None, show_profit=None, custom_filters=Q(), add_or_statement=False,
                      add_or_statement_return=False):
    filters = {
        'company': company
    }
    exclude_conditions=Q()
    if collection_status:
        exclude_conditions |= Q(collection__collection_status__code__in=collection_status)
    if exclude_statuses_codes:
        exclude_conditions |= Q(status__code__in=exclude_statuses_codes)
    if delivery_date is not None:
        filters['delivery_date__isnull'] = not delivery_date
    if status:
        if isinstance(status, list):
            filters['status__in'] = status
        else:
            filters['status'] = status
    if return_date is not None:
        filters['return_date__isnull'] = not return_date
    if delivery_company_status is not None:
        if isinstance(delivery_company_status, bool):
            filters['delivery_company_status__isnull'] = delivery_company_status
        elif isinstance(delivery_company_status, str):
            filters['delivery_company_status'] = delivery_company_status
        elif isinstance(delivery_company_status, list):
            filters['delivery_company_status__in'] = delivery_company_status
    if order_type:
        filters['order_type__code__in'] = order_type
    if delayed_time:
        filters['delivery_status_change_date__lte'] = timezone.now() - timedelta(days=delayed_time)
    if area_mismatch:
        filters['area_mismatch__gt'] = ''
    if created_by:
        filters['created_by_user'] = created_by
    if is_paid and isinstance(is_paid, bool):
        filters['is_paid'] = is_paid
    extra_filter = Q()
    if add_or_statement:
        delivery_status_filter_q = filters.pop('delivery_company_status__in', [])
        delivery_date_filter_q = filters.pop('delivery_date__isnull', False)
        extra_filter = Q(Q(delivery_company_status__in=delivery_status_filter_q) | Q(delivery_date__isnull=delivery_date_filter_q))
    if add_or_statement_return:
        status_filter_q = filters.pop('status__in', [])
        order_type_filter_q = filters.pop('order_type__code__in', False)
        extra_filter = Q(Q(status__in=status_filter_q) | Q(order_type__code__in=order_type_filter_q))

    queryset = Order.objects.filter(**filters).filter(custom_filters).filter(date_filter).filter(delivery_company_filter).filter(extra_filter).exclude(exclude_conditions)
    
    company_currency = company.company_area.country.currency

    currency_aggregates = queryset.values('currency').annotate(
        total_cod_sum=Sum('total_cod'),
        total_package_cost=Sum('package_cost'),
        total_delivery_fee=Sum(F('delivery_fee') + F('extra_delivery_fee')),
        total_profit=Sum('profit'),
        total_order_due_amount=Sum('order_due_amount'),
        total_amount_received=Sum('amount_received'),
        count=Count('id')
    )
    summary = {
        'total_cod_sum': Decimal(0),
        'total_package_cost': Decimal(0),
        'total_delivery_fee': Decimal(0),
        'total_profit': Decimal(0),
        'total_order_due_amount': Decimal(0),
        'total_amount_received': Decimal(0),
        'count': 0
    }

    for aggregate in currency_aggregates:
        order_currency = aggregate['currency']
        total_cod_sum = aggregate['total_cod_sum'] or 0
        total_package_cost = aggregate['total_package_cost'] or 0
        total_delivery_fee = aggregate['total_delivery_fee'] or 0
        total_order_due_amount = aggregate['total_order_due_amount'] or 0
        total_profit = aggregate['total_profit'] or 0
        total_amount_received = aggregate['total_amount_received'] or 0
        count = aggregate['count']

        conversion_rate = get_conversion_rate(order_currency, company_currency)

        converted_cod_sum = Decimal(total_cod_sum) * conversion_rate
        converted_package_cost = Decimal(total_package_cost) * conversion_rate
        converted_delivery_fee = Decimal(total_delivery_fee) * conversion_rate
        converted_order_due_amount = Decimal(total_order_due_amount) * conversion_rate
        converted_amount_received = Decimal(total_amount_received) * conversion_rate

        summary['total_cod_sum'] += round_two_decimals(converted_cod_sum)
        summary['total_package_cost'] += round_two_decimals(converted_package_cost)
        summary['total_delivery_fee'] += round_two_decimals(converted_delivery_fee)
        summary['total_order_due_amount'] += round_two_decimals(converted_order_due_amount)
        summary['total_profit'] += round_two_decimals(Decimal(total_profit))
        summary['total_amount_received'] += round_two_decimals(converted_amount_received)
        summary['count'] += count

    if not show_money:
        summary.pop('total_cod_sum', None)
        summary.pop('total_package_cost', None)
        summary.pop('total_delivery_fee', None)
        summary.pop('total_order_due_amount', None)
        summary.pop('total_amount_received', None)
        
    if not show_profit:
        summary.pop('total_profit', None)

    filter_items = [
        {"field": "created_at", "operator": "gt", "value": start_date} if start_date else None,
        {"field": "created_at", "operator": "lt", "value": end_date} if end_date else None,
        {"field": "connection", "operator": "exact", "value": connection_id} if connection_id else None,
        {"field": "driver", "operator": "exact", "value": driver_id} if driver_id else None,
        {"field": "status__code", "operator": "exact", "value": status_code} if isinstance(status_code, str) else None,
        {"field": "status__code", "operator": "in", "value": status_code} if isinstance(status_code, list) else None,
        {"field": "delivery_company_status", "operator": "isnull", "value": delivery_company_status} if isinstance(delivery_company_status, bool) else None,
        {"field": "delivery_company_status", "operator": "exact", "value": delivery_company_status} if isinstance(delivery_company_status, str) else None,
        {"field": "delivery_company_status", "operator": "in", "value": delivery_company_status} if isinstance(delivery_company_status, list) else None,
        {"field": "order_type__code", "operator": "in", "value": order_type} if order_type else None,
        {"field": "area_mismatch", "operator": "gt", "value": ''} if area_mismatch else None,
        {"field": "return_date", "operator": "isnull", "value": not return_date} if return_date is not None else None,
        {"field": "delivery_date", "operator": "isnull", "value": not delivery_date} if delivery_date is not None else None,
        {"field": "created_by_user_id", "operator": "exact", "value": created_by.id} if created_by else None,
        {"field": "delivery_status_change_date", "operator": "lte", "value": (timezone.now() - timedelta(days=delayed_time)).strftime('%Y-%m-%d')} if delayed_time else None,
        {"field": "collection__collection_status__code", "operator": "not_in", "value": collection_status} if collection_status else None,
        {"field": "status__code", "operator": "not_in", "value": exclude_statuses_codes} if exclude_statuses_codes else None,
        {"field": "is_paid", "operator": "exact", "value": is_paid} if is_paid and isinstance(is_paid, bool) else None
    ]

    filter_items = [item for item in filter_items if item is not None]
    summary_filters = summary.get('filters', [])
    if add_or_statement:
        delivery_status_filter = {}
        delivery_date_filter = {}
        filter_items_copy = filter_items
        filter_items = []
        for filter_item in filter_items_copy:
            if filter_item.get('field', None) == 'delivery_company_status':
                delivery_status_filter = filter_item
            elif filter_item.get('field', None) == 'delivery_date':
                delivery_date_filter = filter_item
            else:
                filter_items.append(filter_item)
        filter_items.append({'operator': 'or', 'filters':[delivery_date_filter, delivery_status_filter]})
    if add_or_statement_return:
        order_type_filter = {}
        status_filter = {}
        filter_items_copy = filter_items
        filter_items = []
        for filter_item in filter_items_copy:
            if filter_item.get('field', None) == 'order_type__code':
                order_type_filter = filter_item
            elif filter_item.get('field', None) == 'status__code' and filter_item.get('operator') == 'in':
                status_filter = filter_item
            else:
                filter_items.append(filter_item)
        filter_items.append({'operator': 'or', 'filters':[order_type_filter, status_filter]})
    if summary_filters:
        summary_filters[0].setdefault('filters', []).extend(filter_items)
    else:
        summary['filters'] = [{"operator": "and", "filters": filter_items}]

    return summary