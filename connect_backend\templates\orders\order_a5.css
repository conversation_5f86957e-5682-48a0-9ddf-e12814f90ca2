@page {
    size: a5 landscape;
    margin: 0;
}

.o-table .cell-truncate {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 11rem;
    width: 11rem;
}

.waybill-container {
    padding: 3.8rem;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: solid 0.033rem;
    padding: .2rem .6rem;
    height: 7.15rem;
}

.header-info .info-item {
    padding-bottom: .6rem;
}

.header-barcode-container {
    width: 18rem;
    height: 5.8rem;
}

.o-table {
    border-spacing: 0.15rem 0.35rem;
}

.o-table td,
th {
    padding: .4rem .6rem;
    width: 25%;
    text-align: center;
}

.date-time {
    font-size: 1.13rem;
}

.card {
    border: solid 0.033rem;
    border-radius: .5rem;
    padding: .9rem;
    margin-top: .9rem;
}

.card-header {
    font-size: 1.25rem;
    font-weight: bold;
    border-bottom: .15rem solid;
    padding-bottom: .5rem;
    margin-bottom: 1.3rem;
}

.cards-container {
    display: inline-block;
    width: 50%;
    vertical-align: top;
    padding: 0rem 0rem 0rem .45rem;
}

/* :dir(rtl)  */
.cards-container {
    padding: 0rem .45rem 0rem 0rem;
}

.cards-container:first-child {
    padding-right: .45rem;
    padding-left: 0rem;
}

:dir(rtl) 
.cards-container:first-child {
    padding-right: 0rem;
    padding-left: .45rem;
}

.notes-content {
    height: 2rem;
    overflow: hidden;
}

.products-container {
    min-height: 29.8rem;
    position: relative;
    padding-bottom: 3rem;
}

.products-container .item-sequence {
    width: 8%;
}

.products-container .item-name {
    width: 71%;
}

.products-container .item-quantity {
    width: 10%;
}

.products-container .item-price {
    width: 11%;
}

.products-container .total-row {
    position: absolute;
    bottom: 0rem;
    width: 100%;
}

.card-header-container {
    display: flex;
}

.card-header-container .card-header {
    flex-grow: 1;
}

.card-barcode-container {
    width: 12.4rem;
    height: 4.8rem;
}

.text-truncate {
    text-align: end;
}

.business-logo {
    max-height: 5.3rem;
}