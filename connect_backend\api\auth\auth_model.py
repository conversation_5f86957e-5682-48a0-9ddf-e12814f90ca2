from django.db import models
from django.utils import timezone
from datetime import timedelta
import binascii
import os
from django.core.exceptions import ValidationError
from api.reseller.models import ResellerJWTAuthentication
import jwt
from django.conf import settings
from django.contrib.auth.models import User as DjUser
from ..users.user_model import User
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.permissions import BasePermission
from django.contrib.auth.models import Permission
from functools import wraps
from django.http import JsonResponse
from django.utils.timezone import now
from api.billing.billing_model import CompanyPlan, Subscription
from api.error.error_model import *

class OtpCodes(models.Model):

    def default_expire_date():
        return timezone.now() + timezone.timedelta(minutes=3)

    code = models.CharField(max_length=6)
    mobile_number = models.CharField(max_length=15,unique=True)
    created_at = models.DateTimeField(default=timezone.now)
    expire_date = models.DateTimeField(default=default_expire_date)
    number_of_trials = models.IntegerField(default=0)
    error_trials = models.IntegerField(default=0)

class OtpToken(models.Model):
    
    mobile_number = models.CharField(max_length=15, unique=True)
    token = models.CharField(max_length=200,unique=True)
    created_at = models.DateTimeField(default=timezone.now)
    expires_at = models.DateTimeField()

    def save(self, *args, **kwargs):
        self.token = ''.join(binascii.hexlify(os.urandom(20)).decode())
        self.expires_at = timezone.now() + timedelta(minutes=30)
        super().save(*args, **kwargs)

class PermissionDescription(models.Model):
    permission = models.OneToOneField(Permission, on_delete=models.CASCADE)
    description = models.TextField()

class CustomJWTAuthentication(BaseAuthentication):
    def authenticate(self, request):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Token '):
            raise AuthenticationFailed('Authorization header must start with "Token "')

        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            raise AuthenticationFailed('Token has expired')
        except jwt.InvalidTokenError:
            raise AuthenticationFailed('Invalid token')

        try:
            user = DjUser.objects.get(id=payload['user_id'])
        except DjUser.DoesNotExist:
            raise AuthenticationFailed('User not found')

        return (user, payload)
    
class NoAuthentication(BaseAuthentication):
    def authenticate(self, request):
        return None

    def authenticate_header(self, request):
        return None
    
def subscription_required(view_func):
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        user = request.user
        if not user.is_authenticated:
            return JsonResponse({"error": "Authentication required."}, status=401)
        
        def get_current_subscription(company):
            company_plan = CompanyPlan.objects.filter(company=company).last()
            subscription = Subscription.objects.filter(company_plan=company_plan, company=company).last()
            if not subscription or subscription.end_date < timezone.now():
                raise get_error_response('BILLING_800', {})

            return subscription
        
        user = User.objects.get(user=user)

        try:
            subscription = get_current_subscription(user.company)
        except ConnectError as e:
            response = e.to_dict()
            if response['error']['code'] == 'AUTH_400':
                status=401
            else:
                status=400
            return JsonResponse(response, status=status)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)

        return view_func(request, *args, **kwargs)
    
    return _wrapped_view

class CombinedJWTAuthentication(BaseAuthentication):
    def __init__(self):
        self.authenticators = [
            ResellerJWTAuthentication(),
            CustomJWTAuthentication()
        ]

    def authenticate(self, request):
        last_exception = None
        for authenticator in self.authenticators:
            try:
                result = authenticator.authenticate(request)
                if result is not None:
                    return result 
            except AuthenticationFailed as e:
                last_exception = e 
        
        if last_exception:
            raise last_exception

        return None