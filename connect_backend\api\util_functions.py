from django.apps import apps
from django.shortcuts import get_object_or_404
from django.db import models
from django.db.models import Q, Sum
import urllib.parse
from django.forms.models import model_to_dict
from django.core.paginator import Paginator
from django.utils import timezone
from urllib.parse import urlencode
from django.utils.translation import gettext as _
from django.utils import translation
from collections import defaultdict
import jwt
import json
import time
from django.conf import settings
import datetime
from django.contrib.auth.models import User as DjUser
from .users.user_model import User, CompanyConf
from weasyprint import HTML
from django.http import HttpResponse
import base64
from barcode import Code128
from barcode.writer import ImageWriter
from io import BytesIO
from jinja2 import Environment, FileSystemLoader
import requests
from .orders.order_model import Order, Status
from .connection.connection_model import *
from urllib.parse import urlparse
import os
from .lookups.lookup_model import *
from .serializers import *
from django.core.files.base import ContentFile
from .billing.billing_model import *
from dateutil.relativedelta import relativedelta
from .error.error_model import get_error_response
import re
import difflib
from .dashboard.dashboard_utils import get_conversion_rate
from decimal import ROUND_HALF_UP, Decimal
import qrcode
import pytz
from .error.error_model import *
from django.db.models.functions import TruncDate,TruncMonth,TruncYear
from .error.error_model import *
import pandas as pd
from .middleware import get_current_request
from django.db.models import Case, IntegerField

def get_user_from_token(request):
    token = request.headers.get('Authorization')
    if not token or not token.startswith('Token '):
        return None
    token_key = token.split(' ')[1]
    try:
        payload = jwt.decode(token_key, settings.SECRET_KEY, algorithms=['HS256'])
        user_id = payload.get('user_id')
        if user_id is None:
            return None

        # Validate token expiration
        exp = payload.get('exp')
        if exp is None:
            return None

        # Convert expiration time to timezone-aware datetime
        exp_datetime = datetime.datetime.fromtimestamp(exp, datetime.timezone.utc)
        if exp_datetime < timezone.now():
            return None

        try:
            auth_user = DjUser.objects.get(id = user_id)
            user_mobile_number = '0' + auth_user.username[4:]
            user = User.objects.get(mobile_number = user_mobile_number)
            return user
        except User.DoesNotExist:
            return None
        except DjUser.DoesNotExist:
            return None
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None
    
def object_to_json(data_list, objects_to_handle, model_map):
    for data in data_list:
        for field, value in data.items():
            if field in objects_to_handle:
                try:
                    model_class = model_map.get(field)
                    if model_class:
                        serializer_class = MODEL_SERIALIZER_MAP.get(model_class)
                        if serializer_class:
                            pk = value
                            if isinstance(value, dict) and 'id' in value:
                                pk = value.get('id')
                            elif isinstance(value, dict) and 'company_id' in value:
                                pk = value.get('company_id')
                            elif isinstance(value, dict) and 'role_id' in value:
                                pk = value.get('role_id')
                            if pk is not None:
                                model_instance = False
                                if isinstance(pk, int):
                                    model_instance = model_class.objects.get(pk=pk)
                                elif isinstance(pk, model_class):
                                    model_instance = pk
                                else:
                                    data[field] = None
                                if model_instance:
                                    serializer = serializer_class(model_instance)
                                    data[field] = serializer.data
                            else:
                                data[field] = None
                        else:
                            data[field] = None
                except models.ObjectDoesNotExist:
                    pass
            elif field.endswith('_image'):
                data[field] = data[field].url if data[field] else ''
            elif field == 'data' and isinstance(data[field], list):
                object_to_json(data[field], objects_to_handle, model_map)
            else:
                if isinstance(data[field], str):
                    data[field] = _(data[field])
    return data_list;
    
def model_dict(model_instance):
    data = model_to_dict(model_instance)
    # Loop through fields to handle special cases
    for field in model_instance._meta.get_fields():
        if isinstance(field, models.ImageField):
            data[field.name] = getattr(model_instance, field.name).url if getattr(model_instance, field.name) else None

    return data

def get_model_instance(field, model_id, model_map):
    model_name = model_map.get(field)
    if model_name and model_id:
        ModelClass = apps.get_model(model_name)
        instance = get_object_or_404(ModelClass, pk=model_id)
        return instance
    return None

def is_number(s):
    s = str(s)
    return s.replace('.', '', 1).isdigit()

def handle_related(queryset, model, fields_to_include, related_fields=None):
    if not queryset:
        return []
    
    
    if not isinstance(queryset, list):
        queryset_data = list(queryset.values(*fields_to_include))
    else:
        queryset_data = queryset

    data = []
    related_fields = related_fields or {}

    seen_ids = set() 
    unique_data = []
    if isinstance(queryset_data[0], dict):
        for item_data in queryset_data:
            if model == Company:
                item_id = item_data.get('company_id')
            elif model == Role:
                item_id = item_data.get('role_id')
            else:
                item_id = item_data.get('id')
            if item_id in seen_ids:
                continue
            seen_ids.add(item_id)
            instance = model.objects.get(pk=item_id)

            for field, serializer_class in related_fields.items():
                related_manager = getattr(instance, field, None)
                if related_manager:
                    try:
                        if field == 'cloned_order':
                            serialized_data = CloneOrderSerializer(related_manager).data
                        else:
                            serialized_data = serializer_class(related_manager.all(), many =True).data
                    except Exception:
                        serialized_data = serializer_class(related_manager).data
                    item_data[field] = serialized_data
                

            unique_data.append(item_data)
    else:
        for instance in queryset_data:

            if model == Company:
                item_id = instance.company_id
            elif model == Role:
                item_id = instance.role_id
            else:
                item_id = instance.id
            if item_id in seen_ids:
                continue
            seen_ids.add(item_id)
            item_data = {field: getattr(instance, field) for field in fields_to_include}

            # Add related fields dynamically
            for field, serializer_class in related_fields.items():
                related_manager = getattr(instance, field, None)
                if related_manager:
                    try:
                        if field == 'cloned_order':
                            serialized_data = CloneOrderSerializer(related_manager).data
                        else:
                            serialized_data = serializer_class(related_manager.all(), many =True).data
                    except Exception:
                        serialized_data = serializer_class(related_manager).data
                    item_data[field] = serialized_data

            unique_data.append(item_data)

    return unique_data

def parse_date(value):
    try:
        return datetime.datetime.strptime(value, '%Y-%m-%d').date()
    except ValueError:
        return None

def get_date_range(value, timezone="UTC"):
    today = datetime.datetime.now(pytz.timezone(timezone)).date()
    start_date = None
    end_date = today

    if value == 'day':
        start_date = today
    elif value == 'yesterday':
        start_date = today - datetime.timedelta(days=1)
        end_date = start_date
    elif value == 'week':
        start_date = today - datetime.timedelta(days=7)
    elif value == 'month':
        start_date = today.replace(day=1)
    elif value == 'year':
        start_date = today.replace(month=1, day=1)
    else:
        start_date = parse_date(value)
        end_date = start_date
    start_date = datetime.datetime.combine(start_date, datetime.time.min).replace(tzinfo=pytz.timezone(timezone))
    end_date = datetime.datetime.combine(end_date, datetime.time.max).replace(tzinfo=pytz.timezone(timezone))

    return start_date, end_date

def format_group_value(group_value,date_value):
    if date_value == 'day':
        return  group_value.strftime('%Y-%m-%d')
    elif date_value == 'month':
        return  group_value.strftime('%Y-%m')
    elif date_value == 'year':
        return group_value.strftime('%Y')

def search_filter_group(queryset, json_query, fields_to_include, related_fields, sum_field=None):
    search = json_query.get('search')
    filters = json_query.get('filters', [])
    exclude = json_query.get('exclude', [])
    group_by = json_query.get('group_by', [])
    page = json_query.get('offset', 1)
    size = json_query.get('size', 300)
    date_value = json_query.get('date_value') if 'date_value' in json_query else None

    if page == 0:
        page = 1

    model = queryset.model

    search_filter = Q()
    if search:
        for field in model._meta.get_fields():
            if isinstance(field, (models.CharField, models.TextField, models.EmailField, models.DateField, models.BigIntegerField, models.FloatField, models.DecimalField)):
                if is_number(search):
                    if "." in search:
                        temp = float(search)
                    else:
                        temp = int(search)
                else:
                    temp = search
                search_filter |= Q(**{f"{field.name}__icontains": temp})
        queryset = queryset.filter(search_filter)
    
    q_filters = Q()
    for filter_group in filters:
        group_q = Q()
        for key, group_filters in filter_group.items():
            group_and_q = Q()
            for filter_item in group_filters:
                field = filter_item.get('field')
                operator = filter_item.get('operator', 'exact')
                value = filter_item.get('value')

                if field and value is not None:
                    if field == 'created_at' and operator == 'range':
                        if value in ['day', 'yesterday' ,'week', 'month', 'year']:
                            start_date, end_date = get_date_range(value)
                            date_value = [start_date, end_date]
                            group_and_q &= Q(**{f"{field}__{operator}": date_value})
                            continue
                    if operator in ['gt', 'lt', 'gte', 'lte'] and isinstance(value, str):
                        date_value = parse_date(value)
                        if date_value is not None:
                            value = datetime.datetime.combine(date_value, datetime.datetime.min.time())
                        elif operator == 'gt' and value == '':
                            lookup = f"{field}__{operator}"
                            group_and_q &= Q(**{lookup: value})
                        else:
                            continue
                    
                    if operator == 'isnull' and isinstance(value, bool):
                        value = True if value else False
                    
                    if isinstance(value, datetime.datetime) and value.tzinfo is None:
                        value = timezone.make_aware(value, timezone.get_current_timezone())
                    
                    if operator == 'neq':
                        group_and_q &= ~Q(**{field: value})
                    elif operator == 'eq': 
                        group_and_q &= Q(**{field: value})
                    else:
                        lookup = f"{field}__{operator}"
                        group_and_q &= Q(**{lookup: value})
            group_q |= group_and_q
        q_filters &= group_q

    if filters:
        queryset = queryset.filter(q_filters)

    exclude_filters = Q()
    for filter_group in exclude:
        group_q = Q()
        for key, group_filters in filter_group.items():
            for filter_item in group_filters:
                field = filter_item.get('field')
                operator = filter_item.get('operator', 'exact')
                value = filter_item.get('value')

                if field and value is not None:
                    if operator in ['gt', 'lt', 'gte', 'lte'] and isinstance(value, str):
                        date_value = parse_date(value)
                        if date_value is not None:
                            value = datetime.datetime.combine(date_value, datetime.datetime.min.time())
                        elif operator == 'gt' and value == '':
                            lookup = f"{field}__{operator}"
                            group_q &= Q(**{lookup: value})
                        else:
                            continue
                    
                    if operator == 'isnull' and isinstance(value, bool):
                        value = True if value else False
                    
                    if isinstance(value, datetime.datetime) and value.tzinfo is None:
                        value = timezone.make_aware(value, timezone.get_current_timezone())
                    
                    if operator == 'neq':
                        group_q &= ~Q(**{field: value})
                    elif operator == 'eq': 
                        group_q &= Q(**{field: value})
                    else:
                        lookup = f"{field}__{operator}"
                        group_q &= Q(**{lookup: value})
            exclude_filters |= group_q

    if exclude:
        queryset = queryset.exclude(exclude_filters)

    if sum_field and queryset and model == Order:
        company = queryset[0].company
        company_currency = company.company_area.country.currency
        total_sum = 0
        currency_aggregates = queryset.values('currency').annotate(total_cod_sum=Sum(sum_field))
        for aggregate in currency_aggregates:
            order_currency = aggregate['currency']
            total_cod_sum = aggregate['total_cod_sum'] or 0

            conversion_rate = get_conversion_rate(order_currency, company_currency)

            converted_cod_sum = Decimal(total_cod_sum) * conversion_rate

            total_sum += converted_cod_sum
        queryset = queryset.annotate(sum_field=Sum(sum_field))
        
    if not group_by:
        if not queryset:
            response = [{'data': [], 'data_count': queryset.count()}]
            if sum_field and queryset and model == Order:
                response[0]['total_sum'] = total_sum if sum_field else 0
            return response
        total_size = len(queryset)
        queryset = queryset[page - 1: (page - 1 + size)]
        data = handle_related(queryset, model, fields_to_include, related_fields)
        response = [{'data': data, 'data_count': total_size}]
        if sum_field:
            response[0]['total_sum'] = total_sum if sum_field else 0
        return response
    
    def group_queryset(queryset, group_by_fields):
        if not group_by_fields:
            return handle_related(queryset, model, fields_to_include, related_fields)

        current_group_by = group_by_fields.pop(0)
        grouped_data = {}
        if isinstance(queryset, list):
            queryset = model.objects.filter(pk__in=[item['id'] for item in queryset])

        if current_group_by == 'created_at':
            if date_value == 'day':
                group_values = queryset.annotate(date=TruncDate('created_at')).values_list('date', flat=True).distinct()

                for group_value in group_values:
                    group_queryset_instance = queryset.filter(created_at__date=group_value)
                    grouped_data[group_value] = group_queryset_instance
            elif date_value == 'month':
                group_values = queryset.annotate(month=TruncMonth('created_at')).values_list('month', flat=True).distinct()

                for group_value in group_values:
                    group_queryset_instance = queryset.filter(created_at__month=group_value.month, created_at__year=group_value.year)
                    grouped_data[group_value] = group_queryset_instance
            elif date_value == 'year':
                group_values = queryset.annotate(year=TruncYear('created_at')).values_list('year', flat=True).distinct()

                for group_value in group_values:
                    group_queryset_instance = queryset.filter(created_at__year=group_value.year)
                    grouped_data[group_value] = group_queryset_instance

        else:
            group_values = queryset.values_list(current_group_by, flat=True).distinct()
    
            for group_value in group_values:
                group_queryset_instance = queryset.filter(**{current_group_by: group_value})
                grouped_data[group_value] = group_queryset_instance

        grouped_users = []
        for group_value, group_queryset_instance in grouped_data.items():

            if date_value:
                formatted_group_value = format_group_value(group_value,date_value)
            else:
                formatted_group_value = group_value

            if sum_field and group_queryset_instance and model == Order:
                company = group_queryset_instance[0].company
                company_currency = company.company_area.country.currency
                total_sum = 0
                currency_aggregates = group_queryset_instance.values('currency').annotate(total_cod_sum=Sum(sum_field))
                for aggregate in currency_aggregates:
                    order_currency = aggregate['currency']
                    total_cod_sum = aggregate['total_cod_sum'] or 0

                    conversion_rate = get_conversion_rate(order_currency, company_currency)

                    converted_cod_sum = Decimal(total_cod_sum) * conversion_rate

                    total_sum += converted_cod_sum
            grouped_users.append({
                current_group_by: formatted_group_value,
                'data': handle_related(group_queryset_instance, model, fields_to_include, related_fields),
                'data_count': group_queryset_instance.count(),
                'is_group': True,
            })
            if sum_field:
                grouped_users[-1]['total_sum'] = total_sum if sum_field else 0

        return grouped_users

    def handle_grouping(queryset, group_by_fields):
        grouped_users = group_queryset(queryset, group_by_fields)
        for group_data in grouped_users:
            if 'data' in group_data:
                group_data['data'] = handle_grouping(group_data['data'], group_by_fields.copy())
            else: 
                paginator = Paginator(grouped_users, 150)
                grouped_users = list(paginator.page(1))
        return grouped_users

    grouped_users = handle_grouping(queryset, group_by)

    return grouped_users

ARABIC_CHARACTERS = set('ابتثجحخدذرزسشصضطظعغفقكلمنهوية')
HEBREW_CHARACTERS = set('אבגדהוזחטיךלמנסעפצקראש')
ENGLISH_CHARACTERS = set('abcdefghijklmnopqrstuvwxyz')

def clean_text(text):
    text = re.sub(r'[^\w\s]', '', text)
    text = text.replace('أ', 'ا').replace('إ', 'ا').replace('آ', 'ا').replace('ؤ', 'و').replace('ئ', 'ي').replace('ة', 'ه')
    return text.lower().strip()

def detect_language(text):
    if any(char in ARABIC_CHARACTERS for char in text):
        return 'ar'
    elif any(char in HEBREW_CHARACTERS for char in text):
        return 'he'
    elif any(char in ENGLISH_CHARACTERS for char in text):
        return 'en'
    return 'unknown'

def similarity_score(a, b):
    return difflib.SequenceMatcher(None, a, b).ratio() * 100

def fuzzy_search(query, model_type, filter=None, threshold=80):
    cleaned_query = clean_text(query)
    lang = detect_language(cleaned_query)

    model_map = {
        'SubArea': FuzzySubArea,
        'Area': FuzzyArea,
        'Country': FuzzyCountry,
    }
    fields_to_get = {
        'SubArea': {
            'ar': 'sub_area_name_arabic',
            'en': 'sub_area_name_english',
            'he': 'sub_area_name_hebrew'
        },
        'Area': {
            'ar': 'area_name_arabic',
            'en': 'area_name_english',
            'he': 'area_name_hebrew'
        },
        'Country': {
            'ar': 'country_name_arabic',
            'en': 'country_name_english',
            'he': 'country_name_hebrew'
        },
    }

    model = model_map.get(model_type)
    field_to_get = fields_to_get.get(model_type).get(lang)

    if lang and model:
        if filter:
            area_names = model.objects.filter(filter).values_list(field_to_get, flat=True)
        else:
            area_names = model.objects.values_list(field_to_get, flat=True)

        best_match_score = 0
        best_match_name = None
        for area in area_names:
            score = similarity_score(cleaned_query, clean_text(area))
            if score > best_match_score:
                best_match_score = score
                best_match_name = area

        if best_match_name:
            best_match_name = best_match_name
            best_score = best_match_score
            if best_match_name in ['Ramallah', 'Al Bireh']:
                best_match_name = 'Ramallah and Al Bireh'
            elif best_match_name in ['רמאללה', 'אל-בירה']:
                best_match_name = 'רמאללה ואל-בירֶה'
            elif best_match_name in ['البيرة', 'رام الله']:
                best_match_name = 'رام الله والبيرة'
            elif best_match_name == 'Israel':
                best_match_name = 'Palestine'
            elif best_match_name == 'إسرائيل':
                best_match_name = 'فلسطين'
            elif best_match_name == 'יִשְׂרָאֵל':
                best_match_name = 'פַּלֶשְׂתִינָה'
            elif best_match_name == 'Emirates':
                best_match_name = 'United Arab Emirates'
            elif best_match_name == 'الإمارات':
                best_match_name = "الإمارات العربية المتحدة"
            elif best_match_name == 'אמירות':
                best_match_name = "איחוד האמירויות הערביות"
            matched_object = model.objects.filter(**{field_to_get: best_match_name}).first()
            if matched_object:
                if not model_type == 'Country':
                    arabic_name = getattr(matched_object, fields_to_get[model_type]['ar'])
                else:
                    arabic_name = getattr(matched_object, fields_to_get[model_type]['en'])
                return arabic_name, best_score

    return None, None

def generate_pdf(template_folder, template_name, docs, meta):

    env = Environment(loader=FileSystemLoader('.'))

    template_path = f"templates/{template_folder}/{template_name}.html"
    template = env.get_template(template_path)
    now = datetime.datetime.now()
    now_str = now.strftime('%H:%M %d-%m-%Y')
    now_str_date = now.strftime('%d-%m-%Y')
    meta['formatted_now'] = now_str
    meta['formatted_now_date'] = now_str_date
    try:
        rendered_template = template.render(docs=docs, meta=meta)
        pdf_file = HTML(string=rendered_template, base_url='file://' + os.path.abspath('templates')).write_pdf()
        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = 'inline; filename="report.pdf"'
        return response
    
    except Exception as e:
        raise e

def generate_barcode_base64(order_number):
    # Generate a Code 128 barcode image as bytes
    writer = ImageWriter()
    barcode = Code128(order_number, writer)
    options = {
        'module_width': 0.2,  # Width of each module (barcode element)
        'module_height': 7.0,  # Height of the barcode
        'text_distance': 3.0,  # Distance of the text from the barcode
        'font_size': 7,  # Font size of the text
    }
    # Create BytesIO object to hold the image bytes
    buffer = BytesIO()
    barcode.write(buffer, options=options)
    
    # Get the bytes from the buffer
    barcode_image_bytes = buffer.getvalue()

    # Convert barcode image bytes to base64
    barcode_image_base64 = base64.b64encode(barcode_image_bytes).decode('utf-8')

    return barcode_image_base64

def generate_qrcode_base64(order_number):
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=1,
    )
    qr.add_data(order_number)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")
    # Generate a Code 128 barcode image as bytes
    buffer = BytesIO()
    img.save(buffer, format="PNG")
    buffer.seek(0)
    
    # Encode to base64
    img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    
    return img_base64

def send_orders_to_delivery_company(values):
    orders_list = values['orders_list']
    delivery_company = values['delivery_company']
    connection_user = values['connection_user']
    company = values['company']
    name = values['name']
    auto_send = values.get('auto_send', False)

    if not orders_list:
        return [], []
    
    missing_keys = validate_required_keys(values)
    if missing_keys:
        raise get_error_response('CONNECTION_313', {'delivery_company': values.get('delivery_company'), 'missing_info': ', '.join(missing_keys)})

    if not delivery_company.delivery_company_url:
        raise get_error_response('CONNECTION_316', {'delivery_company': delivery_company})
    overall_success_messages = []
    overall_fail_messages = []
    batch_size = 10
    for i in range(0, len(orders_list), batch_size):
        batch = orders_list[i:i + batch_size]
        params = prepare_api_params(delivery_company, connection_user, batch)
        try:
            response = send_api_to_delivery_company(
                delivery_company, '/connection/create_multi_orders', params, return_raw=True
            )
        except ConnectError as e:
            for order_data in batch:
                order = Order.objects.get(company=company, order_sequence=order_data.get('connect_reference_id'))
                overall_fail_messages.append(handle_sending_connect_error(e.to_dict(), order, name=order.delivery_company.name))
            continue
        except Exception as e:
            for order_data in batch:
                order = Order.objects.get(company=company, order_sequence=order_data.get('connect_reference_id'))
                connect_error = get_error_response('GENERAL_007', {'error': str(e)})
                overall_fail_messages.append(handle_sending_connect_error(connect_error.to_dict(), order, name=order.delivery_company.name))
            continue
        if response.get('result', {}).get('message') == 'Odoo Server Error':
            for order_data in batch:
                order = Order.objects.get(company=company, order_sequence=order_data.get('connect_reference_id'))
                connect_error = get_error_response('CONNECTION_307', {'delivery_company': delivery_company, 'error_code': 400, 'connect_message': response.get('result', {}).get('data', {}).get('message')})
                overall_fail_messages.append(handle_sending_connect_error(connect_error.to_dict(), order, name=order.delivery_company.name))
            continue
        success_messages, existing_messags, fail_messages = process_orders(response, company, delivery_company, name, connection_user, auto_send)
        success_messages.extend(existing_messags)
        overall_success_messages.extend(success_messages)
        overall_fail_messages.extend(fail_messages)

    return overall_success_messages, overall_fail_messages

def validate_required_keys(values):
    required_keys = ['orders_list', 'delivery_company', 'connection_user', 'company', 'name']
    return [key for key in required_keys if not values.get(key)]

def prepare_api_params(delivery_company, connection_user, orders_list):
    return {
        "jsonrpc": "2.0",
        "params": {
            'db': delivery_company.delivery_company_db,
            'login': connection_user.company_username,
            'password': connection_user.company_password,
            'orders_list': orders_list
        }
    }

def process_orders(response, company, delivery_company, name, connection_user, auto_send=False):
    result = response.get('result', {})
    sequences = result.get('Sequences', [])
    references = result.get('References', [])
    failed_references = result.get('fail_references', [])
    fail_messages = result.get('fail_message', [])
    existing_records = result.get('existing_records', []) 
    existing_sequence_records = result.get('existing_sequence_records', [])

    success_messages = []
    failed_messages = []
    existing_messags = []

    for seq, ref in zip(sequences, references):
        process_successful_order(seq, ref, company, delivery_company, name, auto_send)
        success_messages.append({'sequence': ref, 'delivery_company': delivery_company.name, 'message': _('Order sent successfully')})

    for existing_record, existing_sequence_record in zip(existing_records, existing_sequence_records):
        sync_existing_order(existing_record, connection_user, company, delivery_company, existing_sequence_record)
        existing_messags.append({'sequence': existing_record, 'delivery_company': delivery_company.name, 'message': _('Order already existed and status synced successfully')})

    for ref, msg in zip(failed_references, fail_messages):
        process_failed_order(ref, msg, company, name)
        error_code, connect_message, connect_what_to_do = parse_error_response(msg)
        if not error_code:
            error_code = 'Unknown error'
        if not connect_message:
            connect_message = msg
        if not connect_what_to_do:
            connect_what_to_do = _("contact support to resolve this issue")
        failed_messages.append({'sequence': ref, 'delivery_company': delivery_company.name, 'message': connect_message, 'what_to_do': connect_what_to_do, 'error_code': error_code})

    return success_messages, existing_messags, failed_messages

def sync_existing_order(order, connection_user, company, delivery_company, existing_sequence_record):
    try:
        if isinstance(order, str):
            customer_code = f"C{company.company_id:03d}"
            if customer_code in order or 'CO' in order:
                order = Order.objects.get(order_sequence=order, company=company)
            else:
                order = Order.objects.get(order_reference=order, company=company)
        order.delivery_company = delivery_company
        order.save()
        params = {
            "jsonrpc": "2.0",
            "params": {
                'db': delivery_company.delivery_company_db,
                'login': connection_user.company_username,
                'password': connection_user.company_password,
                'order_id': existing_sequence_record
            }
        }
        response = send_api_to_delivery_company(delivery_company, '/get_order_status', params, return_raw=True)
        result = response.get('result')
        values = result.get('response')
        order.olivery_sequence = values.get('sequence')
        try:
            if values.get('status'):
                status_map = DeliveryStatusMap.objects.get(imported_status_code=values.get('status'), delivery_company=order.delivery_company)
                delivery_status = status_map.status
        except DeliveryStatusMap.DoesNotExist:
            delivery_status = None
        order.status = get_company_delivery_request_status(company)
        if delivery_status:
            order.delivery_company_status = delivery_status.delivery_company_status
        params = {
            "jsonrpc": "2.0",
            "params": {
                'db': delivery_company.delivery_company_db,
                'login': connection_user.company_username,
                'password': connection_user.company_password,
                'order_id': order.olivery_sequence,
                'vals': {
                    'is_connect_order': True,
                    'connect_reference_id': order.order_sequence
                }
            }
        }
        send_api_to_delivery_company(delivery_company, '/edit_order', params, return_raw=True)
        if order.channel.lower() == 'woocommerce' and order.order_reference:
            try:
                send_order = send_delivery_status_to_woocommerce(order.order_reference, order.delivery_company_status, order.olivery_sequence, order.company)
            except:
                pass
        order.save()
    except Exception as e:
        print(f'Error syncing existing order: {str(e)}')
        print(str(e))
        pass

def process_successful_order(sequence, reference, company, delivery_company, name, auto_send):
    try:
        customer_code = f"C{company.company_id:03d}"
        if customer_code in reference or 'CO' in reference:
            order = Order.objects.get(order_sequence=reference, company=company)
        else:
            order = Order.objects.get(order_reference=reference, company=company)
        order.olivery_sequence = sequence
        order.status = get_company_delivery_request_status(company)
        order.delivery_company = delivery_company
        order.delivery_company_status = StatusMap.objects.get(status_code='waiting').delivery_company_status
        order.driver = None
        order.send_failed = False
        create_log('Order', company, _('Sent Orders to Delivery Company') if not auto_send else _('Automatically Sent Orders to Delivery Company'), name, order.id)
        order.save()
        if order.channel.lower() == 'woocommerce' and order.order_reference:
            try:
                send_order = send_delivery_status_to_woocommerce(order.order_reference, order.delivery_company_status, order.olivery_sequence, order.company)
            except:
                pass
    except Order.DoesNotExist:
        pass

def process_failed_order(reference, fail_message, company, name):
    try:
        customer_code = f"C{company.company_id:03d}"
        if customer_code in reference or 'CO' in reference:
            order = Order.objects.get(order_sequence=reference, company=company)
        else:
            order = Order.objects.get(order_reference=reference, company=company)
        order.send_failed = True
        log = create_log('Order', company, 'Failed to send order', name, order.id)
        create_log_info(log, 'Fail Message', None, fail_message)
        order.save()
    except Order.DoesNotExist:
        pass

def update_order_to_vhub(vhub_vals, order_id ,delivery_company, connection_user, company):
    delivery_company_url = delivery_company.delivery_company_url
    delivery_company_db = '.'.join(urlparse(delivery_company_url).hostname.split('.')[:-2]) if urlparse(delivery_company_url).hostname and len(urlparse(delivery_company_url).hostname.split('.')) > 2 else None
    
    if not delivery_company_url:
        response = {
            'success': False,
            'message': _('Order was not reflected in delivery company because delivery company has no URL'),
            'what_to_do': _('Contact Support to setup delivery company')
        }
        return response
    
    params = {
        "jsonrpc": "2.0",
        "params": {
            'db': delivery_company_db,
            'login': connection_user.company_username,
            'password': connection_user.company_password,
            'vals': vhub_vals,
            "order_id": order_id,
            "lang": 'ar_SY' if translation.get_language() == 'ar' else 'en_US'
        }
    }

    try:
        response = send_api_to_delivery_company(delivery_company, '/edit_order', params)
    except ConnectError as e:
        response = e.to_dict()
        return {
            'success': False,
            'message': response.get('error').get('message'),
            'what_to_do': response.get('error').get('what_to_do')
        }
    
    return {'success': True}
    
def get_default_connection(company):
    try:
        company_conf = CompanyConf.objects.get(company=company)
        return company_conf.default_connection
    except CompanyConf.DoesNotExist:
        return None
    
def parse_request_body(request):
    try:
        return json.loads(request.body)
    except json.JSONDecodeError:
        return None
    
def get_area(model, validation_errors, country=None):
    area_id_or_name = str(model.get('area'))
    if isinstance(area_id_or_name, dict):
        if 'id' in area_id_or_name:
            area_id_or_name = area_id_or_name['id']
        elif 'name' in area_id_or_name:
            area_id_or_name = area_id_or_name['name']
    if country:
        country_filter = Q(country = country)
        sub_area_country_filter = Q(area__country = country)
    else:
        country_filter = Q()
        sub_area_country_filter = Q()
    if area_id_or_name:
        if area_id_or_name.isdigit():
            try:
                area = Area.objects.filter(id=area_id_or_name).filter(country_filter).first()
                if area:
                    return area
                else:
                    validation_errors.append('Invalid area ID')
            except Area.DoesNotExist:
                validation_errors.append('Invalid area ID')
        else:
            area_names = list(Area.objects.filter(country_filter).values_list('name', flat=True))
            area_names.append('رام الله')
            area_names.append('البيرة')
            best_match, score = fuzzy_search(area_id_or_name, 'Area', filter=Q(area_name_arabic__in = area_names))
            if not score or score < 75:
                sub_area_names = list(SubArea.objects.filter(sub_area_country_filter).values_list('name', flat=True))
                sub_best_match, sub_area_score = fuzzy_search(area_id_or_name, 'SubArea', filter=Q(sub_area_name_arabic__in = sub_area_names))
                if not sub_area_score or sub_area_score > 75:
                    sub_area = SubArea.objects.filter(name=sub_best_match).first()
                    model['sub_area'] = sub_area.name
                    best_match = sub_area.area.name
                update_area_mismatch(model, 'Invalid area name', 'area')
                update_area_mismatch(model, 'Invalid sub area name', 'sub_area')
            if best_match:
                area = Area.objects.filter(name=best_match).filter(country_filter).first()
                if area:
                    return area
                else:
                    validation_errors.append('Invalid area name')
            else:
                validation_errors.append('Invalid area name')
    else:
        validation_errors.append('Missing field: area')
    return None

def get_sub_area(model, validation_errors, area=None):
    sub_area_id_or_name = str(model.get('sub_area'))
    if isinstance(sub_area_id_or_name, dict):
        if 'id' in sub_area_id_or_name:
            sub_area_id_or_name = sub_area_id_or_name['id']
        elif 'name' in sub_area_id_or_name:
            sub_area_id_or_name = sub_area_id_or_name['name']
    if area:
        area_filter = Q(area = area)
    else:
        area_filter = Q()
    if sub_area_id_or_name:
        if sub_area_id_or_name.isdigit():
            try:
                return SubArea.objects.get(id=sub_area_id_or_name)
            except SubArea.DoesNotExist:
                validation_errors.append('Invalid sub area ID')
        else:
            # Fuzzy search for the sub area name
            sub_area_names = [sub_area.name for sub_area in SubArea.objects.filter(area_filter)]
            best_match, score = fuzzy_search(sub_area_id_or_name, 'SubArea', filter=Q(sub_area_name_arabic__in= sub_area_names))
            if not score or score < 75:
                if 'Invalid area name' not in model.get('area_mismatch', ''):
                    update_area_mismatch(model, 'Invalid area name', 'area')
                    update_area_mismatch(model, 'Invalid sub area name', 'sub_area')
            if best_match:
                if score < 75:
                    try:
                        sub_area = SubArea.objects.get(area=area, is_default_for_area=True)
                        return sub_area
                    except SubArea.DoesNotExist:
                        pass
                try:
                    sub_area = SubArea.objects.filter(area_filter).filter(name=best_match).last()
                    return sub_area
                except SubArea.DoesNotExist:
                    validation_errors.append('Invalid sub area name')
            else:
                validation_errors.append('Invalid sub area name')
    else:
        validation_errors.append('Missing field: sub_area')
    return None

def get_country(model, validation_errors):
    country_name_or_code = str(model.get('country'))
    if isinstance(country_name_or_code, dict):
        if 'code' in country_name_or_code:
            country_name_or_code = country_name_or_code['code']
        elif 'name' in country_name_or_code:
            country_name_or_code = country_name_or_code['name']
    if country_name_or_code:
        if model['country'] in ['Palestinian Territory, Occupied' , 'Israel', 'اسرائيل', 'IL', 'فلسطين']:
            country_name_or_code = 'PS'
        elif model['country'] in ['Jordan', 'الأردن', 'أردن']:
            country_name_or_code = 'JO'
        elif model['country'] in ['UAE', 'الامارات', 'الإمارات', 'الإمارات العربية المتحدة', 'الامارات العربية المتحدة', 'Emirates', 'United Arab Emirates']:
            country_name_or_code = 'UAE'
        elif model['country'] in ['Oman', 'عُمان','عمان']: 
            country_name_or_code = 'Om'
        if len(country_name_or_code) < 4:
            country = Country.objects.filter(code=country_name_or_code).first()
            if not country:
                validation_errors.append('Invalid country code')
                update_area_mismatch(model, 'Invalid country code', 'country')
            return country
        else:
            # Fuzzy search for the country name
            country_names = [country.name for country in Country.objects.all()]
            best_match, score = fuzzy_search(country_name_or_code, 'Country')
            if score and score < 75:
                update_area_mismatch(model, 'Invalid country name', 'country')
            if best_match:
                country = Country.objects.filter(name=best_match).first()
                if not country:
                    validation_errors.append('Invalid country name')
                return country
            else:
                validation_errors.append('Invalid country name')
        return None
    else:
        validation_errors.append(_('Missing field: country'))
    return None

def get_role(model, validation_errors):
    role_name_or_id = str(model.get('role'))
    if isinstance(role_name_or_id, dict):
        if 'role_id' in role_name_or_id:
            role_name_or_id = role_name_or_id['role_id']
        elif 'role_name' in role_name_or_id:
            role_name_or_id = role_name_or_id['role_name']
    if not role_name_or_id:
        validation_errors.append('Missing field: role')
        return None
    
    if role_name_or_id.isdigit():
        try:
            return Role.objects.get(role_id=role_name_or_id)
        except Role.DoesNotExist:
            validation_errors.append('Invalid role ID')
            return None
    else:
        try:
            return Role.objects.get(name=role_name_or_id)
        except Role.DoesNotExist:
            validation_errors.append('Invalid role name')
            return None
        
def validate_foreign_keys(data, required_fields, validation_errors):
    foreign_key_objects = {}
    if 'country' in data:
        foreign_key_objects['country'] = get_country(data, validation_errors)

    if 'area' in data:
        foreign_key_objects['area'] = get_area(data, validation_errors, foreign_key_objects.get('country')) if 'country' in foreign_key_objects else get_area(data, validation_errors, None)

    if 'sub_area' in data:
        foreign_key_objects['sub_area'] = get_sub_area(data, validation_errors, foreign_key_objects.get('area')) if 'area' in foreign_key_objects else get_sub_area(data, validation_errors, None)

    if 'role' in data:
        foreign_key_objects['role'] = get_role(data, validation_errors)

    if 'order_type' in data:
        foreign_key_objects['order_type'] = get_order_type(data, validation_errors)

    if 'branch_page' in data and data['branch_page']:
        foreign_key_objects['branch_page'] = get_branch_page(data, validation_errors)

    if 'driver_pricelist' in data and data['driver_pricelist']:
        foreign_key_objects['driver_pricelist'] = get_pricelist(data['driver_pricelist'])


    return foreign_key_objects

def get_branch_page(data, validation_errors,company=None):
    try:
        if str(data['branch_page']).isdigit():
            return BranchPage.objects.get(id=int(data['branch_page']))
        elif company:
            return BranchPage.objects.get(name=data['branch_page'], company=company)
        else:   
            return None
    except BranchPage.DoesNotExist:
        validation_errors.append('Invalid branch_page ID')
        return None

def get_order_type(model, validation_errors):
    order_type = str(model.get('order_type'))
    if not order_type:
        validation_errors.append('Missing field: order_type')
        return None
    
    if order_type.isdigit():
        try:
            return OrderType.objects.get(id=order_type)
        except OrderType.DoesNotExist:
            validation_errors.append('Invalid order_type ID')
            return None
    else:
        try:
            return OrderType.objects.get(code=order_type)
        except OrderType.DoesNotExist:
            validation_errors.append('Invalid order_type code')
            return None

def create_log(model, company, action, created_by, model_id):
    request = get_current_request()
    token_payload = getattr(request, 'auth', {}) if request else {}
    if token_payload:
        impersonated_by = token_payload.get('impersonated_by', '')
        if impersonated_by:
            created_by = f"{created_by} {'(' + impersonated_by + ')'}"
    return Logs.objects.create(
        model=model,
        company=company,
        action=action,
        created_by=created_by,
        model_id=model_id
    )

def create_log_info(log, field_name, old_value, new_value):
    return LogsInfo.objects.create(
        log=log,
        field_name=field_name,
        old_value=old_value,
        new_value=new_value
    )

def get_tracked_fields(company, model):
    return TrackedFields.objects.filter(company=company, model=model).values_list('field_name', flat=True)

def log_tracked_fields(log, values, fields):
    for key, value in values.items():
        if key in fields:
            if isinstance(value, models.Model):
                if key == 'parent_order':
                    value = value.order_sequence
                elif key == 'company':
                    value = value.company_name
                else:
                    value = value.name
            create_log_info(log, key, None, value)

def log_and_update_field(model, key, value, old_value, log, is_tracked):
    if is_tracked:
        if isinstance(old_value, models.Model):
            if key == 'parent_order':
                old_value = old_value.order_sequence
            elif key == 'product_variant':
                old_value = value.variant.name
            elif key == 'company':
                    value = value.company_name
            else:
                old_value = old_value.name
    setattr(model, key, value)
    if is_tracked:
        if isinstance(value, models.Model):
            if key == 'parent_order':
                value = value.order_sequence
            elif key == 'product_variant':
                value = value.variant.name
            elif key == 'company':
                value = value.company_name
            else:
                value = value.name
        if is_tracked:
            create_log_info(log, key, old_value, value)

def delete_log_if_no_changes(log):
    if not LogsInfo.objects.filter(log=log).exists():
        log.delete()

def get_date_next_month():
    current_date = timezone.now()
    current_date = timezone.now()
    new_month = current_date.month % 12 + 1
    new_year = current_date.year + (1 if new_month == 1 else 0)
    return current_date.replace(year=new_year, month=new_month)

def get_date_next_year():
    current_date = timezone.now()
    next_year = current_date.replace(year=current_date.year + 1)
    return next_year

def get_current_month_date_range(subscription_start_date):
    current_date = timezone.now()

    if isinstance(subscription_start_date, datetime.datetime):
        next_subscription_date = subscription_start_date
    else:
        next_subscription_date = datetime.datetime.combine(subscription_start_date, datetime.datetime.min.time())
        next_subscription_date = timezone.make_aware(next_subscription_date)
    while next_subscription_date <= current_date:
        next_subscription_date += relativedelta(months=1)

    start_date = next_subscription_date - relativedelta(months=1)
    end_date = next_subscription_date

    return start_date, end_date

def group_notified_users_by_language(order):
    if not order:
        return {}
    user_ids_by_lang = defaultdict(list)

    users = User.objects.filter(
        Q(company=order.company) &
        (
            Q(role__name__in=['Super Manager', 'Manager', 'Sales Manager']) |
            Q(name=order.created_by)
        )
    ).values_list('player_id', 'lang')
    for player_id, lang in users:
        if player_id:
            user_ids_by_lang[lang].append(player_id)

    return user_ids_by_lang

def send_api_to_delivery_company(delivery_company, end_point, values, return_raw=False):
    url = delivery_company.delivery_company_url + end_point
    try:
        values['params']['context'] = {
            "lang": 'ar_SY' if translation.get_language() == 'ar' else 'en_US'
        }
        response = requests.post(url, json=values, headers={'Content-Type': 'application/json'})
    except requests.exceptions.RequestException as e:
        raise get_error_response('CONNECTION_300', {'error': str(e), 'delivery_company': delivery_company.name, 'end_point': end_point})
    
    return handle_response(response, delivery_company, return_raw)

def handle_response(response, delivery_company, return_raw=False):
    if response is None:
        raise get_error_response('CONNECTION_301', {'delivery_company': delivery_company})

    if response.status_code == 404:
        raise get_error_response('CONNECTION_302', {'delivery_company': delivery_company})

    if response.status_code == 400:
        raise get_error_response('CONNECTION_303', {'delivery_company': delivery_company, 'error': response.text})

    if response.status_code == 401:
        raise get_error_response('CONNECTION_304', {'delivery_company': delivery_company, 'error': response.text})

    if response.status_code == 403:
        raise get_error_response('CONNECTION_305', {'delivery_company': delivery_company, 'error': response.text})

    if response.status_code == 500:
        raise get_error_response('CONNECTION_306', {'delivery_company': delivery_company, 'error': response.text})

    if response.status_code == 200:
        try:
            json_res = response.json()
            result = json_res.get('result')
            if result and result.get('code') == 200:
                return {
                    'success': True,
                    'result': result,
                    'code': result.get('code')
                }
            else:
                if not result:
                    result = json_res.get('error')
                if return_raw:
                    return {
                        'success': False,
                        'result': result,
                        'code': result.get('code')
                    }
                message = result.get('message')
                if message:
                    if message == 'Odoo Server Error':
                        raise get_error_response('CONNECTION_307', {'delivery_company': delivery_company, 'error_code': 400, 'connect_message': result.get('data', {}).get('message')})
                    error_code, connect_message, connect_what_to_do = parse_error_response(message)
                    if error_code and connect_message:
                        raise get_error_response('CONNECTION_307', {'delivery_company': delivery_company, 'error_code': error_code, 'connect_message': connect_message})
                    else:
                        raise get_error_response('CONNECTION_308', {'delivery_company': delivery_company, 'error': message})
                elif result.get('error'):
                    raise get_error_response('CONNECTION_308', {'delivery_company': delivery_company, 'error': result.get('error')})
        except json.JSONDecodeError:
            raise get_error_response('CONNECTION_309', {'delivery_company': delivery_company, 'error': response.text})

    raise get_error_response('CONNECTION_310', {'delivery_company': delivery_company, 'error_code': response.status_code, 'error': response.text})

def map_order_area(order, delivery_company):
    try:
        mapped_area = AreaMap.objects.get(delivery_company=delivery_company, area=order.area).imported_area_id
        mapped_subarea_id = None
        if delivery_company.is_subarea_required:
            mapped_subarea = SubAreaMap.objects.get(delivery_company=delivery_company, sub_area=order.sub_area)
            mapped_subarea_id = mapped_subarea.imported_sub_area_id
            if not mapped_subarea:
                return None, None
            else:
                mapped_area = mapped_subarea.parent_area_id
        return mapped_area, mapped_subarea_id
    except (AreaMap.DoesNotExist, SubAreaMap.DoesNotExist):
        return None, None

def extract_error_code(text):
    match = re.search(r'\(\s*(\d+)\s*\)', text)
    return match.group(1) if match else ''

def extract_error_message(text):
    match = re.search(r"'\s*([^'\[]+)\s*\[", text)
    return match.group(1).strip() if match else ''

def extract_what_to_do(text):
    match = re.search(r"',\s*'(.+?)'\)", text, re.DOTALL)
    return match.group(1).strip() if match else ''

def parse_error_response(response):
    if isinstance(response, list):
        response = response[0]
    error_code = extract_error_code(response)
    connect_message = extract_error_message(response)
    connect_what_to_do = extract_what_to_do(response)
    return error_code, connect_message, connect_what_to_do

def get_origin(request):
    try:
        store_name = request.headers.get('Store-Name')
        mystore = None
        if not store_name:
            origin_name = request.META.get('HTTP_ORIGIN')
            if '.connect-plus.app' in origin_name:
                store_name = origin_name.split('//')[1].split('.connect-plus')[0]
            else:
                mystore_mapping = StoreDomainMapping.objects.filter(store_url=origin_name).first()
                if mystore_mapping:
                    mystore = mystore_mapping.mystore
                    store_name = mystore.name
        # Handle cases for not mapping
        if (store_name and not mystore):
            mystore = MyStore.objects.filter(name=store_name).first()

    except:
        return None
    return mystore

def user_has_group_permission(user, permission_codename):
    for group in user.groups.all():
        if group.permissions.filter(codename=permission_codename).exists():
            return True
    return False 

def update_area_mismatch(order, error_key, fk_key):
    mismatch_message = f"{error_key}: {order[fk_key]}"
    if 'area_mismatch' in order and order['area_mismatch']:
        order['area_mismatch'] += f", {mismatch_message}"
    else:
        order['area_mismatch'] = mismatch_message

def build_filters(filters):
    query = Q()

    for filter_item in filters:
        operator = filter_item.get('operator')
        filter_list = filter_item.get('filters', [])

        if operator == 'and':
            and_query = Q()
            for sub_filter in filter_list:
                and_query &= build_filters([sub_filter])
            query &= and_query
        elif operator == 'or':
            or_query = Q()
            for sub_filter in filter_list:
                or_query |= build_filters([sub_filter])
            query |= or_query
        else:
            query &= build_single_filter(filter_item)

    return query

def convert_to_utc(value):
    try:
        parsed_datetime = datetime.datetime.strptime(value, "%Y-%m-%dT%H:%M:%S.%fZ")
        return pytz.utc.localize(parsed_datetime)
    except (ValueError, TypeError):
        return value

def to_ms_json_date(dt):
    timestamp_ms = int(dt.timestamp() * 1000)
    return timestamp_ms

def build_single_filter(filter_item):
    field = filter_item.get('field')
    filter_operator = filter_item.get('operator')
    value = filter_item.get('value')

    if field in ['created_at', 'updated_at'] and isinstance(value, str):
        value = convert_to_utc(value)

    if field == 'created_at':
        if value in ['day', 'yesterday', 'week', 'month', 'year']:
            start_date, end_date = get_date_range(value)
            return Q(**{f"{field}__gte": start_date, f"{field}__lte": end_date})
        if filter_operator == 'lte':
            return Q(**{f"{field}__date__lte": value})
        elif filter_operator == 'gte':
            return Q(**{f"{field}__date__gte": value})

    if filter_operator == 'exact':
        return Q(**{f"{field}__exact": value})
    elif filter_operator == 'not_exact':
        return ~Q(**{f"{field}__exact": value})
    elif filter_operator == 'contains':
        return Q(**{f"{field}__icontains": value})
    elif filter_operator == 'not_contains':
        return ~Q(**{f"{field}__icontains": value})
    elif filter_operator == 'isnull':
        return Q(**{f"{field}__isnull": value})
    elif filter_operator == 'not_in':
        return ~Q(**{f"{field}__in": value})
    elif filter_operator == 'not_startswith':
        return ~Q(**{f"{field}__startswith": value})
    elif filter_operator == 'not__gte':
        return ~Q(**{f"{field}__gte": value})
    else:
        return Q(**{f"{field}__{filter_operator}": value})
    
def get_search_filter(search, model_class):
    search_filter = Q()
    if search:
        for field in model_class._meta.get_fields():
            if isinstance(field, (models.CharField, models.TextField, models.EmailField, 
                                  models.DateField, models.BigAutoField)):
                temp = search
                search_filter |= Q(**{f"{field.name}__icontains": temp})
            elif isinstance(field, models.ForeignKey) and 'name' in [related_field.name for related_field in field.related_model._meta.get_fields()]:
                search_filter |= Q(**{f"{field.name}__name__icontains": temp})
            elif isinstance(field, models.ForeignKey) and 'company_name' in [related_field.name for related_field in field.related_model._meta.get_fields()]:
                search_filter |= Q(**{f"{field.name}__company_name__icontains": temp})
        if model_class == Order:
            search_filter |= Q(**{"parent_order__order_sequence__icontains": temp})
            search_filter |= Q(**{"cloned_orders__order_sequence__icontains": temp})
            search_filter |= Q(**{"order_lines__product_variant__variant__name__icontains": temp})
        if model_class == WarehouseTracking:
            search_filter |= Q(**{"order__order_sequence__icontains": temp})
            search_filter |= Q(**{"warehouse_variant__variant__name__icontains": temp})
        if model_class == Product:
            search_filter |= Q(**{"tags__name__icontains": temp})
        if model_class == WarehouseVariant:
            search_filter |= Q(**{"product_locations__location__name__icontains": temp})
        return search_filter
    else:
        return Q()

def get_records(model, json_query, user, company_filter=None, custom_filter=None, context=None, custom_order_by=None, custom_order_default=1000):
    search = json_query.get('search')
    filters = json_query.get('filters', [])
    offset = json_query.get('offset', 0)
    size = json_query.get('size', 300)
    sum_fields = json_query.get('sum_fields', [])
    order_by = json_query.get('order_by')

    q_filter = build_filters(filters)

    model_class = apps.get_model('api', model)

    
    search_filter = get_search_filter(search, model_class)
    q_filter &= search_filter

    if user and 'company' in [field.name for field in model_class._meta.get_fields()]:
        q_filter &= Q(company=user.company)
    elif company_filter:
        q_filter &= company_filter
    permission_string = f'view_self_{model.lower()}'
    if user and user_has_group_permission(user.user, permission_string):
        q_filter &= Q(created_by_user=user)

    if custom_filter:
        q_filter &= custom_filter

    queryset = model_class.objects.filter(q_filter)

    # Apply database optimizations if provided as parameters
    related_fields = getattr(model_class, 'RELATED_FIELDS', None)
    prefetch_related = getattr(model_class, 'PREFETCH_RELATED', None)
    if related_fields:
        queryset = queryset.select_related(*related_fields)
    
    if prefetch_related:
        queryset = queryset.prefetch_related(*prefetch_related)

    if custom_order_by:
        queryset = queryset.annotate(
            custom_order=Case(
                *custom_order_by,
                output_field=IntegerField(),
                default=custom_order_default 
            )
        ).order_by('custom_order').distinct()
    elif order_by:
        queryset = queryset.order_by(order_by).distinct()
    else:
        order_by = '-id'
        queryset = queryset.order_by(order_by).distinct()

    sum_results = {}
    if model_class == Order and user:
        # Build a map for remove_distinct for each field
        remove_distinct_map = {}
        for item in sum_fields:
            for field in item["fields"]:
                remove_distinct_map[field] = item.get('remove_distinct', False)

        sum_fields_values = [field for item in sum_fields for field in item["fields"]]
        company_currency = user.company.company_area.country.currency
        sum_query = queryset.values('id', 'currency').annotate(**{
            f'{sum_field}_sum': Sum(sum_field, distinct=(not remove_distinct_map.get(sum_field, False))) for sum_field in sum_fields_values
        })
        conversion_rates = {}
        for aggregate in sum_query:
            order_currency = aggregate['currency']
            if order_currency not in conversion_rates:
                conversion_rates[order_currency] = get_conversion_rate(order_currency, company_currency)

            for sum_field in sum_fields:
                for sum_field_value in sum_field['fields']:
                    total_sum = aggregate.get(f'{sum_field_value}_sum', 0) or 0
                    if sum_field.get('is_quantity', False):
                        converted_sum = Decimal(total_sum)
                    elif sum_field_value != 'profit':
                        conversion_rate = conversion_rates[order_currency]
                        converted_sum = Decimal(total_sum) * conversion_rate
                        converted_sum = converted_sum.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                    else:
                        converted_sum = Decimal(total_sum)
                        converted_sum = converted_sum.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

                    if sum_field['key'] not in sum_results:
                        sum_results[sum_field['key']] = 0
                    sum_results[sum_field['key']] += converted_sum

    paginated_queryset = queryset[offset:offset + size]
    
    serializer = MODEL_SERIALIZER_MAP.get(model_class)

    sum_results = {item: float(value) for item, value in sum_results.items()}

    return {
            'total_records': queryset.count(),
            'loaded_records': paginated_queryset.count(),
            'records': serializer(paginated_queryset, many=True, context=context).data if context else serializer(paginated_queryset, many=True).data,
            'sum_results': sum_results
        }


def handle_stock(order, next_status=None):
    def get_company_conf(order):
        try:
            return CompanyConf.objects.get(company=order.company)
        except CompanyConf.DoesNotExist:
            raise get_error_response(
                'GENERAL_002', 
                {'model': 'CompanyConf', 'field': 'company', 'value': order.company.company_id}
            )
    
    def create_warehouse_tracking(variant, physical_change, virtual_change):
        try:
            with transaction.atomic():
                physical_quantity = 0
                virtual_quantity = 0
                if physical_change:
                    physical_quantity = physical_change
                if virtual_change:
                    virtual_quantity = virtual_change
                if physical_quantity or virtual_quantity:
                    warehouse_tracking = WarehouseTracking.objects.create(
                        warehouse_variant=variant,
                        physical_change=physical_quantity,
                        virtual_change=virtual_quantity,
                        previous_physical = variant.physical_quantity - physical_change,
                        previous_virtual = variant.virtual_quantity - virtual_change,
                        order=order,
                    )
        except Exception as e:
            raise e


    def update_variant_stock(variant, physical_change, virtual_change, raise_error):
        variant.physical_quantity += physical_change
        variant.virtual_quantity += virtual_change
        if variant.physical_quantity < 0 or (variant.virtual_quantity < 0 and raise_error):
            raise get_error_response('WAREHOUSE_1000', {})
        create_warehouse_tracking(variant, physical_change, virtual_change)
        variant.save()

    def handle_physical_deduction(order_line, company_conf):
        physical_deduct_amount = order_line.quantity - order_line.physical_deducted
        virtual_deduct_amount = order_line.quantity - order_line.virtual_deducted

        update_variant_stock(
            order_line.product_variant,
            physical_change=-physical_deduct_amount,
            virtual_change=-virtual_deduct_amount,
            raise_error=company_conf.raise_error_on_stock
        )
        order_line.physical_deducted += physical_deduct_amount
        order_line.virtual_deducted += virtual_deduct_amount
        order_line.save()

    def handle_virtual_deduction(order_line, company_conf):
        virtual_deduct_amount = order_line.quantity - order_line.virtual_deducted
        update_variant_stock(
            order_line.product_variant,
            physical_change=0,
            virtual_change=-virtual_deduct_amount,
            raise_error=company_conf.raise_error_on_stock
        )
        order_line.virtual_deducted += virtual_deduct_amount
        order_line.save()

    def handle_return_or_cancel(order, company_conf):
        for order_line in order.order_lines.all():
            update_variant_stock(
                order_line.product_variant,
                physical_change=order_line.physical_deducted,
                virtual_change=order_line.virtual_deducted,
                raise_error=company_conf.raise_error_on_stock
            )
            order_line.physical_deducted = 0
            order_line.virtual_deducted = 0
            order_line.save()
                
    def get_physical_statuses(company_conf):
        statuses = ['with_delivery_company', 'with_driver','completed','money_received','money_collected']
        if company_conf.deduct_physical_quantity.code == 'ready_for_delivery':
            statuses.append('ready_for_delivery')
        return statuses
    
    def get_virtual_statuses(company_conf):
        statuses = ['with_delivery_company', 'ready_for_delivery', 'preparing']
        if company_conf.deduct_virtual_quantity.code == 'new_order':
            statuses.append('new_order')
        return statuses

    company_conf = get_company_conf(order)

    if not company_conf.use_stock:
        return

    if order.status.code in get_physical_statuses(company_conf) or next_status in get_physical_statuses(company_conf):
        for order_line in order.order_lines.all():
            handle_physical_deduction(order_line, company_conf)
    elif order.status.code in get_virtual_statuses(company_conf):
        for order_line in order.order_lines.all():
            handle_virtual_deduction(order_line, company_conf)
    elif order.status.code in ['returned', 'cancelled', 'deleted','completed_returned']:
        handle_return_or_cancel(order, company_conf)
    else:
        pass

def normalize_country_and_mobile(order):
    country_mappings = {
        ('Palestinian Territory, Occupied', 'فلسطين', 'Palestine', 'PS'): ('PS', '+970'),
        ('Israel', 'اسرائيل', 'IL'): ('PS', '+972'),
        ('Jordan', 'الأردن', 'أردن', 'JO'): ('JO', '+962'),
        ('UAE', 'الامارات', 'الإمارات', 'الإمارات العربية المتحدة', 'الامارات العربية المتحدة', 'Emirates', 'United Arab Emirates'): ('UAE', '+971'),
        ('Oman', 'عمان','عُمان', 'Om'): ('Om', '+968'),

    }

    for countries, (normalized_country, default_code) in country_mappings.items():
        if order.get('country', None) in countries:
            order['country'] = normalized_country
            order['country_code'] = default_code
            break
    else:
        order['country_code'] = 'TEMP'

    if 'customer_mobile' in order and (order['customer_mobile'] is None or pd.isna(order['customer_mobile'] or order['customer_mobile'] != '')):
        order['customer_mobile'] = None
        return order

    mobile = str(order['customer_mobile']).replace(" ", "").replace("-", "").strip()
    country_code = order['country_code']

    if order.get('country', None) == 'PS':
        if mobile.startswith('+970'):
            country_code = '+970'
            mobile = mobile[4:]
        elif mobile.startswith('970'):
            country_code = '+970'
            mobile = mobile[3:]
        elif mobile.startswith('0970'):
            country_code = '+970'
            mobile = mobile[4:]
        elif mobile.startswith('+972'):
            country_code = '+972'
            mobile = mobile[4:]
        elif mobile.startswith('972'):
            country_code = '+972'
            mobile = mobile[3:]
        elif mobile.startswith('0972'):
            country_code = '+972'
            mobile = mobile[4:]
        elif mobile.startswith('00970'):
            country_code = '+970'
            mobile = mobile[5:]
        elif mobile.startswith('00972'):
            country_code = '+972'
            mobile = mobile[5:]
    mobile = mobile.lstrip('+')
    if mobile.startswith(country_code.replace('+', '')):
        mobile = mobile[len(country_code.replace('+', '')):]
    elif mobile.startswith('00'):
        extracted_code = '+' + mobile[2:5]
        if extracted_code == country_code:
            mobile = mobile[5:]
        else:
            mobile = mobile[3:]
    if not mobile.startswith('0') :
        mobile = '0' + mobile
    order['customer_mobile'] = mobile
    order['country_code'] = country_code

    return order

def handle_client_payment(order_id, amount, customer_name, provider):
    try:
        amount=float(amount)
        order = Order.objects.get(id=order_id)
        order.paid = True
        order.is_online_payment = True
        order.customer_payment = amount / 100
        order.save()
        ClientPayment.objects.create(order=order, company=order.company, amount=amount/100, customer_name=customer_name, provider=provider)
        if order.olivery_sequence is not None:
            try:
                connection_user = order.connection.credentials
                params = {
                    "jsonrpc": "2.0",
                    "params": {
                        'db': order.delivery_company.delivery_company_db,
                        'login': connection_user.company_username,
                        'password': connection_user.company_password,
                        'order_id': order.olivery_sequence,
                        'vals': {
                            'paid': order.paid,
                            'delivery_cost_on_customer': False if order.paid else True
                        }
                    }
                }
                send_api_to_delivery_company(order.delivery_company, '/edit_order', params, return_raw=True)
            except Exception as e:
                pass
        company_wallet, _ = CompanyWallet.objects.get_or_create(company=order.company)
        if order.currency == 'JOD':
            company_wallet.jod_balance += amount * 0.0095
            company_wallet.save()
        elif order.currency == 'ILS':
            company_wallet.ils_balance += amount * 0.0095
            company_wallet.save()
    except Exception as e:
        print(f'Error handling client payment: {str(e)}')
        pass

def round_two_decimals(value):
    return value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

def get_connection(id):
    try:
        return ConnectDeliveryCompany.objects.get(id=id)
    except ConnectDeliveryCompany.DoesNotExist:
        return None

def get_pricelist(id):
    try:
        return Pricelist.objects.get(id=id)
    except Pricelist.DoesNotExist:
        return None
    
def get_delivery_fee_util(company, connection, area):
    if not connection:
        try:
            pricelist = Pricelist.objects.get(company=company, connection=None)
            pricelist_item = PricelistItem.objects.get(pricelist=pricelist, to_area=area)
        except PricelistItem.DoesNotExist:
            raise get_error_response('CONNECTION_320', {'delivery_company': 'Default Pricelist', 'area': area.name})
        return pricelist_item.price
    
    try:
        pricelist = Pricelist.objects.get(company=company, connection=connection)
        pricelist_item = PricelistItem.objects.filter(pricelist=pricelist, to_area=area, from_area=connection.area).last()
    except PricelistItem.DoesNotExist:
        raise get_error_response('CONNECTION_320', {'delivery_company': connection.delivery_company.name, 'area': area.name})
    return pricelist_item.price

def build_request_params(request_template, instance, prerequisite_data={}):
    if '-' in request_template.model_name:
        instance = get_instance_value(request_template.model_name, None)

    if request_template.prerequisite_links.all().count() > 0:
        prerequisite_data = handle_prerequisites_apis(request_template, instance)
    else:
        prerequisite_data = {}
        
    process_function = request_template.process_function
    processed_data = {}
    if process_function:
        try:
            local_vars = {"instance": instance, "prerequisite_data": prerequisite_data, "processed_data": processed_data}
            exec(process_function, {}, local_vars)
            processed_data = local_vars.get("processed_data", '')
        except Exception as e:
            print(f"Error executing process_function: {e}")

    headers = replace_placeholders(request_template.headers, instance, prerequisite_data, processed_data)
    body = {}
    endpoint = request_template.endpoint
    if '(' in endpoint and ')' in endpoint:
        endpoint = replace_placeholders(request_template.endpoint, instance, prerequisite_data, processed_data)
    if request_template.method.upper() == 'GET' and request_template.is_querystring:
        endpoint += '?' + urlencode(replace_placeholders(request_template.body, instance, prerequisite_data, processed_data))
    else:
        body = replace_placeholders(request_template.body, instance, prerequisite_data, processed_data)

    return {
        'endpoint': endpoint,
        'headers': headers,
        'body': body,
        'prerequisite_data': prerequisite_data
    }

def handle_prerequisites_apis(request_template, instance):
    prerequisite_data = {}
    for prerequisite in request_template.prerequisite_links.all().order_by('position'):
        input = replace_placeholders(prerequisite.input, instance, prerequisite_data)
        params = build_request_params(prerequisite.prerequisite, input, prerequisite_data)
        response = send_dynamic_api_request(prerequisite.prerequisite, params)
        prerequisite_data[prerequisite.prerequisite.name] = response.get('data')

    return prerequisite_data

def replace_placeholders(template_data, instance, prerequisite_data, processed_data={}):
    if isinstance(template_data, dict):
        return {k: replace_placeholders(v, instance, prerequisite_data, processed_data) for k, v in template_data.items()}
    elif isinstance(template_data, list):
        return [replace_placeholders(v, instance, prerequisite_data, processed_data) for v in template_data]
    elif isinstance(template_data, str):
        template_data = template_data.replace('#(', '__OPEN_PAREN__')
        template_data = template_data.replace('#)', '__CLOSE_PAREN__')
        matches = re.findall(r'\((.*?)\)', template_data)
        if not matches:
            template_data = template_data.replace("__OPEN_PAREN__", "(")
            template_data = template_data.replace("__CLOSE_PAREN__", ")")
            return template_data

        values = {match: get_instance_value(match, instance, prerequisite_data, processed_data) for match in matches}
        if len(matches) == 1 and template_data.strip() == f"({matches[0]})":
            return values[matches[0]]
        for key, value in values.items():
            template_data = template_data.replace(f"({key})", str(value))
        template_data = template_data.replace("__OPEN_PAREN__", "(")
        template_data = template_data.replace("__CLOSE_PAREN__", ")")
        
        return template_data
    return template_data

def get_instance_value(placeholder, instance, prerequisite_data={}, processed_data={}):
    attrs = placeholder.split(".")
    root_attr = attrs.pop(0)
    if 'sub_area_map' == root_attr:
        try:
            value = SubAreaMap.objects.get(sub_area=instance.sub_area, delivery_company=instance.connection.delivery_company)
        except SubAreaMap.DoesNotExist:
            raise get_error_response('AREA_900')
    elif 'area_map' == root_attr:
        try:
            value = AreaMap.objects.get(area=instance.area, delivery_company=instance.connection.delivery_company)
        except AreaMap.DoesNotExist:
            raise get_error_response('AREA_900')
    elif 'sender_area_map' == root_attr:
        try:
            value = AreaMap.objects.get(area=instance.connection.area, delivery_company=instance.connection.delivery_company)
        except AreaMap.DoesNotExist:
            raise get_error_response('AREA_900')
    elif 'sender_sub_area_map' == root_attr:
        try:
            sub_area = SubArea.objects.get(area=instance.connection.area, is_default_for_area=True)
            value = SubAreaMap.objects.get(sub_area=sub_area, delivery_company=instance.connection.delivery_company)
        except (AreaMap.DoesNotExist, SubArea.DoesNotExist) as e:
            raise get_error_response('AREA_900')
    elif '-' in placeholder:
        placeholder = placeholder.replace('(', '').replace(')', '')
        model_name = placeholder.split('-')[0]
        id = placeholder.split('-')[1]
        model_instance = apps.get_model('api', model_name=model_name)
        value = model_instance.objects.get(id=id)
        return value
    elif 'prerequisite_data' == root_attr:
        value = prerequisite_data
    elif 'processed_data' == root_attr:
        value = processed_data
    elif 'self' == root_attr:
        value = instance
    elif 'str' == root_attr:
        parse_to = 'str'
        value = get_instance_value(".".join(attrs), instance, prerequisite_data, processed_data)
        return str(value) if value is not None else ''
    elif 'int' == root_attr:
        parse_to = 'int'
        value = get_instance_value(".".join(attrs), instance, prerequisite_data, processed_data)
        return int(value) if value is not None else 0
    elif 'float' == root_attr:
        parse_to = 'float'
        value = get_instance_value(".".join(attrs), instance, prerequisite_data, processed_data)
        return float(value) if value is not None else 0.0
    elif 'bool' == root_attr:
        parse_to = 'bool'
        value = get_instance_value(".".join(attrs), instance, prerequisite_data, processed_data)
        return bool(value) if value is not None else False
    elif 'calc_date' == root_attr:
        now = timezone.now()
        format_attr = attrs.pop(0)
        new_placeholder = ".".join(attrs)

        days = get_instance_value(new_placeholder, instance, prerequisite_data, processed_data)
        if isinstance(days, (int, float)):
            date = now + timedelta(days=days)
        elif isinstance(days, datetime):
            date = days
        else:
            date = now
        if format_attr.startswith('strftime:'):
            format_string = format_attr[len('strftime:'):]
            return date.strftime(format_string)
        elif format_attr == 'unix':
            return int(date.timestamp())
        elif format_attr == 'msdate':
            return to_ms_json_date(datetime.datetime.strptime(date.strftime("%d.%m.%Y %H:%M:%S,%f"),'%d.%m.%Y %H:%M:%S,%f')   )
        elif format_attr == 'now':
            return now
        else:
            return None
    else:
        value = getattr(instance, root_attr, None)  

    for attr in attrs:
        if isinstance(value, dict):
            value = value.get(attr, None)
        else:
            value = getattr(value, attr, None)
        
        if value is None:
            return None
    return value

def send_dynamic_api_request(request_template, params):
    delivery_company = request_template.delivery_company
    end_point = params.get('endpoint', None)
    if end_point and (end_point.startswith('http://') or end_point.startswith('https://')):
        full_url = end_point
    else:
        full_url = f'{delivery_company.delivery_company_url}{end_point}'
    headers = params.get('headers', {})
    body = params.get('body', {})
    request_data = {}
    method = request_template.method.upper()
    if not end_point:
        raise get_error_response('CONNECTION_303', {'delivery_company': delivery_company, 'error': f'Endpoint for {request_template.name} was not found'})
    
    response_template = request_template.response_template
    request_context = {}
    if not request_template.return_static:
        if hasattr(response_template, 'iterate_pages') and response_template.iterate_pages:
            return handle_paginated_response(request_template, params)
        request_context = {
            "endpoint": full_url,
            "headers": headers,
            "body": body,
            "prerequisite_data": params.get('prerequisite_data', {}),
        }
        print(request_context)
        if headers.get('Content-Type') == 'multipart/form-data':
            request_data = {'data': body}
            headers.pop('Content-Type', None)
        else:
            request_data = {'json': body}
        if method == 'GET':
            response = requests.get(full_url, headers=headers, params=body)
        elif method == 'POST':
            response = requests.post(full_url, headers=headers, **request_data)
        elif method == 'PUT':
            response = requests.put(full_url, headers=headers, **request_data)
        print(response.text)
    else:
        response=MockResponse(json_data=body, status_code=200)
    response_schema = {
        "success_type": response_template.success_type,
        "success_values": response_template.success_values,
        "failure_values": response_template.failure_values,
        "success_path": response_template.success_path,
        "data_path": response_template.data_path,
        "data_mapping": response_template.data_mapping,
        "error_path": response_template.error_path,
        "process_function": response_template.process_function,
        "request_context": request_context,
    }

    serializer = DynamicResponseSerializer(response_schema=response_schema)
    processed_response = serializer.parse_response(response)
    if not processed_response.get('success'):
        raise get_error_response('CONNECTION_303', {'delivery_company': delivery_company, 'error': processed_response.get('data')})
    
    return processed_response

def handle_paginated_response(request_template, params):
    delivery_company = request_template.delivery_company
    end_point = params.get('endpoint', None)
    headers = params.get('headers', {})
    body = params.get('body', {})
    method = request_template.method.upper()
    if not end_point:
        raise get_error_response('CONNECTION_303', {'delivery_company': delivery_company, 'error': 'Endpoint is None in paginated request'})
    
    response_template = request_template.response_template
    iterate_pages = response_template.iterate_pages if hasattr(response_template, 'iterate_pages') else False
    pagination_field = response_template.pagination_field if iterate_pages else None
    current_page = 1
    if end_point and (end_point.startswith('http://') or end_point.startswith('https://')):
        full_url = end_point
    else:
        full_url = f'{delivery_company.delivery_company_url}{end_point}'
    all_data = []
    seen_data = set()
    start_time = time.time()
    
    while True:
        if iterate_pages:
            parsed_url = urllib.parse.urlparse(end_point)
            query_params = dict(urllib.parse.parse_qsl(parsed_url.query))
            if pagination_field:
                query_params[pagination_field] = str(current_page)
            updated_endpoint = f"{full_url.split('?')[0]}?{urllib.parse.urlencode(query_params)}"
        else:
            updated_endpoint = f"{full_url}"

        request_context = {
            "endpoint": updated_endpoint,
            "headers": headers,
            "body": body,
            "prerequisite_data": params.get('prerequisite_data', {}),
            "current_page": current_page
        }
        if method == 'GET':
            response = requests.get(updated_endpoint, headers=headers)
        elif method == 'POST':
            response = requests.post(updated_endpoint, headers=headers, json=body)
        elif method == 'PUT':
            response = requests.put(updated_endpoint, headers=headers, json=body)
        else:
            raise get_error_response('CONNECTION_303', {'delivery_company': delivery_company, 'error': f'Invalid method for {request_template.name}: {method}'})

        response_schema = {
            "success_type": response_template.success_type,
            "success_values": response_template.success_values,
            "failure_values": response_template.failure_values,
            "success_path": response_template.success_path,
            "data_path": response_template.data_path,
            "data_mapping": response_template.data_mapping,
            "error_path": response_template.error_path,
            "process_function": response_template.process_function,
            "request_context": request_context,
        }

        serializer = DynamicResponseSerializer(response_schema=response_schema)
        processed_response = serializer.parse_response(response)

        if not processed_response.get('success'):
            raise get_error_response('CONNECTION_303', {'delivery_company': delivery_company, 'error': processed_response.get('data')})

        data = processed_response.get('data', [])
        if not isinstance(data, list):
            data = [data]
        unique_data = []
        for item in data:
            if isinstance(item, dict):
                item_tuple = tuple(sorted(item.items()))
            else:
                item_tuple = tuple([item])

            if item_tuple not in seen_data:
                unique_data.append(item)
                seen_data.add(item_tuple)

        all_data.extend(unique_data)
        if data:
            current_page += 1
        else:
            break

        if time.time() - start_time > 120:
            print("Time limit reached, stopping loop.")
            break

    return {'success': True, 'data': all_data}

def process_driver_orders_wrapper(driver_orders, user, auto_send=False):
    success_messages = []
    fail_messages = []

    for driver_id, order_ids in driver_orders:
        try:
            driver = User.objects.get(id=driver_id)
        except User.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'User', 'field': 'id', 'value': driver_id})

        sequences, driver_fail = process_driver_orders(driver, order_ids, user)
        fail_messages.extend(driver_fail)

        for sequence in sequences:
            success_messages.append({
                'sequence': sequence, 
                'delivery_company': driver.name, 
                'message': _('Sent To Driver') if not auto_send else _('Sent To Driver through Auto-Send')
            })

    return success_messages, fail_messages

def process_delivery_orders_wrapper(delivery_company_orders, user, delivery_request_status, auto_send=False):
    fail_messages = []
    success_messages = []
    integration_success_messages = []
    integration_failed_messages = []
    olivery_fail_messages = []
    olivery_success_messages = []

    for connection_id, order_ids in delivery_company_orders.items():
        try:
            connection = ConnectDeliveryCompany.objects.get(id=connection_id)
        except ConnectDeliveryCompany.DoesNotExist:
            fail_messages.append(get_error_response('GENERAL_002', {'model': 'ConnectDeliveryCompany', 'field': 'id', 'value': connection_id}))
            continue

        if connection.delivery_company.is_olivery_company:
            olivery_success_messages, olivery_fail_messages = process_delivery_company_orders(connection, order_ids, user,delivery_request_status, auto_send)
        else:
            integration_success_messages, integration_failed_messages = process_dynamic_integrations(connection, order_ids, user, delivery_request_status, auto_send)

        success_messages.extend(olivery_success_messages)
        fail_messages.extend(olivery_fail_messages)
        success_messages.extend(integration_success_messages)
        fail_messages.extend(integration_failed_messages)

    return success_messages, fail_messages

def process_delivery_company_orders(connection, order_ids, user, delivery_request_status, auto_send=False):
    failed_orders = []
    orders_list = []
    fail_messages = []
    for order_id in order_ids:
        try:
            order = Order.objects.get(id=order_id)
            update_order_delivery_company_status(order, connection)

            delivery_fee = calculate_delivery_fee(order, connection)
            adjust_order_fees(order, delivery_fee)

            mapped_area, mapped_subarea = map_order_area(order, connection.delivery_company)
            if not mapped_area:
                raise get_error_response('AREA_900')
            if not mapped_subarea and order.delivery_company.is_subarea_required:
                raise get_error_response('AREA_900')
            handle_stock(order, next_status=delivery_request_status.code)
            connection_user = connection.credentials

            if mapped_area:
                orders_list.append(order_to_json(order, order.company, mapped_area, mapped_subarea, connection_user))
            else:
                mark_order_as_failed(order, failed_orders)

            order.save()
        except ConnectError as e:
            fail_messages.append(handle_sending_connect_error(e.to_dict(), order, name=order.delivery_company.name))
        except Order.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'id', 'value': order_id})

    overall_success_messages, overall_fail_messages = send_orders_to_delivery_company({
        'orders_list': orders_list,
        'connection': connection,
        'delivery_company': connection.delivery_company,
        'connection_user': connection.credentials,
        'company': connection.company,
        'name': user,
        'auto_send': auto_send
    })
    overall_fail_messages.extend(fail_messages)
    return overall_success_messages, overall_fail_messages

def update_order_delivery_company_status(order, connection):
    order.delivery_company = connection.delivery_company
    order.connection = connection
    order.driver = None

def mark_order_as_failed(order, failed_orders):
    failed_orders.append(order.order_sequence)
    order.send_failed = True
    order.save()

def handle_sending_connect_error(error, order, name=''):
    response = error
    return {
        'sequence': order.order_sequence,
        'delivery_company': name,
        'message': response.get('error', {}).get('message'),
        'what_to_do': response.get('error', {}).get('what_to_do'),
        'error_code': response.get('error', {}).get('code')
    }

def update_order_driver_connection(order, driver):
    order.driver = driver
    order.delivery_company = None
    order.connection = None

def update_order_driver_status(order, driver):
    order.status = Status.objects.get(code='with_driver')
    order.delivery_company_status = 'In Progress'
    order.olivery_sequence= ''

def calculate_delivery_fee(order, connection):
    try:
        return get_delivery_fee_util(order.company, connection, order.area) or 0
    except Exception:
        return 0

def adjust_order_fees(order, new_delivery_fee):
    difference = new_delivery_fee - order.delivery_fee
    order.delivery_fee = new_delivery_fee
    if not order.is_fixed_total:
        order.total_cod += difference

def order_to_json(order, company, mapped_area, mapped_sub_area, connection_user):
    company_conf = get_company_conf(company)
    show_order_reference = company_conf.show_reference_in_waybills
    product_note = order.product_info
    if not product_note or not product_note.strip():
        product_note = " + ".join(
            f"{line.quantity}×({line.product_variant.variant.name})"
            for line in order.order_lines.all()
        )
    order_json =  {
        'reference_id': order.order_reference if order.order_reference and show_order_reference else order.order_sequence, 
        'connect_reference_id': order.order_sequence,
        'cost': order.total_cod, 
        'assign_to_business': connection_user.company_username,
        'customer_mobile': order.customer_mobile,
        'customer_name': order.customer_name, 
        'customer_address': order.address, 
        'second_mobile_number': order.customer_second_mobile,
        'customer_area': mapped_area, 
        'note': order.note,
        'product_note': product_note,
        'is_connect_order': True,
        'paid': order.paid,
        'delivery_cost_on_customer': False if order.paid else True
    }
    if mapped_sub_area:
        order_json['customer_sub_area'] = mapped_sub_area
    return order_json

def get_company_conf(company):
    try:
        return CompanyConf.objects.get(company=company)
    except CompanyConf.DoesNotExist:
        return None
    
def process_dynamic_integrations(connection, order_ids, user, delivery_request_status, auto_send=False):
    success_messages = []
    fail_messages = []
    for order_id in order_ids:
        try:
            order = Order.objects.get(id=order_id)
            request_template = RequestTemplate.objects.get(delivery_company=connection.delivery_company, name='create_order')
            update_order_delivery_company_status(order, connection)
            delivery_fee = calculate_delivery_fee(order, connection)
            adjust_order_fees(order, delivery_fee)
            handle_stock(order, next_status=delivery_request_status.code)
            order.save()
            try:
                params = build_request_params(request_template, order)
                response = send_dynamic_api_request(request_template, params)
                if response.get('success'):
                    create_log('Order', order.company, 'Send order to delivery company', user, order.id)
                    delivery_sequence = response.get('data')
                    order = Order.objects.get(id=order_id)
                    order.olivery_sequence = delivery_sequence
                    order.delivery_company_status = 'Waiting'
                    order.delivery_status = StatusMap.objects.get(status_code='waiting')
                    order.status = delivery_request_status
                    order.save()
                    success_messages.append({
                        'sequence': delivery_sequence, 
                        'delivery_company': order.delivery_company.name, 
                        'message': _('Sent To Delivery Company')
                    })
                    if order.channel.lower() == 'woocommerce' and order.order_reference:
                        try:
                            send_order = send_delivery_status_to_woocommerce(order.order_reference, order.delivery_company_status, order.olivery_sequence, order.company)
                            print(send_order)
                        except:
                            pass
            except ConnectError as e:
                fail_messages.append(handle_sending_connect_error(e.to_dict(), order, name=order.delivery_company.name))
            except Exception as e:
                connect_error = get_error_response('GENERAL_007', {'error': str(e)})
                fail_messages.append(handle_sending_connect_error(connect_error.to_dict(), order, name=order.delivery_company.name))
        except RequestTemplate.DoesNotExist:
            connect_error = get_error_response('GENERAL_002', {'model': 'RequestTemplate', 'field': 'name', 'value': 'create_order'})
            fail_messages.append(handle_sending_connect_error(connect_error.to_dict(), order, name=order.delivery_company.name))
    return success_messages, fail_messages

def process_driver_orders(driver, order_ids, user_name, auto_send=False):
    sequences = []
    fail_messages = []
    for order_id in order_ids:
        try:
            order = Order.objects.get(id=order_id)
            update_order_driver_connection(order, driver)
            delivery_fee = calculate_delivery_fee(order, None)
            adjust_order_fees(order, delivery_fee)
            handle_stock(order, next_status='with_driver')
            update_order_driver_status(order, driver)
            create_log('Order', order.company, 'Sent to driver' if auto_send else 'Sent To Driver through Auto-Send', user_name, order.id)
            order.save()
            sequences.append(order.order_sequence)
        except ConnectError as e:
            fail_messages.append(handle_sending_connect_error(e.to_dict(), order, name=driver.name))
        except Order.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'id', 'value': order_id})
    return sequences, fail_messages

def sync_dynamic_order_status(order, orders_for_notification):
    request_template = RequestTemplate.objects.get(delivery_company=order.connection.delivery_company, name='get_order_status')
    params = build_request_params(request_template, order)
    response = send_dynamic_api_request(request_template, params)
    status_code = response.get('data')
    status_map = DeliveryStatusMap.objects.get(imported_status_code=status_code, delivery_company=order.connection.delivery_company)
    if status_map.status and status_map.status.delivery_company_status != order.delivery_company_status:
        order.delivery_status = status_map.status
        order.delivery_company_status = status_map.status.delivery_company_status
        order.updated_by = order.connection.delivery_company.name
        order.save()
        orders_for_notification.append({'order': order, 'vhub_vals': {'state': status_map.status.status_code}})

def reset_warehouse(warehouse):
    variants = WarehouseVariant.objects.filter(warehouse=warehouse)
    for variant in variants:
        variant.virtual_quantity = 0
        variant.physical_quantity = 0
        variant.save()

    order_lines = OrderLine.objects.filter(product_variant__warehouse=warehouse, order__status__code='stuck')
    for order_line in order_lines:
        order_line.virtual_deducted = 0
        order_line.physical_deducted = 0
        order_line.save()

    for order_line in OrderLine.objects.filter(order__company=warehouse.company, order__status__code='with_delivery_company'):
        order_line.physical_deducted = order_line.quantity
        order_line.virtual_deducted = order_line.quantity
        order_line.save()


def send_delivery_status_to_woocommerce(order_id, delivery_status, delivery_sequence, company):
    try:
        wooco_info = WoocommerceInformation.objects.get(company=company)
    except:
        return 
    consumer_key = wooco_info.consumer_key
    consumer_secret = wooco_info.consumer_secret

    url = f"{wooco_info.woocommerce_url}/wp-json/wc/v3/orders/{order_id}"

    auth =(consumer_key, consumer_secret)

    headers = {
        'Content-Type': 'application/json'
    }

    payload = {
        "meta_data": [
            {
                "key": "_delivery_status",
                "value": delivery_status
            },
            {
                "key": "_delivery_sequence",
                "value": delivery_sequence
            }
        ]
    }

    response = requests.put(url, auth=auth, headers=headers, json=payload, verify=False)

def get_delivery_company(delivery_company_id):
    try:
        return DeliveryCompany.objects.get(id=delivery_company_id)
    except DeliveryCompany.DoesNotExist:
        return None
    
def get_status(code):
    try:
        return Status.objects.get(code=code)
    except Status.DoesNotExist:
        return None

def get_company_delivery_request_status(company):
    try:
        delivery_request = CompanyConf.objects.get(company=company)
        if delivery_request.delivery_request_status:
            return delivery_request.delivery_request_status
        else:
            raise Exception(f'Delivery request status for company {company} is not set.')
    except CompanyConf.DoesNotExist:
        raise Exception(f'CompanyConf for company {company} does not exist.')
    
def get_delivery_request_statuses_list():
    return Status.objects.filter(code__in=['with_delivery_company', 'ready_for_delivery'])

def read_and_validate_excel(file, header_mapping, required_fields):
    try:
        if file.content_type == 'text/csv':
            df = pd.read_csv(file, dtype=str)
        else:
            df = pd.read_excel(file, header=0, dtype=str)
    except Exception as e:
        raise get_error_response('GENERAL_007', {'error': str(e)})

    df.columns = df.columns.str.strip()
    df.dropna(how='all', inplace=True)
    
    df.rename(columns=header_mapping, inplace=True)
    df = df[[col for col in required_fields + list(header_mapping.values()) if col in df.columns]]

    missing_fields = [field for field in required_fields if field not in df.columns]
    if missing_fields:
        raise get_error_response('GENERAL_003', {'fields': ', '.join(missing_fields)})
    
    return df
def get_warehouse(id):
    try:
        return Warehouse.objects.get(id=id)
    except Warehouse.DoesNotExist:
        return None
