from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from rest_framework.decorators import *
from ..permissions import *
from ..error.error_model import *
from ..util_functions import *
from django.utils.translation import gettext as _
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_financialreconciliation'])])
def get_financial_reconciliations(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response = get_records('FinancialReconciliation', json_data, user)
    return JsonResponse(response, status=200)


@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_financialreconciliation'])])
def add_financial_reconciliation(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    order_ids = json_data.get('order_ids')
    if not order_ids:
        raise get_error_response('GENERAL_003', {'fields': 'order_ids'})
    percentage = json_data.get('percentage')
    if percentage is None or percentage == '':
        raise get_error_response('GENERAL_003', {'fields': 'percentage'})
    orders = Order.objects.filter(id__in=json_data['order_ids'])
    reconciliation = FinancialReconciliation.objects.create(
        percentage = json_data['percentage'],
        company=user.company
    )
    reconciliation.orders.add(*orders)
    reconciliation_data = FinancialReconciliationSerializer(reconciliation).data
    return JsonResponse({'success': True, 'reconciliation': reconciliation_data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_financialreconciliation'])])
def money_collection(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    connection_id = json_data.get('connection_id',False)
    collection_ids = json_data.get('collection_ids',False)
    if not connection_id:
        raise get_error_response('GENERAL_003', {'fields': 'connection_id'})
    if not collection_ids:
        raise get_error_response('GENERAL_003', {'fields': 'collection_ids'})
    
    connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    try:

        values = {
        'params' : {
            'db':connection.delivery_company.delivery_company_db,
            "login": connection.credentials.company_username,
            "password": connection.credentials.company_password,
            "collection_ids": collection_ids
            }
        }
        response = requests.post(connection.delivery_company.delivery_company_url + '/money_collection', json=values, headers={'Content-Type': 'application/json'})
    except ConnectError as e:
        response = e.to_dict()
        return {
            'success': False,
            'message': response.get('error').get('message'),
            'what_to_do': response.get('error').get('what_to_do')
        }
    return JsonResponse(handle_response(response,connection.delivery_company), status = 200)
