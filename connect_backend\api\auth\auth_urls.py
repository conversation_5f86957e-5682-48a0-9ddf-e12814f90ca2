from django.urls import path
from .auth_apis import *

urlpatterns = [
    path('token', get_token, name='token_obtain'),
    path('token_extend', extend_token_validity, name='token_extend'),
    path('otp', generate_and_send_otp, name='otp_obtain'),
    path('verify_otp', verify_otp, name='verify_otp'),
    path('create_user', create_super_user, name='create_user'),
    path('reset_password_otp', reset_password_otp, name='user_reset_password_otp'),
    path('reset_password', reset_password, name='reset_password'),
    path('get_permissions', get_permissions, name='reset_password'),
    path('get_otp', get_otp, name='get_otp'),
]