from django.core.management.base import BaseCommand
from api.products.product_model import Product, ProductVariant
from api.warehouse.warehouse_model import Warehouse, WarehouseVariant
from api.users.user_apis import Company, CompanyConf
from api.orders.order_model import Status

class Command(BaseCommand):
    help = 'Create default warehouses'

    def handle(self, *args, **kwargs):
        created_variants_count = 0

        for company in Company.objects.all():
            try:
                company_conf = CompanyConf.objects.get(company=company)
            except CompanyConf.DoesNotExist:
                continue
            if not company_conf.default_warehouse:
                warehouse = Warehouse.objects.create(name='Default Warehouse', company=company, country=company.company_area.country, area=company.company_area)
                company_conf.default_warehouse = warehouse
            else:
                warehouse = company_conf.default_warehouse
            company_conf.deduct_physical_quantity = Status.objects.get(code='with_delivery_company')
            company_conf.deduct_virtual_quantity = Status.objects.get(code='preparing')
            company_conf.save()

            WarehouseVariant.objects.filter(warehouse=warehouse).delete()
            for variant in ProductVariant.objects.filter(product__company=company):
                WarehouseVariant.objects.create(warehouse=warehouse, variant=variant, physical_quantity=0, virtual_quantity=0, reserved_quantity=0)
                created_variants_count += 1
                self.stdout.write(self.style.SUCCESS(f"Created default warehouse variant for product: {variant.product.name}"))

        if created_variants_count:
            self.stdout.write(self.style.SUCCESS(f"{created_variants_count} default warehouse variants created successfully."))
        else:
            self.stdout.write(self.style.WARNING("No new warehouse variants created. All products already have variants."))