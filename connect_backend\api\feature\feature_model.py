from django.db import models
from ..users.user_model import Company

class Feature(models.Model):
    key = models.CharField(max_length=100, unique=True)
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

class CompanyFeature(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    feature = models.ForeignKey(Feature, on_delete=models.CASCADE)
    enabled = models.BooleanField(default=False)

    class Meta:
        unique_together = ('company', 'feature')
