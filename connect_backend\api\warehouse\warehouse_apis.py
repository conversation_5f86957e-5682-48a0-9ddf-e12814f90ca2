from django.utils.translation import gettext as _
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from ..util_functions import *
from ..users.user_model import User
from .warehouse_utils import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ..permissions import *
from ..error.error_model import *
from api.mystore.mystore_utils import *
from rest_framework.parsers import MultiPartParser, FormParser
from django.db.models import Subquery

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_warehouse'])])
@csrf_exempt
def add_warehouse(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    values = json_data
    values['created_by'] = user.name
    values['updated_by'] = user.name
    values['company'] = user.company

    validation_errors, foreign_key_objects = validate_warehouse(values)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
    
    values = {**values, **foreign_key_objects}
    warehouse = create_warehouse_util(values)
    serializer = WarehouseSerializer(warehouse)
    return JsonResponse({'success': True, 'warehouse': serializer.data}, status=201)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_warehouse'])])
@csrf_exempt
def get_warehouses(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    q_filter = Q(
        Q(
            Q(warehouse_connection__connection_status__iexact="connected") &
            Q(warehouse_connection__company_id=user.company)
        ) |
        Q(
            Q(reseller__isnull=True) &
            Q(company_id=user.company)
        )
    )
    
    result = get_records('Warehouse', json_data, False, company_filter=q_filter)

    return JsonResponse(result, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['connect_reseller_warehouse'])])
@csrf_exempt
def get_available_warehouses(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    warehouse_ids = CompanyResellerWarehouse.objects.filter(company=user.company).values_list('warehouse', flat=True)
    custom_filter=Q(id__in=warehouse_ids, reseller__isnull=False)
    if not warehouse_ids:
        custom_filter = Q(reseller__isnull=False)
    
    response_data = get_records('Warehouse', json_data, None, custom_filter=custom_filter, context={'show_connection': True, 'company': user.company})
    return JsonResponse(response_data, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['connect_reseller_warehouse'])])
@csrf_exempt
def connect_with_warehouse(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    company = user.company
    reseller_warehouse_id = json_data.get('warehouse_id')
    warehouse = Warehouse.objects.get(id=reseller_warehouse_id)
    connection = CompanyWarehouseConnection.objects.create(company=company, warehouse=warehouse, connection_status="pending")
    connection.save()
    serializer = CompanyWarehouseConnectionSerializer(connection)
    return JsonResponse({'success': True, 'connection': serializer.data}, status=201)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_warehousevariant'])])
@csrf_exempt
def get_warehouse_variants(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    q_filter = Q(
        Q(
            Q(warehouse__warehouse_connection__connection_status__iexact="connected") &
            Q(warehouse__warehouse_connection__company_id=user.company)
        ) |
        Q(
            Q(warehouse__reseller__isnull=True) &
            Q(warehouse__company_id=user.company)
        )
    )
    q_filter &= Q(variant__product__company=user.company)
    result = get_records('WarehouseVariant', json_data, False, company_filter=q_filter)

    return JsonResponse(result, status=200)

@csrf_exempt
def get_warehouse_variants_store(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        mystore = validate_mystore(request)
    except Exception as e:
        raise get_error_response('MYSTORE_1101',{})
            
    result = get_records('WarehouseVariant', json_data, None, company_filter=Q(warehouse__company=mystore.company))
    return JsonResponse(result, status=200)

@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_warehousevariant'])])
@csrf_exempt
@subscription_required
def update_warehouse_stock(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    warehouse_id = json_data.get('warehouse_id')
    warehouse = Warehouse.objects.get(id=warehouse_id)
    variants = json_data.get('variants', [])
    edited_ids = []
    stock_approval = None
    with transaction.atomic():
        for variant in variants:
            edited_ids.append(variant.get('variant'))
            quantity = variant.get('quantity')
            type = variant.get('type')
            variant['updated_by'] = user.name

            if quantity == None or type == None:
                raise get_error_response('GENERAL_003', {'fields': ','.join(['quantity','type'])})
            
            can_bypass_stock_approval = user_has_group_permission(request.user, 'change_stock_without_approval')
            if warehouse.reseller_id is None and can_bypass_stock_approval:
                update_warehouse_stock_util(variant, warehouse_id)
            else:
                status = "pending"
                if can_bypass_stock_approval:
                    status = "pending_warehouse_approval"
                stock_approval = create_aproval_record(variant, user, status, warehouse)
    
    return JsonResponse({'success': True, 'variant_ids': edited_ids,'stock_approval':model_to_dict(stock_approval) if stock_approval else None}, status=200)


@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_stockapproval'])])
@csrf_exempt
def get_stock_approval(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    result = get_records('StockApproval', json_data, user,company_filter=Q(company=user.company))

    return JsonResponse(result, status=200)
        
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_warehouse'])])
def get_dashboard(request):
    json_data = parse_request_body(request)
    if not json_data and json_data !={}:
        raise get_error_response('GENERAL_001',{})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    warehouse_id = json_data.get('warehouse')
    if not warehouse_id:
        raise get_error_response('GENERAL_003', {'fields': ['warehouse']})
    warehouse = Warehouse.objects.filter(
        Q(company=user.company) |
        Q(id__in=Subquery(
            CompanyWarehouseConnection.objects.filter(company=user.company).values('warehouse')
        ))
    ).filter(
        id=warehouse_id
    ).first()
    if not warehouse:
        raise get_error_response('GENERAL_007', {'message': _('Warehouse not found')})

    context = {
        'total_physical_stock': get_total_physical_stock(user, warehouse),
        'total_virtual_stock':get_total_virtual_stock(user, warehouse),
        'products_out_of_stock':get_product_out_of_stock(user, warehouse),
        'products_almost_out_of_stock':get_products_almost_out_of_stock(user, warehouse),
        'stock_tracking':get_warehouse_logs_based_on_date(user, json_data.get('start_date'),json_data.get('end_date'), warehouse)
    }
    return JsonResponse({'success': True, 'context': context}, status=200)
        
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_stockapproval'])])
@csrf_exempt
def update_stock_approval(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
        if 'quantity' in values:
            quantity = values.get('quantity')
            if not is_valid_numerical_quantity(quantity):
                raise get_error_response('PRODUCT_504', {'quantity': quantity})
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    values = json_data.get('values')
    update_approval_record_business(values,user)
    return JsonResponse({'success': True, 'approval_id': values.get('stock_approval_id')}, status=200)
    

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_warehousetracking'])])
@csrf_exempt
def get_warehouse_logs(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    q_filter = Q(
       warehouse_variant__variant__product__company=user.company
    )
    
    result = get_records('WarehouseTracking', json_data, False, custom_filter=q_filter)

    return JsonResponse(result, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['import_excel_warehouse_variants'])])
def import_excel(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    json_data = parse_request_body(request)
    if not json_data.get('excel_url'):
        raise get_error_response('GENERAL_003', {'fields': ['excel_url']})
    if not json_data.get('warehouse_id'):
        raise get_error_response('GENERAL_003', {'fields': ['warehouse_id']})
    
    repeated_variants_message = check_excel_file(json_data.get('excel_url'),user,json_data.get('warehouse_id'))
    if repeated_variants_message and len(repeated_variants_message) > 0:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(repeated_variants_message)})
    StockApproval.objects.create(
                user=user,
                status="pending",
                excel_file=json_data.get('excel_url'),
                company=user.company,
                warehouse=Warehouse.objects.get(id=json_data.get('warehouse_id')),
            )
    
    return JsonResponse({'success': True}, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_stocktaking'])])
@csrf_exempt
def add_stocktaking(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    values = json_data
    values['company'] = user.company
    values['user'] = user.name
    product_items = values.pop('stocktaking_products', [])
    product_instances = []
    for item in product_items:
        warehouse_variant_id = item.pop('warehouse_variant')
        try:
            warehouse_variant = WarehouseVariant.objects.get(id=warehouse_variant_id)
        except WarehouseVariant.DoesNotExist:
            raise ValueError(f"WarehouseVariant with id {warehouse_variant_id} does not exist")
        product = StocktakingProducts.objects.create(warehouse_variant=warehouse_variant,**item)
        product_instances.append(product)
    stocktaking = Stocktaking.objects.create(**values)
    stocktaking.stocktaking_Products.set(product_instances)
    serializer = StocktakingSerializer(stocktaking)
    return JsonResponse({'success': True, 'stocktaking': serializer.data}, status=201)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_stocktaking'])])
@csrf_exempt
def get_stocktaking(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    result = get_records('Stocktaking', json_data, user)

    return JsonResponse(result, status=200)


@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_location'])])
@csrf_exempt
def create_location(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        try:
            user = User.objects.get(user=request.user)
        except User.DoesNotExist:
            raise get_error_response('AUTH_400', {'username': request.user.username})
    
        values = json_data
        location = create_location_util(values)
        serializer = LocationSerializer(location)
        return JsonResponse({'success': True, 'message': 'Location Created Successfully', 'location': serializer.data}, status=201)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_location'])])
@csrf_exempt
def get_locations(request):

    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    q_filter = Q(warehouse__company=user.company)
        
    result = get_records('Location', json_data, user,company_filter=q_filter)

    return JsonResponse(result, status=200)

@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def print_location(request):
    try:
        ids = request.GET.getlist('ids')
        locations = Location.objects.filter(id__in=ids)
        render_data = []
        for location in locations:
            location_barcode_olivery_base64 = generate_barcode_base64(location.name) if location.name else None
            render_data.append({'name':location.name,'location_barcode_olivery_base64':location_barcode_olivery_base64})
        language = translation.get_language() or 'ar'
        language = language.lower()

        meta = {
            'lang':language,
        }
        response = generate_pdf('locations', 'location_barcode', render_data, meta)
        return response
    except Exception as e:
        raise e
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_locationproduct'])])
@csrf_exempt
def add_locations_to_products(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        location_id = json_data['location_id']
        products_info = json_data['products_info']
        location = Location.objects.get(id=location_id)
        for product_info in products_info:
            product_id = product_info['id']
            quantity = product_info['quantity']
            product = WarehouseVariant.objects.get(id=product_id)
            if quantity > 0:
                LocationProduct.objects.update_or_create(location=location, product=product, defaults={'quantity': quantity})
        serializer = LocationSerializer(location)
        return JsonResponse({'success': True, 'message': 'Location Updated Successfully', 'location': serializer.data}, status=201)
    except Exception as e:
        raise e
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_location'])])
@csrf_exempt
def get_locations_with_products(request):

    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    q_filter = Q(warehouse__company=user.company)
    response_data = get_records('Location', json_data, False,company_filter=q_filter,context={'use_location_with_product_serializer': True})
    return JsonResponse(response_data, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_location'])])
@csrf_exempt
@parser_classes([MultiPartParser, FormParser])
def upload_location_excel(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    warehouse_id = request.data.get('warehouse_id',False)
    if not warehouse_id:
        raise get_error_response('GENERAL_003', {'fields': ['warehouse_id']})
        
    file = request.FILES['file']
    if file.content_type not in [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
        'application/vnd.ms-excel',
        'text/csv'
    ]:
        raise get_error_response('GENERAL_004', {})
    
    locations_to_create = proceed_location_excel(file, user,warehouse_id)
    if not locations_to_create:
        raise get_error_response('GENERAL_005', {'message': _('No valid locations found in the file')})
    
    return JsonResponse({'success': True, 'message': _('All Locations Added successfully')}, status=201)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_locationproduct'])])
@csrf_exempt
@parser_classes([MultiPartParser, FormParser])
def upload_location_excel_with_products(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    warehouse_id = request.data.get('warehouse_id',False)
    if not warehouse_id:
        raise get_error_response('GENERAL_003', {'fields': ['warehouse_id']})
        
    file = request.FILES['file']
    if file.content_type not in [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
        'application/vnd.ms-excel',
        'text/csv'
    ]:
        raise get_error_response('GENERAL_004', {})
    
    locations_to_updates = proceed_location_with_products_excel(file, user,warehouse_id)
    if not locations_to_updates:
        raise get_error_response('GENERAL_005', {'message': _('No valid locations found in the file')})
    
    return JsonResponse({'success': True, 'message': _('All Locations Added successfully')}, status=201)


@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_locationproduct'])])
@csrf_exempt
def change_products_location(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        try:
            user = User.objects.get(user=request.user)
        except User.DoesNotExist:
            raise get_error_response('AUTH_400', {'username': request.user.username})
    
        values = json_data
        new_location_products = change_location_util(values)
        serializer = LocationProductSerializer(new_location_products, many=True)
        return JsonResponse({'success': True, 'message': 'Location Changed Successfully', 'location': serializer.data}, status=201)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_locationproduct'])])
@csrf_exempt
def get_location_products(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    q_filter = Q(location__warehouse__company=user.company)
    response_data = get_records('LocationProduct', json_data, False,company_filter=q_filter)
    return JsonResponse(response_data, status=200)