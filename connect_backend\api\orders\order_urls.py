from django.urls import path
from .order_apis import *

urlpatterns = [
    path('add_order', add_order, name='add_order'),
    path('add_bulk_orders', add_bulk_orders, name='add_bulk_orders'),
    path('get_orders', get_orders, name='get_orders'),
    path('update_order', update_order, name='update_order'),
    path('update_order_status', update_order_status, name='update_order_status'),
    path('get_statuses', get_statuses, name='get_statuses'),
    path('get_order_types', get_order_types, name='get_order_types'),
    path('generate_report', generate_report, name='generate_report'),
    path('get_order_by_sequence', get_order_by_sequence, name='get_order_by_sequence'),
    path('get_order_by_id', get_order_by_id, name='get_order_by_id'),
    path('set_default_vhub_fields', set_default_vhub_fields, name='set_default_vhub_fields'),
    path('get_delivery_statuses', get_delivery_statuses, name='get_delivery_statuses'),
    path('import_orders_from_excel', import_orders_from_excel, name='import_orders_from_excel'),
    path('test_import_excel', test_import_excel, name='test_import_excel'),
    path('create_branch_page', create_branch_page, name='create_branch_page'),
    path('get_branch_pages', get_branch_pages, name='get_branch_pages'),
    path('update_branch_page', update_branch_page, name='update_branch_page'),
    path('get_order_by_sequence_or_mobile', get_order_by_sequence_or_mobile, name='get_order_by_sequence_or_mobile'),
    path('resync_with_delivery_company', resync_with_delivery_company, name='resync_with_delivery_company'),
    path('get_payment_link', get_payment_link, name='get_payment_link'),
    path('sync_delivery_fees', sync_delivery_fees, name='sync_delivery_fees'),
    path('set_order_driver_note', set_order_driver_note, name='set_order_driver_note'),
    path('get_previous_states', get_previous_states, name='get_previous_states'),
    path('get_stuck_reasons', get_stuck_reasons, name='get_stuck_reasons'),
    path('create_stuck_reason', create_stuck_reason, name='create_stuck_reason'),
    path('update_stuck_reason', update_stuck_reason, name='update_stuck_reason'),
    path('get_all_statuses', get_all_statuses, name='get_all_statuses'),
    path('get_order_from_ai', get_order_from_ai, name='get_order_from_ai'),
    path('duplicate_order', duplicate_order, name='duplicate_order'),
    path('resend_order', resend_order, name='resend_order'),
]