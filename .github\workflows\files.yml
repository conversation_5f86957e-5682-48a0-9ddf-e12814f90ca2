name: copying fixtures and commands

on:
  push:
    branches: [ "dev-beta-v2" , 'dev']
    paths:
      - 'connect_backend/connect_backend/fixtures/*'
      - 'connect_backend/connect_backend/connect_app/management/commands/*'
  workflow_dispatch:

jobs:
  process-files:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Collect fixtures
      run: |
        cd connect_backend/connect_backend/fixtures
        ls -p | grep -v / | sed 's/\..*$//' > ../fixtures_list.txt
        cd ../../../..

    - name: Collect commands
      run: |
        cd connect_backend/connect_backend/connect_app/management/commands
        ls -p | grep -v / | sed 's/\..*$//' > ../../../../commands_list.txt
        cd ../../../../../..

    - name: Display collected files
      run: |
        cat connect_backend/connect_backend/fixtures_list.txt
        cat connect_backend/connect_backend/commands_list.txt

    - name: Run commands
      run: |
        # Add your commands here that use the generated files
        echo "Running commands with fixtures and commands files"

    - name: Send to <PERSON>
      run: |
        fixtures=$(cat connect_backend/connect_backend/fixtures_list.txt | tr '\n' ',' | sed 's/,$//')
        commands=$(cat connect_backend/connect_backend/commands_list.txt | tr '\n' ',' | sed 's/,$//')
        curl -X POST "https://jenkins.grefoot.com/generic-webhook-trigger/invoke?token=update_modules_properties_from_git" \
          -H "Content-Type: application/json" \
          -d "{\"fixtures\": \"$fixtures\", \"commands\": \"$commands\"}"
