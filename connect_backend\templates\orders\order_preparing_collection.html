<!DOCTYPE html>

{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_preparing_collection.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}
  <div class="header">
    <div class="business-logo">
        <img src="{{docs[0].company_logo}}" class="business-logo">
        <div class="text-center fw-bold">{{docs[0].company_registry}}</div>
    </div>
    <h2 class="info-item">
        {{docs[0].company_name}}
    </h2>
    <div class="date-time fw-bold">
        {{meta.formatted_now}}
    </div>
</div>

<table>
  <tr>
    <th>{{meta.labels.STORE_SEQUENCE}}</th>
    <th>{{meta.labels.NAME}}</th>
    <th>{{meta.labels.MOBILE_NUMBER}}</th>
    <th>{{meta.labels.ADDRESS}}</th>
    <th>{{meta.labels.CREATOR}}</th>
    <th>{{meta.labels.COD}}</th>
    <th>{{meta.labels.PRODUCTS}}</th>
  </tr>
  {% for doc in docs %}
  <tr>
    <td>
      <div  class="qrcode-container">
        <img class="barcode" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}" alt="">
      </div>
    </td>
    <td>{{doc.order.customer_name}}</td>
    <td>{{doc.order.customer_mobile}}</td>
    <td>
      {{doc.order.area.name}}, 
      {{doc.order.sub_area.name}},
      {{doc.order.address}}
    </td>
    <td>{{doc.order.created_by}}</td>
    <td>{{doc.order.total_cod}}</td>
    <td>
      {% if doc.order_lines and doc.order_lines|length > 0 %}
          {% for item in doc.order_lines %}
          <div>
            {{item.product_variant.variant.name}} || X{{item.quantity}} ||  {{item.price}}
          </div>
          {% endfor %}
      {% else %}
      {{doc.order.product_info.replace('\n', '<br>')}}
      {% endif %}
    </td>
  </tr>
  {% endfor %}
</table>

</body>

</html>