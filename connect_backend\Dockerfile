# Use an official Python runtime as a parent image
FROM python:3.8-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONUNBUFFERED 1

# Set work directory
WORKDIR /code

# Install system dependencies for WeasyPrint and dos2unix
RUN apt-get update && \
    apt-get install -y \
    libpango-1.0-0 \
    cron \
    libpango1.0-dev \
    libgdk-pixbuf2.0-0 \
    libgdk-pixbuf2.0-dev \
    libcairo2 \
    libcairo2-dev \
    libffi-dev \
    build-essential \
    dos2unix && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt /code/
RUN pip install --no-cache-dir -r requirements.txt

# Copy your code (including entrypoint.sh) into /code
COPY . /code/

# Add your cron job file to the container
COPY ./crontab /etc/cron.d/my-cron-job

# Give execution rights on the cron job
RUN chmod 0644 /etc/cron.d/my-cron-job

# Create a log file to store cron logs
RUN touch /var/log/cron.log

# Convert line endings and make entrypoint executable
RUN dos2unix entrypoint.sh && chmod +x entrypoint.sh

# Start Django directly
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
