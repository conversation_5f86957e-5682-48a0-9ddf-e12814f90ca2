from django.core.management.base import BaseCommand
from django.db import transaction
from api.category.category_utils import create_default_category
from api.users.user_model import CompanyConf
from api.category.category_model import Category

class Command(BaseCommand):
    help = "Create(if not exist) and set default category in company business configuration"

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.NOTICE("Starting company config update..."))

        with transaction.atomic():
          updated_count = 0
          company_configurations = CompanyConf.objects.all()
          for company_config in company_configurations:
            company = company_config.company
            # Check if default category exists
            default_category = Category.objects.filter(company=company).first()
            if not default_category:
                # Create default category if it does not exist
                default_category = create_default_category(company)
                self.stdout.write(self.style.SUCCESS(f"Created default category for company: {company.company_name}"))
            else:
                self.stdout.write(self.style.SUCCESS(f"Default category already exists for company: {company.company_name}"))
            # Set default category in company configuration
            company_config.default_category = default_category
            company_config.save()
            updated_count += 1
          self.stdout.write(self.style.SUCCESS(f"Updated {updated_count} companies."))

        self.stdout.write(self.style.SUCCESS("companies update completed."))
