from ..util_functions import *
from .collection_model import *
from django.db import transaction
from datetime import date
from django.conf import settings

def validate_collection(values, required_fields):
    validation_errors = []
    foreign_key_objects = {}

    for field in required_fields:
        if not values.get(field):
            validation_errors.append(f'Missing field: {field}')

    foreign_key_objects.update(validage_collection_foreign_keys(values, validation_errors))

    return validation_errors, foreign_key_objects

def validage_collection_foreign_keys(values, validation_errors):
    foreign_key_objects = {}

    if 'collection_status' in values:
        foreign_key_objects['collection_status'] = get_collection_foreign_key_object(
            values, 'collection_status', CollectionStatus, validation_errors
        )

    if 'collection_type' in values:
        foreign_key_objects['collection_type'] = get_collection_foreign_key_object(
            values, 'collection_type', CollectionType, validation_errors
        )

    if 'collection_bank' in values:
        foreign_key_objects['collection_bank'] = get_collection_foreign_key_object(
            values, 'collection_bank', CollectionBank, validation_errors
        )

    return foreign_key_objects

def get_collection_foreign_key_object(values, field_name, model_class, validation_errors):
    if field_name not in values:
        validation_errors.append(f'missing {field_name}')
        return None

    field_value = values[field_name]
    
    if isinstance(field_value, int):
        try:
            return model_class.objects.get(id=field_value)
        except model_class.DoesNotExist:
            validation_errors.append(f'Invalid {field_name} id: {field_value}')
            return None
    
    elif isinstance(field_value, str):
        try:
            return model_class.objects.get(code=field_value)
        except model_class.DoesNotExist:
            validation_errors.append(f'Invalid {field_name} code: {field_value}')
            return None
    
    elif isinstance(field_value, dict):
        field_id = field_value.get('id')
        if field_id:
            try:
                return model_class.objects.get(id=field_id)
            except model_class.DoesNotExist:
                validation_errors.append(f'Invalid {field_name} id: {field_id}')
                return None
        else:
            validation_errors.append(f'Missing {field_name} id')
            return None
    
    else:
        validation_errors.append(f'Invalid {field_name} type')
        return None

def prepare_collection_values(json_data, foreign_key_objects, user):
    json_data['company'] = user.company
    json_data['created_by'] = user.name
    json_data['updated_by'] = user.name
    json_data['created_at'] = timezone.now()
    return build_collection_values(json_data, foreign_key_objects)

def build_collection_values(values, foreign_keys_objects):
    return {**values, **foreign_keys_objects}

def process_collection(collection, values):
    if not collection:
        collection = create_collection(values)
        if not collection:
            raise Exception('Failed to create collection')
    else:
        values['id'] = collection.id
        collection = update_collection(values)
    return collection

from django.db.models import Q

def parse_filters(filter_groups):
    q_filters = Q()
    for filter_group in filter_groups:
        group_q = Q()
        for key, group_filters in filter_group.items():
            for filter_item in group_filters:
                field = filter_item.get('field')
                operator = filter_item.get('operator', 'exact')
                value = filter_item.get('value')

                if field and value is not None:
                    if operator in ['gt', 'lt', 'gte', 'lte'] and isinstance(value, str):
                        date_value = parse_date(value)
                        if date_value is not None:
                            value = datetime.datetime.combine(date_value, datetime.datetime.min.time())
                        else:
                            continue

                    if operator == 'isnull' and isinstance(value, bool):
                        value = True if value else False

                    if isinstance(value, datetime.datetime) and value.tzinfo is None:
                        value = timezone.make_aware(value, timezone.get_current_timezone())
                        
                    if operator == 'neq':
                        group_q &= ~Q(**{field: value})
                    elif operator == 'eq':
                        group_q &= Q(**{field: value})
                    else:
                        lookup = f"{field}__{operator}"
                        group_q &= Q(**{lookup: value})
                    
            q_filters |= group_q
    return q_filters

def update_orders_with_collection(filters, exclude, collection, company):
    q_filters = parse_filters(filters)
    q_exclude = parse_filters(exclude)
    
    orders = Order.objects.filter(company=company).filter(q_filters).exclude(q_exclude)
    
    for order in orders:
        order.collection = collection
        order.save()

def create_collection(values):
    try:
        with transaction.atomic():
            collection = Collection.objects.create(**values)
            log = create_log('Collection', values['company'], 'create', values['created_by'], collection.id)
            fields = get_tracked_fields(values['company'], 'Collection')
            log_tracked_fields(log, values, fields)
            return collection
    except Exception as e:
        raise Exception(f'Error creating collection: {str(e)}')
    
def fetch_conversion_rates():
    today = date.today()
    try:
        conversion_rate = ConversionRate.objects.filter(date=today)
        if conversion_rate:
            return conversion_rate
    except ConversionRate.DoesNotExist:
        pass


    currencies = ['JOD', 'ILS']

    for currency in currencies:
        API_URL = settings.CONVERT_RATE_API_URL + '/' + currency

        try:
            response = requests.get(API_URL)
            data = response.json()
            
            if data['result'] != 'success':
                raise Exception("Failed to retrieve conversion rates")
            
            base_currency = data['base_code']
            conversion_rates = data['conversion_rates']

            required_rates = {currency: rate for currency, rate in conversion_rates.items() if currency in currencies}

            conversion_rate_objs = []
            for currency, rate in required_rates.items():
                if currency != base_currency:
                    conversion_rate_objs.append(ConversionRate(
                        currency_from=base_currency,
                        currency_to=currency,
                        rate = rate - 0.04
                    ))

            with transaction.atomic():
                rates = ConversionRate.objects.bulk_create(conversion_rate_objs)
                return rates

        except requests.RequestException as e:
            raise Exception(f"API request failed: {e}")
        except Exception as e:
            raise Exception(f"Error saving conversion rates: {e}")
    
def update_collection(values):
    try:
        with transaction.atomic():
            collection = Collection.objects.filter(id=values['id'], company=values['company']).last()
            if not collection:
                raise Exception("Collection not found")
            log = create_log('Collection', values['company'], 'update', values['updated_by'], collection.id)
            update_collection_fields(collection, values, log)
            collection.save()
            return collection
    except Exception as e:
        raise Exception(f"Error updating collection: {str(e)}")
    
def update_collection_fields(collection, values, log):
    fields = get_tracked_fields(collection.company, 'Collection')

    for key, value in values.items():
        old_value = getattr(collection, key, None)
        if old_value != value:
            log_and_update_field(collection, key, value, old_value, log, bool(key in fields))

def set_default_collection_fields(data, fields, defaults):
    for field in fields:
        if not data.get(field):
            data[field] = defaults.get(field)
    return data

def get_existing_collection(status_code):
    return Collection.objects.filter(collection_status__code=status_code).last()