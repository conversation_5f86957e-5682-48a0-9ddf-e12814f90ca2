
from django.utils.translation import gettext as _
from django.db import transaction
from .developer_connection_model import *
from ..logs.logs_model import *
from ..util_functions import *

def validate_developer_connection(order, required_fields):
    validation_errors = []

    for field in required_fields:
        value = order.get(field, None)
        if value == None:
            validation_errors.append(_('Missing field: ') + field)

    return validation_errors

def build_developer_connection_values(json_data, company, name):
    values = {**json_data, 'company': company}
    values['created_by'] = values['updated_by'] = name
    return values

def create_developer_connection(values):
    try:
        developer_connection = DeveloperConnection.objects.create(**values)
        log = create_log('DeveloperConnection', values['company'], 'create', values['created_by'], developer_connection.id)
        fields = get_tracked_fields(values['company'], 'DeveloperConnection')
        log_tracked_fields(log, values, fields)
        delete_log_if_no_changes(log)
        developer_connection.save()
    except ConnectError as e:
        raise e
    except Exception as e:
        raise ValidationError(f"Error creating developer connection: {str(e)}")
    return developer_connection

def create_developer_connection_token(company, developer_connection, username):
    # Create and return the DeveloperConnectionToken instance
    return DeveloperConnectionToken.objects.create(
        company=company,
        created_at=timezone.now(),
        developer_connection=developer_connection,
        expires_at=timezone.now() + timedelta(hours=720),
        created_by=username,
        updated_by=username
    )

def encode_developer_token(developer_connection_token, developer_connection):
    payload = {
        'company': developer_connection_token.company.company_id,
        'developer_connection': developer_connection,
        'created_at': developer_connection_token.created_at.isoformat(),
        'expires_at': developer_connection_token.expires_at.isoformat(),
        'token_id': developer_connection_token.id,
        'channel': 'developer_integration'
    }
    secret_key = settings.SECRET_KEY
    return jwt.encode(payload, secret_key, algorithm='HS256')
