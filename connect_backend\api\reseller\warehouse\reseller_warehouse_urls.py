from django.urls import path
from .reseller_warehouse_api import *

urlpatterns = [
    path('get_reseller_warehouses', get_reseller_warehouses, name='get_reseller_warehouses'),
    path('add_reseller_warehouse', add_reseller_warehouse, name='add_reseller_warehouse'),
    path('update_reseller_warehouse', update_reseller_warehouse, name='update_reseller_warehouse'),
    path('get_reseller_warehouse_connections', get_reseller_warehouse_connections, name='get_reseller_warehouse_connections'),
    path('accept_warehouse_connection', accept_warehouse_connection, name='accept_warehouse_connection'),
    path('reject_warehouse_connection', reject_warehouse_connection, name='reject_warehouse_connection'),
    path('set_business_available_warehouses', set_business_available_warehouses, name='set_business_available_warehouses'),
    path('get_all_resellers_warehouses', get_all_resellers_warehouses, name='get_all_resellers_warehouses'),
    path('get_reseller_warehouse_variants', get_reseller_warehouse_variants, name='get_reseller_warehouse_variants'),
    path('get_company_assigned_warehouses', get_company_assigned_warehouses, name='get_company_assigned_warehouses'),
    path('get_reseller_stock_approval', get_reseller_stock_approval, name='get_reseller_stock_approval'),
    path('update_reseller_stock_approval', update_reseller_stock_approval, name='update_reseller_stock_approval'),
    path('add_reseller_stocktaking', add_reseller_stocktaking, name='add_stocktaking'),
    path('get_reseller_stocktaking', get_reseller_stocktaking, name='get_stocktaking'),
    path('get_reseller_locations', get_reseller_locations, name='get_reseller_locations'),
    path('create_reseller_location', create_reseller_location, name='create_reseller_location'),
    path('print_reseller_location', print_reseller_location, name='print_reseller_location'),
    path('add_reseller_location_for_products', add_reseller_location_for_products, name='add_reseller_location_for_products'),
    path('get_reseller_locations_with_products', get_reseller_locations_with_products, name='get_reseller_locations_with_products'),
    path('reseller_upload_location_excel', reseller_upload_location_excel, name='reseller_upload_location_excel'),
    path('reseller_upload_location_excel_with_products', reseller_upload_location_excel_with_products, name='reseller_upload_location_excel_with_products'),
    path('change_products_location', change_products_location, name='change_products_location'),
    path('get_reseller_location_products', get_reseller_location_products, name='get_reseller_location_products'),
]