import requests
import base64
import uuid
from django.core.management.base import BaseCommand
from django.core.files.base import ContentFile

from api.delivery_company.delivery_company_model import DeliveryCompany
from api.lookups.lookup_model import Country

class Command(BaseCommand):
    help = 'Fetches companies from the API and saves them to the database'

    def handle(self, *args, **kwargs):
        url = 'https://master.olivery.app/master/companies/get_companies/delivery'
        json_body = {
            "jsonrpc": "2.0"
        }
        response = requests.get(url, json=json_body)
        data = response.json()

        if data.get('result', {}).get('status') == 'success':
            companies = data['result']['data']
            for company in companies:
                img_data = None
                if 'logo' in company:
                    try:
                        img_data = ContentFile(base64.b64decode(company['logo']), name=f'{uuid.uuid4()}.png')
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"Failed to decode logo for company {company['name']}: {e}"))
                        continue

                if 'country' in company:
                    try:
                        country = Country.objects.get(name=company['country'])
                    except Country.DoesNotExist:
                        self.stdout.write(self.style.ERROR(f"Country '{company['country']}' not found"))
                        continue
                
                # Update or create the DeliveryCompany object
                DeliveryCompany.objects.update_or_create(
                    name=company['name'],
                    defaults={
                        'company_image': img_data if img_data else None,
                        'delivery_company_url': company['website_url'],
                        'delivery_company_db': company['website_db'],
                    }
                )
            self.stdout.write(self.style.SUCCESS('Successfully imported companies'))
        else:
            self.stdout.write(self.style.ERROR('Failed to fetch companies from the API'))