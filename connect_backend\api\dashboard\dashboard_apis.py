from django.views.decorators.csrf import csrf_exempt
from ..util_functions import *
from django.http import JsonResponse
from .dashboard_utils import *
from .dashboard_model import *
from rest_framework.decorators import *
from ..permissions import *
from ..error.error_model import *
from rest_framework.permissions import AllowAny

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_dashboard'])])
@csrf_exempt
def get_dashboard(request):
    json_data = parse_request_body(request)
    if not json_data and json_data != {}:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    start_date_str = json_data.get('start_date')
    end_date_str = json_data.get('end_date')
    show_money = False
    show_profit = False
    created_by_user = None
    custom_filters=Q()
    date_filter = get_date_filter(start_date_str, end_date_str)

    connection = json_data.get('connection', None)
    delivery_company_filter = get_delivery_company_filter(connection)

    statuses = get_dashboard_statuses()
    if isinstance(statuses, JsonResponse):
        return statuses
    
    if user_has_group_permission(request.user, 'view_financial_dashboard'):
        show_money = True

    if user_has_group_permission(request.user, 'view_self_dashboard'):
        created_by_user = user

    if user_has_group_permission(request.user, 'view_profit_in_order'):
        show_profit = True

    if user_has_group_permission(request.user, 'view_driver_orders'):
        custom_filters = Q(driver=user, status__code='with_driver')
        sum_field = 'total_cod'

    driver_id = json_data.get('driver', None)
    if driver_id:
        driver = User.objects.get(id=driver_id,company=user.company)
        custom_filters &=  Q(driver=driver)

    exclude_statuses_codes=['completed', 'completed_returned', 'deleted', 'cancelled', 'money_received', 'returned']

    orders_in_stores = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status=True,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        custom_filters=custom_filters
    )

    orders_preparing = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        status=statuses['preparing'],
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code='preparing',
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=None,
        custom_filters=custom_filters
    )

    orders_ready_for_delivery = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        status=statuses['ready_for_delivery'],
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code='ready_for_delivery',
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=None,
        custom_filters=custom_filters
    )

    orders_expected_to_return = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        status=[statuses['expected_return_package']],
        delivery_company_status=None,
        start_date=start_date_str,
        end_date=end_date_str,
        order_type=['replacement', 'retrieval', 'partial_delivery'],
        connection_id=connection,
        driver_id=driver_id,
        status_code=['expected_return_package'],
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        custom_filters=custom_filters,
        add_or_statement_return=True
    )

    orders_able_for_collection = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        delivery_company_status=['Delivered', 'Money In', 'Money Out', 'Money Received', 'Completed'],
        start_date=start_date_str,
        end_date=end_date_str,
        status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code=None,
        collection_status=['pending', 'confirmed', 'completed', 'paid'],
        show_money = show_money,
        created_by=created_by_user,
        exclude_statuses_codes=exclude_statuses_codes,
        delivery_date=True,
        add_or_statement=True,
        is_paid=False,
        show_profit=show_profit,
        custom_filters=custom_filters
    )

    orders_delivered = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        delivery_company_status=['Delivered', 'Money In', 'Money Out', 'Money Received', 'Completed'],
        start_date=start_date_str,
        end_date=end_date_str,
        status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code=None,
        collection_status=None,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        delivery_date=True,
        custom_filters=custom_filters
    )

    orders_with_delivery_company = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status=False,
        status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code=None,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        custom_filters=custom_filters
    )

    orders_in_progress = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status='In Progress',
        status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code=None,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        custom_filters=custom_filters
    )

    orders_cancelled = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status='Cancelled',
        status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code=None,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        custom_filters=custom_filters
    )

    orders_picked_up = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status=['In Branch', 'Waiting', 'Picked up', 'Picking up'],
        status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code=None,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        custom_filters=custom_filters
    )

    orders_returned = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status=['Branch Returned', 'Rejected', 'Stuck'],
        status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code=None,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        return_date=True,
        custom_filters=custom_filters
    )

    orders_scheduled = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status='Reschedule',
        status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code=None,
        show_money = show_money,
        show_profit=show_profit,
        created_by=created_by_user,
        exclude_statuses_codes=exclude_statuses_codes,
        custom_filters=custom_filters
    )

    conf = get_company_conf(user.company)

    orders_delayed = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status=list(conf.delayed_order_statuses.all().values_list('delivery_company_status', flat=True)),
        status=[statuses['with_delivery_company'], statuses['with_driver']],
        connection_id=connection,
        driver_id=driver_id,
        status_code=['with_delivery_company', 'with_driver'],
        delayed_time= conf.days_delayed,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        custom_filters=custom_filters
    )

    orders_mismatched = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status=None,
        status=None,
        connection_id=connection,
        driver_id=driver_id,
        status_code=None,
        delayed_time=None,
        area_mismatch=True,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=exclude_statuses_codes,
        custom_filters=custom_filters
    )

    orders_waiting = get_order_summary(
        company=user.company,
        date_filter=date_filter,
        delivery_company_filter=delivery_company_filter,
        start_date=start_date_str,
        end_date=end_date_str,
        delivery_company_status=None,
        status=statuses['new_order'],
        connection_id=connection,
        driver_id=driver_id,
        status_code='new_order',
        delayed_time=None,
        show_money = show_money,
        created_by=created_by_user,
        show_profit=show_profit,
        exclude_statuses_codes=None,
        custom_filters=custom_filters
    )
    
    context = {
        'orders_in_stores': orders_in_stores,
        'orders_preparing': orders_preparing,
        'orders_ready_for_delivery': orders_ready_for_delivery,
        'orders_with_delivery_company': orders_with_delivery_company,
        'orders_expected_to_return': orders_expected_to_return,
        'orders_in_progress': orders_in_progress,
        'orders_scheduled': orders_scheduled,
        'orders_able_for_collection': orders_able_for_collection,
        'orders_delivered': orders_delivered,
        'orders_returned': orders_returned,
        'orders_delayed': orders_delayed,
        'orders_mismatched': orders_mismatched,
        'orders_waiting': orders_waiting,
        'orders_picked_up': orders_picked_up,
        'orders_cancelled': orders_cancelled,
    }

    return JsonResponse({'success': True, 'context': context}, status=200)

@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_dashboard'])])
@csrf_exempt
def get_expected_close_orders(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    # Get the start of today in current timezone
    today = timezone.now().astimezone().replace(hour=0, minute=0, second=0, microsecond=0)

    # Cutoff = 90 days ago, end of that day
    cutoff_date = today - timedelta(days=60) + timedelta(days=1)
    filter_items = [
        {"field": "store_status_change_date", "operator": "lte", "value": cutoff_date},
        {"field": "status__code", "operator": "in", "value": ["returned", "money_received"]},
    ]
    response = {
        'success': True,
        'context': {
            'count': Order.objects.filter(
                        company=user.company,
                        store_status_change_date__lte=cutoff_date, 
                        status__code__in=["returned", "money_received"]
                    ).count(),
            'filters': [{"operator": "and", "filters": filter_items}]
        }
    }
    return JsonResponse(response, status=200)
    
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_true_buyer(request):
    mobile_number = request.GET.get('mobile_number')
    if not mobile_number:
        raise get_error_response('GENERAL_001', {})
    
    url = "https://truebuyer.olivery.io/true_buyer"
    headers = {
        "X-API-Key": "ojdsfbndosuabgfadosbgoiade9r@#@^%T"
    }
    params = {
        "phone_number": mobile_number
    }

    response = requests.get(url, headers=headers, params=params)
    result = response.json()
    return JsonResponse(result, status=200)