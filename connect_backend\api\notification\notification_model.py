from django.db import models
from ..users.user_model import User
class NotificationSound(models.Model):
    name = models.Char<PERSON>ield(max_length=200)
    channel = models.CharField(max_length=200, null=True)

class Notification(models.Model):
    sound = models.ForeignKey(NotificationSound, on_delete=models.SET_NULL, null=True)
    delivery_status = models.CharField(max_length=200, null=True, blank=True)
    source = models.CharField(max_length=200, null=True)

class NotificationBanner(models.Model):
    title_en = models.CharField(max_length=200)
    title_ar = models.CharField(max_length=200)
    message_en = models.Char<PERSON>ield(max_length=500, null=True)
    message_ar = models.Char<PERSON>ield(max_length=500, null=True)
    start_date = models.DateTimeField(null=True, blank=True)
    end_date = models.DateTimeField(null=True, blank=True)
    active = models.BooleanField(null=True, blank=True, default=False)

class NotificationCenter(models.Model):
    message = models.Char<PERSON>ield(max_length=500, null=True)
    source = models.Char<PERSON>ield(max_length=200, null=True)
    users = models.ManyToManyField(User, related_name='notifications')
    orders_sequences = models.JSONField(default=list, blank=True, null=True)
    seen = models.BooleanField(default=False)
    context = models.JSONField(default=dict, blank=True, null=True)