from django.core.management.base import BaseCommand
from api.orders.order_model import Order
from api.connection.connection_utils import sync_dynamic_order_status, handle_status_change_notifications
import logging
from django.utils import timezone

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Update statuses for dynamic integrations companies'

    def handle(self, *args, **kwargs):
        orders = Order.objects.filter(
            connection__delivery_company__is_olivery_company=False, 
            olivery_sequence__isnull=False
        ).exclude(status__code__in=['completed', 'completed_returned', 'cancelled'])

        orders_for_notifications = {}
        logger.info(f"================================================================")
        logger.info(f"Start of Update Status Cron job {timezone.now()}")
        for order in orders:
            notification_details = []
            try:
                sync_dynamic_order_status(order, notification_details)
                if notification_details:
                    delivery_company = order.connection.delivery_company
                    company = order.company
                    if delivery_company not in orders_for_notifications:
                        orders_for_notifications[delivery_company] = {}
                    if company.company_id not in orders_for_notifications[delivery_company]:
                        orders_for_notifications[delivery_company][company.company_id] = []
                    orders_for_notifications[delivery_company][company.company_id].extend(notification_details)
            except Exception as e:
                logger.error(f"Error updating order {order.id}: {str(e)}")
        try:
            logger.info("Sending notifications...")
            for delivery_company, companies in orders_for_notifications.items():
                for company_id, notifications in companies.items():
                    handle_status_change_notifications(notifications, delivery_company)
        except Exception as e:
            logger.error(f"Error handling notifications: {str(e)}")
        logger.info(f"End of Update Status Cron job {timezone.now()}")
        logger.info(f"================================================================")
