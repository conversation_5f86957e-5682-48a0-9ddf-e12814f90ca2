from django.urls import path,include
from .mystore_apis import *



urlpatterns = [

    path('add_mystore' , add_mystore, name='add_mystore'),
    path('update_mystore' , update_mystore, name='update_mystore'),
    path('get_mystore', get_mystore, name='get_mystore'),
    path('delete_mystore',delete_mystore,name='delete_mystore'),
    path('add_banner_detail',add_banner_detail,name='add_banner_detail'),
    path('get_banners', get_banners , name='get_banner_details'),
    path('get_banner_auth',get_banner_auth,name='get_banner_setting'),
    path('delete_banners',delete_mystore_banner, name='delete_banners'),
    path('update_banner_detail',update_banner_detail,name='update_banner_detail'),
    path('add_mystore_order_auth', add_mystore_order_auth, name='add_mystore_order_auth'),
    path('add_mystore_order', add_mystore_order,name='add_mystore_order'),
    path('get_mystore_public', get_mystore_public, name='get_mystore_public'),
    path('get_orders', get_store_orders, name='get_store_orders'),
    path('add_store_section', add_store_section, name='add_store_section'),
    path('get_store_section_auth', get_store_section_auth, name='get_store_section_auth'),
    path('get_store_section_public', get_store_section_public, name='get_store_section_public'),
    path('update_store_section', update_store_section, name='update_store_section'),
    path('get_store_delivery_fee', get_store_delivery_fee, name='get_store_delivery_fee'),
    path('',include('api.mystore.users.user_urls')),
    
]