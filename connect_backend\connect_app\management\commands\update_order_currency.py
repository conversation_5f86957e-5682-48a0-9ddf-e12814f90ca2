from django.core.management.base import BaseCommand
from api.orders.order_model import Order
from api.client.client_model import Client

class Command(BaseCommand):
    help = 'Update currency for orders based on customer mobile number or company currency'

    def handle(self, *args, **kwargs):
        orders = Order.objects.all() # Only update orders where the currency is not set

        for order in orders:
            order.currency = order.country.currency
            order.save()

            self.stdout.write(self.style.SUCCESS(f"Updated order {order.id} with currency {order.currency}"))
