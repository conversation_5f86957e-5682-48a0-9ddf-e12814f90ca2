from django.db import models
from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON>ser, BaseUserManager
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
import jwt
from django.conf import settings
from django.utils.translation import gettext as _
from ..lookups.lookup_model import Country, Area
from ..users.user_model import Company
from ..delivery_company.delivery_company_model import DeliveryCompany
import os
import binascii
from ..warehouse.warehouse_model import Warehouse

class CustomUserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError("The Email field must be set")
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

class Reseller(AbstractBaseUser):
    mobile_number = models.CharField(max_length=200, unique=True)
    is_active = models.BooleanField(default=True)
    country_code = models.Char<PERSON>ield(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_delivery_company = models.BooleanField(default=False)
    name = models.CharField(max_length=200)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True)
    national_id_url = models.CharField(max_length=200, null=True, blank=True)
    certificate_url = models.CharField(max_length=200, null=True, blank=True)
    company_logo_url = models.CharField(max_length=200, null=True, blank=True)
    area = models.ForeignKey(Area, on_delete=models.SET_NULL, null=True, blank=True)
    address = models.CharField(max_length=200, null=True, blank=True)
    api_documentation = models.CharField(max_length=500, null=True, blank=True)
    waiting_confirmation = models.BooleanField(default=True)
    limit = models.IntegerField(default=50)
    is_approved = models.BooleanField(default=False)
    update_pending = models.BooleanField(default=False)
    last_pickup_time = models.TimeField(null=True, blank=True)
    delivery_daily = models.BooleanField(default=False)
    delivery_company = models.ForeignKey(DeliveryCompany, on_delete=models.SET_NULL, null=True, blank=True)
    is_aggregator = models.BooleanField(default=False)
    company_registry = models.CharField(max_length=500, null=True, blank=True)

    objects = CustomUserManager()

    USERNAME_FIELD = 'mobile_number'
    REQUIRED_FIELDS = []

    def __str__(self):
        return self.mobile_number
    
class ResellerPricelistItem(models.Model):
    reseller = models.ForeignKey(Reseller, related_name='pricelist_items', on_delete=models.CASCADE)
    from_area = models.CharField(max_length=255, blank=True)
    to_area = models.CharField(max_length=255, blank=True)
    price = models.FloatField(max_length=20, null=True)

class DeliveryCompanyBranch(models.Model):
    reseller = models.ForeignKey(Reseller, related_name='branches', on_delete=models.CASCADE)
    name = models.CharField(max_length=200)

class ResellerWallet(models.Model):
    reseller = models.OneToOneField(Reseller, related_name='wallet', on_delete=models.CASCADE, null=True)
    credit = models.FloatField(max_length=20, null=True, default=0)
    debit = models.FloatField(max_length=20, null=True, default=0)

class ResellerUpdate(models.Model):
    reseller = models.ForeignKey(Reseller, related_name='updates', on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    status = models.CharField(max_length=200, blank=True, null=True, choices=[('pending', _('Pending')),('rejected', _('Rejected')), ('accepted', _('Accepted'))])

class ResellerUpdateField(models.Model):
    update = models.ForeignKey(ResellerUpdate, related_name='fields', on_delete=models.CASCADE)
    field_name = models.CharField(max_length=200)
    old_value = models.TextField(null=True, blank=True)
    new_value = models.TextField(null=True, blank=True)
    new_value_id = models.CharField(max_length=20, null=True, blank=True)

class ResellerPricelistItemUpdate(models.Model):
    update = models.ForeignKey(ResellerUpdate, related_name='pricelist_items_updates', on_delete=models.CASCADE)
    from_area = models.CharField(max_length=255, blank=True)
    to_area = models.CharField(max_length=255, blank=True)
    country = models.ForeignKey(Country, null=True, on_delete=models.SET_NULL)
    price = models.FloatField(max_length=20, null=True)

class DeliveryCompanyBranchUpdate(models.Model):
    update = models.ForeignKey(ResellerUpdate, related_name='branch_updates', on_delete=models.CASCADE)
    name = models.CharField(max_length=200)

class ResellerCompany(models.Model):
    reseller = models.ForeignKey(Reseller, related_name='companies', on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True)
    mobile_number = models.CharField(max_length=200, unique=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    status = models.CharField(max_length=200, null=True, blank=True)
    status_code = models.CharField(max_length=200, null=True, blank=True)
    default_delivery_company = models.ForeignKey(DeliveryCompany, on_delete=models.CASCADE, null=True, blank=True)
    default_warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE, null=True, blank=True)
    auto_send_orders = models.BooleanField(default=False)

# This model is for handle default available delivery companies
class ResellerCompanyDeliveryCompany(models.Model):
    reseller_company = models.ForeignKey(ResellerCompany, on_delete=models.CASCADE)
    delivery_company = models.ForeignKey(DeliveryCompany, on_delete=models.CASCADE)

# This model is for handle default available warehouses
class ResellerCompanyWarehouse (models.Model):
    reseller_company = models.ForeignKey(ResellerCompany, on_delete=models.CASCADE)
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE)

class ResellerToken(models.Model):
    reseller = models.ForeignKey(Reseller, related_name='tokens', on_delete=models.CASCADE)
    token = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True)
    is_valid = models.BooleanField(default=True)
    reseller_company = models.ForeignKey(ResellerCompany, related_name='token', on_delete=models.SET_NULL, null=True)

    def save(self, *args, **kwargs):
        self.token = ''.join(binascii.hexlify(os.urandom(20)).decode())
        super().save(*args, **kwargs)

class CompanyDeliveryCompany(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    delivery_company = models.ForeignKey(DeliveryCompany, on_delete=models.CASCADE)

class ResellerJWTAuthentication(BaseAuthentication):
    def authenticate(self, request):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Token '):
            raise AuthenticationFailed('Authorization header must start with "Token "')

        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            raise AuthenticationFailed('Token has expired')
        except jwt.InvalidTokenError:
            raise AuthenticationFailed('Invalid token')
        
        if payload.get('connect_user_id'):
            raise AuthenticationFailed('Not Reseller')

        try:
            user = Reseller.objects.get(id=payload['user_id'])
        except Reseller.DoesNotExist:
            raise AuthenticationFailed('User not found')

        return (user, None)

class CompanyWarehouseConnection (models.Model):
    company = models.ForeignKey('Company', related_name='company_connection', on_delete=models.CASCADE)
    warehouse = models.ForeignKey('Warehouse', related_name='warehouse_connection', on_delete=models.CASCADE)
    connection_status = models.CharField(max_length=100,
                                         choices=[('not_connected',_('Not Connected')),
                                                  ('connected',_('Connected')),
                                                  ('pending',_('Pending')),
                                                  ('rejected',_('Rejected')),
                                                  ('cancelled',_('Cancelled')),
                                                  ],
                                                  default="not_connected")
    
# determine what is the warehouses available for a Business
class CompanyResellerWarehouse (models.Model):
    company = models.ForeignKey('Company', related_name='company_reseller_warehouses', on_delete=models.CASCADE)
    warehouse = models.ForeignKey('Warehouse', related_name='reseller_warehouses_company', on_delete=models.CASCADE)


class ResellerUserPreferences(models.Model):
    user = models.ForeignKey(Reseller, on_delete=models.CASCADE, null=True)
    order_tree_view = models.TextField(null=True, blank=True, default='_item_sequence,order_sequence,customer_name,order_type,status,delivery_company,delivery_company_status,olivery_sequence,area,total_cod,created_by,channel,note,product_info,package_cost,delivery_fee,order_due_amount,amount_received')

    def get_order_tree_view(self):
        order_tree_view = self.order_tree_view.split(',')
        return order_tree_view

    def set_order_tree_view(self, field_list):
        self.order_tree_view = ','.join(field_list)
