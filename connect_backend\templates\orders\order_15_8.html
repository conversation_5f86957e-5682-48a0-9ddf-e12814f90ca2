<!DOCTYPE html>
{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_15_8.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}

    {% for doc in docs %}
    <div class="waybill-container page-break">
        <div class="card-container">
            <div class="header">
                <div class="business-logo">
                    <img src="{{doc.company_logo}}" class="business-logo">
                    {% if doc.company_registry %}
                    <div class="text-center fw-bold">{{doc.company_registry}}</div>
                    {% endif %}
                </div>
            </div>
            <div class="header">
                <div class="header-barcode-container">
                    <img class="barcode" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}"
                        alt="">
                </div>
            </div>
            <div class="div-border">
                {{meta.labels.DATE}}: 
                <span>
                {{meta.formatted_now_date}}
                </span>
            </div>
            <div class="div-border">
                {{meta.labels.RECIPIENT}}: 
                <span>
                    {{doc.order.customer_name}}
                </span>
            </div>
            <div class="div-border">
                {{meta.labels.ADDRESS}}: 
                <span>
                    {{doc.order.address}}
                </span>
            </div>
            <div class="div-border">
                {{meta.labels.MOBILE_NUMBER}}: 
                <span>
                    {{doc.order.customer_mobile}}
                </span>
            </div>
            <div class="div-border">
                {{meta.labels.PRODUCTS}}: 
                {% if doc.order_lines and doc.order_lines|length > 0 %}
                    {% for item in doc.order_lines %}
                    <div>
                        {{item.product_variant.variant.name}} || X{{item.quantity}}
                    </div>
                    {% endfor %}
                {% endif %}
            </div>
            <div class="div-border">
                {{meta.labels.PRODUCT_NOTES}}: 
                <span>
                    {{doc.order.product_info.replace('\n', '<br>')}}
                </span>
            </div>
            <div class="div-border">
                {{meta.labels.ORDER_TYPE}}: 
                <span>
                    {{doc.order.order_type.name}}
                </span>
            </div>
            <div class="div-border">
                {{meta.labels.IS_PAID}}: 
                <span>
                    {{doc.order.paid}}
                </span>
            </div>
            <div class="div-border">
                {{meta.labels.COD}}: 
                <span>
                    {{doc.order.total_cod}}
                </span>
            </div>
            <div class="div-border">
                {{meta.labels.NOTES}}: 
                <span class="notes-content overflow-hidden" >
                    {{doc.order.note[:100] if doc.order.note is not none else ""}}
                </span>
            </div>
            <div class="labels">
                {{meta.labels.DELIVERY_SEQUENCE}}
            </div>
            <div class="header">
                <div class="barcode-delivery-container">
                    <img class="barcode" src="data:image/png;base64,{{ doc.barcode_olivery_base64 }}"
                        alt="">
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</body>

</html>