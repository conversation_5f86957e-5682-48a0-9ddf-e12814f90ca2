@page {
    size: 105mm 148mm;
    margin: 0;
}

.d-flex {
    display: flex;
}
.align-items-center {
    align-items: center;
}
.text-center {
    text-align: center;
}

.padding-10 {
    padding: 10px;
}
.flex-1-3 {
    flex: 0 0 33.333%;
}
.flex-2-3 {
    flex: 0 0 66.667%;
}
.barcode-img {
    width: 200px;
    height: 35px;
    vertical-align: middle;
    margin-top: -3px;
}
.table-wrapper {
    width: fit-content;
    margin: 0 auto;
    padding: 0 10px 0 20px;
}
.order-table {
    border: 1px solid black;
    border-collapse: collapse;
    width: 350px;
}
.order-table td {
    text-align: start;
    padding: 8px 1rem;
    font-size: 16px;
    border: 1px solid black;
}


.delivery-logo {
    max-width: 100px;
    max-height: 50px;
    width: auto;
    height: auto;
    object-fit: contain;
}

.delivery-container {
    display: flex;
    align-items: center;
    padding: 10px;
}

.delivery-barcode-container {
    text-align: center;
    flex: 2;
}

.delivery-logo-container {
    flex: 1;
}

.company_name{
    line-height:1;
    font-size: 2em;
}