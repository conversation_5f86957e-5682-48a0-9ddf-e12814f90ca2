from django.core.management.base import BaseCommand
from django.db import transaction
from api.orders.order_model import Order, Status
from django.utils import timezone
from datetime import timedelta
from api.util_functions import create_log
from django.utils.translation import gettext as _

class Command(BaseCommand):
    help = "Update old orders with more than 3 months to 'completed' or 'completed_return' status"

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.NOTICE("Starting orders update..."))

        # Get the start of today in current timezone
        today = timezone.now().astimezone().replace(hour=0, minute=0, second=0, microsecond=0)

        # Cutoff = 90 days ago, end of that day
        cutoff_date = today - timedelta(days=60) + timedelta(days=1)

        completed_returned_status = Status.objects.get(code='completed_returned')
        completed_status = Status.objects.get(code='completed')

        with transaction.atomic():
            orders_to_update = Order.objects.select_related('company').filter(
                store_status_change_date__lte=cutoff_date, 
                status__code__in=["returned", "money_received"]
            )
            updated_count = 0

            for order in orders_to_update:
                if order.status.code == "returned":
                    order.status = completed_returned_status
                else:
                    order.status = completed_status
                order.save(update_fields=["status"])
                
                create_log('Order', order.company, _('Automatically Order Closed after 90 days'), 'System', order.id)
                updated_count += 1
            self.stdout.write(self.style.SUCCESS(f"Updated {updated_count} orders."))
        self.stdout.write(self.style.SUCCESS("Orders update completed."))
