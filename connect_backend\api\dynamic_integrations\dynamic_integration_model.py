from django.db import models
from ..models import SuperModel
from ..delivery_company.delivery_company_model import DeliveryCompany
import json

class IntegrationTemplate(SuperModel):
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    meta_fields = models.JSONField(default=list)
    active = models.BooleanField(default=True)
    url = models.URLField(max_length=255, null=True, blank=True)
    connection_meta_fields = models.JSONField(default=list)
    order_custom_fields = models.JSONField(default=list)

class RequestTemplate(models.Model):
    name = models.CharField(max_length=255)
    method = models.CharField(max_length=255, default = 'POST')
    endpoint = models.CharField(max_length=255)
    model_name = models.CharField(max_length=255)
    delivery_company = models.ForeignKey(DeliveryCompany, on_delete=models.SET_NULL, null=True)
    integration_template = models.ForeignKey('IntegrationTemplate', on_delete=models.SET_NULL, null=True, blank=True, related_name='request_templates')
    headers = models.JSO<PERSON>ield(default=dict)
    body = models.JSONField(default=dict)
    return_static = models.BooleanField(default=False)
    is_custom = models.BooleanField(default=False)
    is_querystring = models.BooleanField(default=False)
    process_function = models.TextField(null=True)

    def save(self, *args, **kwargs):
        if isinstance(self.headers, str):
            self.headers = json.loads(self.headers)
        if isinstance(self.body, str):
            self.body = json.loads(self.body)
        super().save(*args, **kwargs)

class ResponseTemplate(models.Model):
    request_template = models.OneToOneField(RequestTemplate, related_name='response_template', on_delete=models.CASCADE)  
    success_type = models.CharField(max_length=50, choices=[("status", "HTTP Status"), ("field", "Response Field")])
    success_values = models.JSONField(default=list)
    failure_values = models.JSONField(default=list)
    success_path = models.CharField(max_length=255, null=True, blank=True)
    error_path = models.CharField(max_length=255, null=True, blank=True)
    data_path = models.CharField(max_length=255, null=True, blank=True)
    pagination_field = models.CharField(max_length=255, null=True, blank=True)
    iterate_pages = models.BooleanField(default=False)
    data_type = models.CharField(max_length=50, choices=[("json", "JSON"), ("list", "List"), ("key_value", "Key-Value")])
    data_mapping = models.JSONField(default=dict)
    process_function = models.TextField(null=True)

class MockResponse:
    def __init__(self, json_data, status_code):
        self._json = json_data
        self.status_code = status_code

    def json(self):
        return self._json
    
class RequestTemplatePrerequisite(models.Model):
    request_template = models.ForeignKey("RequestTemplate", on_delete=models.CASCADE, related_name='prerequisite_links')
    prerequisite = models.ForeignKey("RequestTemplate", on_delete=models.CASCADE, related_name='used_in_templates')
    position = models.PositiveIntegerField()
    input = models.JSONField(default=dict, help_text="Map of inputs from parent template to prerequisite template")

    class Meta:
        unique_together = ('request_template', 'prerequisite')
        ordering = ['position']
