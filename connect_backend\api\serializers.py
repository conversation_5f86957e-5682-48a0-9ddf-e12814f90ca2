from collections import defaultdict
from .financial_reconciliation.financial_reconciliation_model import FinancialReconciliation
from rest_framework import serializers
from django.conf import settings
from .products.product_model import *
from .lookups.lookup_model import *
from .auth.auth_model import *
from .delivery_company.delivery_company_model import *
from .integrations.integration_model import *
from .orders.order_model import *
from .users.user_model import *
from .logs.logs_model import *
from .connection.connection_model import *
from .auth.auth_model import *
from .pricelists.pricelist_model import *
from .client.client_model import *
from .mystore.mystore_model import *
from .billing.billing_model import *
from .notification.notification_model import *
from .collection.collection_model import *
from .collection.collection_model import *
from .warehouse.warehouse_model import *
from .delivery_integrations.delivery_integrations_model import *
from .reseller.models import *
from .admin.admin_model import *
from django.utils.translation import gettext_lazy as _
from .category.category_model import *
from django.core.cache import cache
import datetime
from .mystore.users.user_model import *
from .admin.admin_tickets.admin_ticket_model import *
from django.contrib.auth.models import Permission
from .developer_connection.developer_connection_model import *
import json
from .dynamic_integrations.dynamic_integration_model import *
from django.apps import apps
from .admin.admin_tickets.admin_ticket_model import *
from .feature.feature_model import Feature, CompanyFeature
from .admin.store_domain_mapping.store_domain_mapping_model import StoreDomainMapping

def get_model_fields(model_name):
    try:
        model = apps.get_model(app_label="api", model_name=model_name)
        return {field.name: field for field in model._meta.get_fields()}
    except LookupError:
        return None
    
class ImageUrlField(serializers.CharField):
    def to_representation(self, value):
        if value:
            return f"{settings.MEDIA_URL}{value}"
        return value

class RoleSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = ['role_id', 'name', 'active','code']
    
    def get_name(self, obj):
        return _(obj.name)

class ProductPriceSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductPrice
        fields = ['id', 'country', 'price']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data

        return representation

class ProductsImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductsImage
        fields = ['id', 'product_image_url']

class CategorySerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Category
        fields = ["id", "category_sequence", "name", "category_image", "category_image_url", "publish","active"]

class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tags
        fields = ['id','name']

class ProductSerializer(serializers.ModelSerializer):
    images = ProductsImageSerializer(many=True, read_only=True)
    category = CategorySerializer(read_only=True)
    tags = TagSerializer(many=True)
    variants = serializers.SerializerMethodField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'images', 'price', 'cost', 'description', 'product_sequence', 
            'reference_sequence', 'tags', 'active', 'publish', 'category', 'created_at', 
            'updated_at', 'created_by', 'updated_by', 'prices', 'virtual_stock', 'physical_stock',
            'reserved_stock','delivery_attempt_count','successful_delivery_count','return_attempt_count',
            'variants'
        ]
        
    def get_variants(self, obj):
        variant_ids = obj.variants.values_list('id', flat=True)
        variants = WarehouseVariant.objects.filter(variant__id__in=variant_ids)
        return [
            {
                'id': wv.id,
                'warehouse': wv.warehouse_id,
                'physical_quantity': wv.physical_quantity,
                'virtual_quantity': wv.virtual_quantity,
                'reserved_quantity': wv.reserved_quantity
            }
            for wv in variants
        ]

    def to_representation(self, instance):
        cache_key = f"product_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)

        if instance.prices:
            representation['prices'] = ProductPriceSerializer(instance.prices, many=True).data

        representation['images'] = ProductsImageSerializer(instance.images.all(), many=True).data

        if instance.category:
            representation['category'] = CategorySerializer(instance.category).data

        cache.set(cache_key, representation, timeout=3600)
        return representation

class ThinProductSerializer(serializers.ModelSerializer):

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'price', 'cost', 'prices', 'updated_at'
        ]
        
    def to_representation(self, instance):
        cache_key = f"thin_product_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)

        if instance.prices:
            representation['prices'] = ProductPriceSerializer(instance.prices, many=True).data

        cache.set(cache_key, representation, timeout=3600)
        return representation

class AttributeSerializer(serializers.ModelSerializer):

    class Meta:
        model = Attribute
        fields = ['id', 'name']
        
class AttributeValueSerializer(serializers.ModelSerializer):
    class Meta:
        model = AttributeValue
        fields = ['id', 'attribute', 'value', 'company']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.attribute:
            representation['attribute'] = AttributeSerializer(instance.attribute).data

        return representation
    
class VariantAttributeSerializer(serializers.ModelSerializer):
    class Meta:
        model = VariantAttribute
        fields = ['id', 'variant', 'value']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.value:
            representation['value'] = AttributeValueSerializer(instance.value).data

        return representation

class ProductVariantSerializer(serializers.ModelSerializer):
    attributes = VariantAttributeSerializer(many=True, read_only=True)
    warehouse_variants = serializers.SerializerMethodField()
    class Meta:
        model = ProductVariant
        fields = [
            'id', 'product', 'name', 'sku', 'attributes', 'variant_image_url', 'updated_at','delivery_attempt_count',
            'successful_delivery_count','return_attempt_count','warehouse_variants', 'warehouses'
        ]

    def get_warehouse_variants(self, obj):
        return [{'id': wv.id, 'warehouse': WarehouseSerializer(wv.warehouse).data,
                'physical_quantity':wv.physical_quantity,'virtual_quantity':wv.virtual_quantity,
                'reserved_quantity':wv.reserved_quantity} for wv in obj.warehouse_variants.all()]
    
    def to_representation(self, instance):
        cache_key = f"product_variant_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            elif instance.product.updated_at and cached_updated_at and instance.product.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)

        if instance.product:
            representation['product'] = ProductSerializer(instance.product).data

        if instance.attributes:
            representation['attributes'] = VariantAttributeSerializer(instance.attributes, many=True).data

        if instance.warehouses:
            representation['warehouses'] = WarehouseSerializer(instance.warehouses, many=True).data

        cache.set(cache_key, representation, timeout=3600)
        return representation

class ThinProductVariantSerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductVariant
        fields = [
            'id', 'product', 'name', 'sku', 'variant_image_url', 'updated_at'
        ]

    def to_representation(self, instance):
        cache_key = f"thin_product_variant_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            elif instance.product.updated_at and cached_updated_at and instance.product.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)

        if instance.product:
            representation['product'] = ThinProductSerializer(instance.product).data

        cache.set(cache_key, representation, timeout=3600)
        return representation

class SubAreaSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubArea
        fields = ['id', 'name', 'code', 'area', 'is_default_for_area', 'updated_at']

    def to_representation(self, instance):
        cache_key = f"sub_area_{instance.id}"
        cached_representation = cache.get(cache_key)
        if 'show_area' not in self.context:
            self.context['show_area'] = True
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at', None)
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            if 'show_subareas' not in self.context and 'show_area' not in self.context and self.context.get('show_area', True):
                fuzzy_data = FuzzySubArea.objects.filter(sub_area_name_arabic=instance.name).last()
                if fuzzy_data:
                    cached_representation['sub_area_name_english'] = fuzzy_data.sub_area_name_english
                    cached_representation['sub_area_name_hebrew'] = fuzzy_data.sub_area_name_hebrew
                return cached_representation
        representation = super().to_representation(instance)
        fuzzy_data = FuzzySubArea.objects.filter(sub_area_name_arabic=instance.name).last()
        if fuzzy_data:
            representation['sub_area_name_english'] = fuzzy_data.sub_area_name_english
            representation['sub_area_name_hebrew'] = fuzzy_data.sub_area_name_hebrew
        if instance.area and self.context.get('show_area', False):
            representation['area'] = AreaSerializer(instance.area).data
        else:
            representation.pop('area', None)
        cache.set(cache_key, representation, timeout=3600)
        return representation

class ThinSubAreaSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubArea
        fields = ['id', 'name', 'code', 'updated_at']

    def to_representation(self, instance):
        cache_key = f"thin_sub_area_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at', None)
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation


class AreaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Area
        fields = ['id', 'name', 'code', 'country', 'subareas', 'updated_at']

    def to_representation(self, instance):
        cache_key = f"area_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation and 'show_subareas' not in self.context:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                fuzzy_data = FuzzyArea.objects.filter(area_name_arabic=instance.name).last()
                if fuzzy_data:
                    cached_representation['area_name_english'] = fuzzy_data.area_name_english
                    cached_representation['area_name_hebrew'] = fuzzy_data.area_name_hebrew
                return cached_representation
        representation = super().to_representation(instance)
        if self.context.get('show_subareas', False):
            representation['subareas'] = SubAreaSerializer(instance.subareas.all(), many=True).data
        else:
            representation.pop('subareas', None)
        fuzzy_data = FuzzyArea.objects.filter(area_name_arabic=instance.name).last()
        if fuzzy_data:
            representation['area_name_english'] = fuzzy_data.area_name_english
            representation['area_name_hebrew'] = fuzzy_data.area_name_hebrew
        if instance.country:
            representation['country'] = instance.country.code
        try:
            representation['default_subarea'] = SubAreaSerializer(SubArea.objects.get(area=instance, is_default_for_area=True), context={'show_area': False}).data
        except SubArea.DoesNotExist:
            representation['default_subarea'] = None
            pass
        if not 'show_subareas' in self.context:
            cache.set(cache_key, representation, timeout=3600)
        return representation

class ThinAreaSerializer(serializers.ModelSerializer):
    class Meta:
        model = Area
        fields = ['id', 'name', 'code', 'updated_at']

    def to_representation(self, instance):
        cache_key = f"thin_area_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation

class CountrySerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = Country
        fields = ['id', 'name', 'code', 'mobile_intro', 'country_flag', 'currency', 'mobile_placeholder', 'areas', 'mobile_number_length']

    def get_name(self, obj):
        return _(obj.name)

    def to_representation(self, instance):
        cache_key = f"country_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation and 'show_areas' not in self.context:
            return cached_representation
        representation = super().to_representation(instance)
        if self.context.get('show_areas', False):
            representation['areas'] = AreaSerializer(
                instance.areas.all(), many=True, context={'show_subareas': self.context.get('show_subareas', False)}).data
        else:
            representation.pop('areas', None)
        if not 'show_areas' in self.context:
            cache.set(cache_key, representation, timeout=3600)
        return representation

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        show_areas = self.context.get('show_areas', False)
        if show_areas:
            representation['areas'] = AreaSerializer(instance.areas.all(), many=True, required=False, context={'show_subareas': self.context.get('show_subareas', False)}).data
        else:
            representation.pop('areas', None)

        return representation

class ThinCountrySerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = Country
        fields = ['id', 'name', 'code', 'mobile_intro', 'country_flag', 'currency', 'mobile_placeholder', 'mobile_number_length']

    def get_name(self, obj):
        return _(obj.name)

    def to_representation(self, instance):
        cache_key = f"thin_country_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            return cached_representation
        representation = super().to_representation(instance)
       
        return representation

class CompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ['created_at', 'updated_at', 'created_by', 'updated_by', 'company_id', 'company_name', 'company_mobile', 'company_area', 
                  'used_free_trial', 'free_trial_end_date', 'company_image_url','company_social_link', 'owner', 'country'
                  ]

    def to_representation(self, instance):
        
        if self.context.get('use_company_follow_up_serializer'):
            return CompanyFollowUpSerializer(instance).data
        
        representation = super().to_representation(instance)
        
        if instance.company_area:
            representation['company_area'] = AreaSerializer(instance.company_area).data

        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data
        
        representation['company_currency'] = instance.company_area.country.currency

        if instance.owner:
            representation['owner'] = UserSerializer(instance.owner, context={'dont_show_company': True}).data
        
        return representation

class ThinCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ['company_id', 'company_name', 'company_mobile']

class WarehouseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Warehouse
        fields = ['id', 'name', 'country', 'area', 'address', 'company', 'reseller']

    def to_representation(self, instance):
        cache_key = f"warehouse_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation and not self.context.get('show_connection', False):
            return cached_representation
        representation = super().to_representation(instance)

        if instance.country:
            representation['country'] = ThinCountrySerializer(instance.country).data

        if instance.area:
            representation['area'] = ThinAreaSerializer(instance.area).data

        if instance.reseller:
            representation['reseller'] = ResellerSerializer(instance.reseller).data
        
        if instance.warehouse_connection.exists() and self.context.get('show_connection', False) and self.context.get('company', None):
            try:
                representation['warehouse_connection'] = CompanyWarehouseConnectionSerializer(
                    instance.warehouse_connection.get(company=self.context.get('company'))
                ).data
            except CompanyWarehouseConnection.DoesNotExist:
                representation['warehouse_connection'] = None
        elif self.context.get('show_connection', False):
            representation['warehouse_connection'] = None

        cache.set(cache_key, representation, timeout=3600)
        return representation

class WarehouseVariantSerializer(serializers.ModelSerializer):
    warehouse = WarehouseSerializer(read_only=True)
    variant = serializers.PrimaryKeyRelatedField(read_only=True)
    class Meta:
        model = WarehouseVariant
        fields = [
            'id', 'warehouse', 'variant', 'physical_quantity', 
            'virtual_quantity', 'reserved_quantity', 'updated_at'
        ]

    def to_representation(self, instance):
        cache_prefix = self.context.get('cache_prefix', 'warehouse_variant_')
        cache_key = f"{cache_prefix}{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            elif instance.variant.updated_at > cached_updated_at:
                cache.delete(cache_key)
            elif instance.product_locations.filter(updated_at__gt=cached_updated_at).exists():
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)

        if instance.warehouse:
            representation['warehouse'] = WarehouseSerializer(instance.warehouse).data

        additional_fields = self.context.get("additional_fields", [])
        if "company" in additional_fields:
            company = instance.variant.product.company
            if company:
                representation['company'] = ThinCompanySerializer(company).data

        if instance.variant:
            representation['variant'] = ProductVariantSerializer(instance.variant).data
            representation['name'] = instance.variant.name

        if not instance.variant.variant_image_url or 'ProductsImage object' in instance.variant.variant_image_url:
            representation['variant']['variant_image_url'] = instance.variant.product.images.first().product_image_url if instance.variant.product.images.first() else ''

        if instance.product_locations.exists():
            representation['locations'] = LocationSerializer(Location.objects.filter(id__in=instance.product_locations.values_list('location_id', flat=True)), many=True).data
        cache.set(cache_key, representation, timeout=3600)
        return representation

class ThinWarehouseVariantSerializer(serializers.ModelSerializer):
    variant = serializers.PrimaryKeyRelatedField(read_only=True)
    class Meta:
        model = WarehouseVariant
        fields = [
            'id', 'variant', 'physical_quantity', 
            'virtual_quantity', 'reserved_quantity', 'updated_at'
        ]

    def to_representation(self, instance):
        cache_key = f"thin_warehouse_variant_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            elif instance.variant.updated_at > cached_updated_at:
                cache.delete(cache_key)
            elif instance.product_locations.filter(updated_at__gt=cached_updated_at).exists():
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)

        additional_fields = self.context.get("additional_fields", [])
        if "company" in additional_fields:
            company = instance.variant.product.company
            if company:
                representation['company'] = ThinCompanySerializer(company).data

        if instance.variant:
            representation['variant'] = ThinProductVariantSerializer(instance.variant).data
            representation['name'] = instance.variant.name

        if not instance.variant.variant_image_url or 'ProductsImage object' in instance.variant.variant_image_url:
            representation['variant']['variant_image_url'] = instance.variant.product.images.first().product_image_url if instance.variant.product.images.first() else ''

        cache.set(cache_key, representation, timeout=3600)
        return representation

class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'mobile_number',
            'user_image_url', 'name', 'email', 'role', 'second_phone', 'address',
            'area', 'country', 'country_code', 'is_active', 'player_id', 'waiting_confirmation',
            'lang', 'driver_pricelist'
        ]
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        # Custom handling for nested fields
        if instance.role:
            representation['role'] = RoleSerializer(instance.role).data
        
        if instance.area:
            representation['area'] = AreaSerializer(instance.area).data
        
        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data
        
        if instance.driver_pricelist:
            representation['driver_pricelist'] = PricelistSerializer(instance.driver_pricelist).data

        request = self.context.get('request')  
        use_follow_up_serializer = self.context.get('use_follow_up_serializer', False)
        

        if instance.company and not self.context.get('dont_show_company', False):
            if use_follow_up_serializer:
                representation['company'] = CompanyFollowUpSerializer(instance.company).data
            else:
                representation['company'] = CompanySerializer(instance.company).data
        
        return representation

class DeliveryCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = DeliveryCompany
        fields = ['id', 'name', 'company_image', 'delivery_company_url', 'delivery_company_db', 'country', 'is_active', 'fully_synced_areas', 'fully_synced_subareas', 
                'is_subarea_required', 'fully_synced_statuses', 'connections', 'is_olivery_company', 'meta_data', 'use_area_mapping', 'image_url', 'updated_at', 'order_custom_fields',
                'integration_template', 'template_meta_data']

    def to_representation(self, instance):
        cache_key = f"delivery_company_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            elif self.context.get('show_connection', False):
                cache.delete(cache_key)
            else:
                if Reseller.objects.filter(delivery_company=instance).exists():
                    cached_representation['has_reseller'] = True
                else:
                    cached_representation['has_reseller'] = False
                return cached_representation
        representation = super().to_representation(instance)
        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data
        
        if instance.connections and self.context.get('show_connection', False) and self.context.get('company', None):
            representation['connections'] = ConnectDeliveryCompanySerializer(instance.connections.filter(company=self.context.get('company')), many=True).data
        if Reseller.objects.filter(delivery_company=instance).exists():
            representation['has_reseller'] = True
        else:
            representation['has_reseller'] = False

        if instance.integration_template:
            representation['integration_template'] = IntegrationTemplateSerializer(instance.integration_template).data

        if not self.context.get('show_connection', False):
            cache.set(cache_key, representation, timeout=3600)
        return representation
    
class ThinDeliveryCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = DeliveryCompany
        fields = ['id', 'name', 'company_image', 'is_olivery_company', 'meta_data', 'updated_at', 'order_custom_fields']

    def to_representation(self, instance):
        cache_key = f"thin_delivery_company_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
               return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation
    
class StatusSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = Status
        fields = ['id', 'name', 'code', 'hidden']

    def get_name(self, obj):
        return _(obj.name)

    def to_representation(self, instance):
        cache_key = f"status_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation

class ProviderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Provider
        fields = ['id', 'name', 'countries']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.countries:
            representation['countries'] = CountrySerializer(instance.countries.all(), many=True).data

        return representation

class CollectionStatusSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = CollectionStatus
        fields = ['id', 'code', 'name']

    def get_name(self, obj):
        return _(obj.name)

    def to_representation(self, instance):
        cache_key = f"collection_status_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation

class CollectionTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = CollectionType
        fields = ['id', 'code', 'name']

    def to_representation(self, instance):
        cache_key = f"collection_type_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation

class CollectionBankSerializer(serializers.ModelSerializer):
    class Meta:
        model = CollectionBank
        fields = ['id', 'code', 'name', 'country']

    def to_representation(self, instance):
        cache_key = f"collection_bank_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            return cached_representation
        representation = super().to_representation(instance)

        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data

        cache.set(cache_key, representation, timeout=3600)
        return representation

class CollectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Collection
        fields = ['id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'collection_status', 'collection_type', 
                  'collection_bank', 'amount', 'currency', 'company', 'updated_at']

    def to_representation(self, instance):
        cache_key = f"collection_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)
        
        if instance.collection_status:
            representation['collection_status'] = CollectionStatusSerializer(instance.collection_status).data
        
        if instance.collection_type:
            representation['collection_type'] = CollectionTypeSerializer(instance.collection_type).data
        
        if instance.collection_bank:
            representation['collection_bank'] = CollectionBankSerializer(instance.collection_bank).data

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data
        
        cache.set(cache_key, representation, timeout=3600)
        return representation
    
class ConversionRateSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConversionRate
        fields = ['id', 'currency_from', 'currency_to', 'rate', 'date']

class StatusMapSerializer(serializers.ModelSerializer):
    delivery_company_status = serializers.SerializerMethodField()

    class Meta:
        model = StatusMap
        fields = ['id', 'status_code', 'delivery_company_status', 'description']

    def get_delivery_company_status(self, obj):
        if obj.delivery_company_status:
            return _(obj.delivery_company_status)
        else:
            return None

    def to_representation(self, instance):
        cache_key = f"status_map_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation

class OrderLineSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderLine
        fields = ['id', 'product', 'quantity', 'price', 'product_variant', 'updated_at']

    def to_representation(self, instance):
        cache_key = f"order_line_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)

        if instance.product:
            representation['product'] = ThinProductSerializer(instance.product).data

        if instance.product_variant:
            representation['product_variant'] = ThinWarehouseVariantSerializer(instance.product_variant).data

        cache.set(cache_key, representation, timeout=3600)
        return representation

class OrderTypeSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = OrderType
        fields = ['id', 'code', 'name']

    def get_name(self, obj):
        return _(obj.name)

    def to_representation(self, instance):
        cache_key = f"order_type_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation

class BranchPageSerializer(serializers.ModelSerializer):
    class Meta:
        model = BranchPage
        fields = ['id', 'name', 'branch_page_image_url', 'updated_at', 'branch_page_commercial_number']

    def to_representation(self, instance):
        cache_key = f"branch_page_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation

class TicketSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ticket
        fields = ['id', 'ticket_title', 'ticket_description', 'ticket_file_url', 'file_type', 'fix_version']


class ReleaseDocumentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ReleaseDocument
        fields = ['id', 'document_title', 'document_content', 'is_active']

class OrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        exclude = ['company']  # Exclude company field from default serialization
        depth = 1

    def get_delivery_company_status(self, obj):
        if obj.delivery_company_status:
            return _(obj.delivery_company_status)
        else:
            return None
        
    def get_area_mismatch(self, obj):
        if obj.area_mismatch:
            area_mismatch = obj.area_mismatch
            area_mismatch = area_mismatch.replace('Invalid area name', str(_('Invalid area name')))
            area_mismatch = area_mismatch.replace('Invalid sub area name', str(_('Invalid sub area name')))
            area_mismatch = area_mismatch.replace('Invalid country code', str(_('Invalid country code')))
            area_mismatch = area_mismatch.replace('Invalid country name', str(_('Invalid country name')))
            return area_mismatch
        return ''
        
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation["area_mismatch"] = self.get_area_mismatch(instance)
        representation["delivery_company_status"] = self.get_delivery_company_status(instance)
        # Custom handling for nested fields
        if instance.delivery_company:
            representation['delivery_company'] = ThinDeliveryCompanySerializer(instance.delivery_company).data
        
        if instance.area:
            representation['area'] = ThinAreaSerializer(instance.area).data
        
        if instance.sub_area:
            representation['sub_area'] = ThinSubAreaSerializer(instance.sub_area).data
        
        if instance.country:
            representation['country'] = ThinCountrySerializer(instance.country).data
        
        if instance.status:
            representation['status'] = StatusSerializer(instance.status).data

        if instance.order_type:
            representation['order_type'] = OrderTypeSerializer(instance.order_type).data

        if instance.parent_order:
            representation['parent_order'] = OrderSerializer(instance.parent_order).data
            representation.get('parent_order', {}).pop('cloned_orders', None)
            
        if instance.collection:
            representation['collection'] = CollectionSerializer(instance.collection).data

        if instance.cloned_orders.exists():
            representation['cloned_orders'] = CloneOrderSerializer(instance.cloned_orders.all(), many=True).data


        if instance.branch_page:
            representation['branch_page'] = BranchPageSerializer(instance.branch_page).data

        if instance.driver:
            representation['driver'] = UserSerializer(instance.driver).data

        if instance.delivery_status:
            representation['delivery_status'] = StatusMapSerializer(instance.delivery_status).data

        if instance.connection:
            representation['connection'] = ThinConnectDeliveryCompanySerializer(instance.connection).data

        if instance.store_stuck_reason:
            representation['store_stuck_reason'] = StuckReasonSerializer(instance.store_stuck_reason).data

        if instance.warehouse:
            representation['warehouse'] = WarehouseSerializer(instance.warehouse).data

        # Handle additional fields for OrderSerializer
        additional_fields = self.context.get("additional_fields", [])
        if "company" in additional_fields and instance.company:
            representation['company'] = ThinCompanySerializer(instance.company).data

        representation['order_lines'] = OrderLineSerializer(instance.order_lines.all(), many=True).data
        
        return representation

class CloneOrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = ['id', 'order_sequence']

    def to_representation(self, instance):
        cache_key = f"clone_order_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation

class OtpCodesSerializer(serializers.ModelSerializer):
    class Meta:
        model = OtpCodes
        fields = [
            'id', 'code', 'mobile_number', 'created_at', 'expire_date',
            'number_of_trials', 'error_trials'
        ]


class OtpTokenSerializer(serializers.ModelSerializer):
    class Meta:
        model = OtpToken
        fields = [
            'id', 'mobile_number', 'token', 'created_at', 'expires_at'
        ]


class EcommerceTokenSerializer(serializers.ModelSerializer):

    class Meta:
        model = EcommerceToken
        fields = [
            'id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'ecommerce', 'expires_at'
        ]


class LogsSerializer(serializers.ModelSerializer):

    class Meta:
        model = Logs
        fields = '__all__'

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        representation['fields'] =  LogsInfoSerializer(LogsInfo.objects.filter(log=instance), many=True).data

        return representation


class LogsInfoSerializer(serializers.ModelSerializer):
    old_value = serializers.SerializerMethodField()
    new_value = serializers.SerializerMethodField()

    class Meta:
        model = LogsInfo
        fields = [
            'id', 'field_name', 'old_value', 'new_value'
        ]

    def get_old_value(self, obj):
        if obj.old_value:
            message = ''
            if ',' in obj.old_value:
                message = ','.join([str(_(part)) for part in obj.old_value.split(',')])
                return message
            return str(_(obj.old_value))
        else:
            return None
        
    def get_new_value(self, obj):
        if obj.new_value:
            message = ''
            if ',' in obj.new_value:
                message = ','.join([str(_(part)) for part in obj.new_value.split(',')])
                return message
            return str(_(obj.new_value))
        else:
            return None

class TrackedFieldsSerializer(serializers.ModelSerializer):

    class Meta:
        model = TrackedFields
        fields = [
            'id', 'field_name', 'model'
        ]


class ConnectDeliveryCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = ConnectDeliveryCompany
        fields = [
            'id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'delivery_company', 'connection_status', 'user_url', 'area', 
            'name', 'display_name', 'custom_field_defaults'
        ]
    
    def to_representation(self, instance):
        cache_key = f"connection_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                try:
                    if instance.credentials and instance.credentials.updated_at and instance.credentials.updated_at > cached_updated_at:
                        cache.delete(cache_key)
                    else:
                        cached_representation['parsed_meta_fields'] = get_connect_delivery_company_parsed_meta_fields(instance)
                        return cached_representation
                except Exception as e:
                    pass
        representation = super().to_representation(instance)

        if self.context.get('show_delivery_company', False):
            representation = DeliveryCompanySerializer(instance.delivery_company).data
        else:
            if instance.delivery_company:
                representation['delivery_company'] = DeliveryCompanySerializer(instance.delivery_company).data
        
        representation['username'] = None
        representation['password'] = None
        representation['token'] = None
        representation['meta_data'] = []
        representation['connection_status'] = 'not_connected'
        if instance.connection_status:
            representation['connection_status'] = instance.connection_status
            try:
                if instance.credentials:
                    representation['username'] = instance.credentials.company_username
                    representation['password'] = instance.credentials.company_password
                    representation['token'] = instance.credentials.token
                    representation['meta_data'] = instance.credentials.meta_data
            except Exception as e:
                representation['username'] = None
                representation['password'] = None
                representation['token'] = None
                representation['meta_data'] = []

        if instance.area:
            representation['area'] = AreaSerializer(instance.area).data
        else:
            representation['area'] = None

        representation['parsed_meta_fields'] = get_connect_delivery_company_parsed_meta_fields(instance)
        
        cache.set(cache_key, representation, timeout=3600)
        return representation
 
def get_connect_delivery_company_parsed_meta_fields(instance):
    if instance.delivery_company.order_custom_fields:
        parsed_meta_fields = []
        for custom_field in instance.delivery_company.order_custom_fields:
            if custom_field.get('name') in instance.custom_field_defaults.keys():
                custom_field['default'] = instance.custom_field_defaults.get(custom_field.get('name'))
            if custom_field.get('type') == 'boolean':
                if custom_field.get('default') in ['True', 'T']:
                    custom_field['default'] = True
                elif custom_field.get('default') in ['False', 'F']:
                    custom_field['default'] = False
            if custom_field.get('type') == 'date':
                try:
                    days = int(custom_field.get('default'))
                    custom_field['default'] = timezone.now() + timedelta(days=days)
                except Exception as e:
                    custom_field['default'] = timezone.now()
            parsed_meta_fields.append(custom_field)
        return parsed_meta_fields

class ThinConnectDeliveryCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = ConnectDeliveryCompany
        fields = [
            'id', 'updated_at', 'delivery_company', 'area', 
            'name', 'display_name', 'custom_field_defaults'
        ]
   
    def to_representation(self, instance):
        cache_key = f"thin_connection_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            cached_updated_at = cached_representation.get('updated_at')
            if isinstance(cached_updated_at, str):
                cached_updated_at = datetime.datetime.fromisoformat(cached_updated_at.replace("Z", "+00:00"))
            if instance.updated_at and cached_updated_at and instance.updated_at > cached_updated_at:
                cache.delete(cache_key)
            else:
                try:
                    if instance.credentials and instance.credentials.updated_at and instance.credentials.updated_at > cached_updated_at:
                        cache.delete(cache_key)
                    else:
                        cached_representation['parsed_meta_fields'] = get_connect_delivery_company_parsed_meta_fields(instance)
                        return cached_representation
                except Exception as e:
                    pass
        representation = super().to_representation(instance)

        if instance.delivery_company:
            representation['delivery_company'] = ThinDeliveryCompanySerializer(instance.delivery_company).data
    
        representation['username'] = None
        representation['password'] = None
        representation['token'] = None
        representation['meta_data'] = []
        representation['connection_status'] = 'not_connected'
        if instance.connection_status:
            try:
                if instance.credentials:
                    representation['meta_data'] = instance.credentials.meta_data
            except Exception as e:
                representation['username'] = None
                representation['password'] = None
                representation['token'] = None
                representation['meta_data'] = []

        if instance.area:
            representation['area'] = ThinAreaSerializer(instance.area).data
        else:
            representation['area'] = None

        representation['parsed_meta_fields'] = get_connect_delivery_company_parsed_meta_fields(instance)
        
        cache.set(cache_key, representation, timeout=3600)
        return representation

class ConnectionUserSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConnectionUser
        fields = '__all__'

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        representation['username'] = representation.pop('company_username', None)
        representation['password'] = representation.pop('company_password', None)
        
        if instance.delivery_company:
            representation['delivery_company'] = DeliveryCompanySerializer(instance.delivery_company).data
            representation['website'] = instance.delivery_company.delivery_company_url
        
        return representation
    
class PricelistSerializer(serializers.ModelSerializer):
    class Meta:
        model = Pricelist
        fields = ['id', 'name', 'code', 'connection']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.connection:
            representation['connection'] = ConnectDeliveryCompanySerializer(instance.connection).data
        
        return representation

class PricelistItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = PricelistItem
        fields = ['id', 'from_area', 'to_area', 'order_type', 'price']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.from_area:
            representation['from_area'] = AreaSerializer(instance.from_area).data

        if instance.to_area:
            representation['to_area'] = AreaSerializer(instance.to_area).data

        return representation
    
class ClientSerializer(serializers.ModelSerializer):
    class Meta:
        model = Client
        fields = ['id', 'name','mobile_number', 'address', 'area', 'country', 'sub_area', 'country_code']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.area:
            representation['area'] = AreaSerializer(instance.area).data

        if instance.sub_area:
            representation['sub_area'] = SubAreaSerializer(instance.sub_area).data

        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data

        return representation
    
class StripeProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = StripeProduct
        fields = ['id', 'price', 'price_id', 'recurring_interval', 'currency', 'package']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.package:
            representation['package'] = PackageSerializer(instance.package).data
        
        return representation

class StoreSectionSerializer(serializers.ModelSerializer):
    categories = CategorySerializer(read_only=True,many=True)
    tags = TagSerializer(read_only = True,many=True)
    class Meta:
        model = StoreSection
        fields = [
            'id','section_description', "section_code", "categories", "tags", "active",
            'created_at', 'updated_at', 'created_by', 'updated_by',
        ]
    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.categories is not None:  
            representation['categories'] = CategorySerializer(instance.categories.all(), many=True).data
        else:
            representation['categories'] = []

        if instance.tags is not None:  
            representation['tags'] = TagSerializer(instance.tags.all(), many=True).data
        else:
            representation['tags'] = []


        return representation
    
class MyStoreSerializer(serializers.ModelSerializer):
    
    store_section = StoreSectionSerializer(many=True, read_only=True)  # Use nested serializer for store_section

    class Meta:
        model = MyStore
        fields = ['id', 'name', 'mystore_image_url', 'primary_color', 'secondary_color', 'title', 'company','store_section',
                'whatsapp_mobile', 'whatsapp_link', 'facebook', 'instagram', 'email', 'delivery_note', 'is_archived','store_template'
                ]
        
    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data
        
        if instance.store_section:
            representation['store_section'] = StoreSectionSerializer(instance.store_section.all(), many=True).data

        return representation

class BannerDetailSerializer(serializers.ModelSerializer):
    class Meta:
        model = BannerDetail
        fields = ['id', 'title', 'banner_image_url', 'link_url', 'link_label', 'link_color', 'acive', 'company', 'mystore']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data

        if instance.mystore:
            representation['mystore'] = MyStoreSerializer(instance.mystore).data

class PackageSerializer(serializers.ModelSerializer):

    class Meta:
        model = Package
        fields = ['id', 'name', 'description', 'no_of_orders', 'price_monthly', 'price_yearly', 'is_active', 'tier', 'stripe_product_id']

class CompanyPlanSerializer(serializers.ModelSerializer):

    class Meta:
        model = CompanyPlan
        fields = [
            'id',
            'package',
            'company'
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.package:
            representation['package'] = PackageSerializer(instance.package).data
        
        return representation

class SubscriptionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Subscription
        fields = '__all__'
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.company_plan:
            representation['company_plan'] = CompanyPlanSerializer(instance.company_plan).data
        
        return representation

class PaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Payment
        fields = ['id', 'stripe_payment_id', 'amount', 'currency', 'created_at', 'company', 'subscription']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data
        
        if instance.subscription:
            representation['subscription'] = SubscriptionSerializer(instance.subscription).data
        
        return representation

class NotificationSoundSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationSound
        fields = ['id', 'name', 'channel']

class NotificationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Notification
        fields = ['id', 'sound', 'delivery_status']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.sound:
            representation['sound'] = NotificationSoundSerializer(instance.sound).data
        
        return representation
    
class AreaMapSerializer(serializers.ModelSerializer):
    class Meta:
        model = AreaMap
        fields = ['id', 'area', 'imported_area', 'imported_area_id', 'delivery_company']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.area:
            representation['area'] = AreaSerializer(instance.area).data

        if instance.delivery_company:
            representation['delivery_company'] = DeliveryCompanySerializer(instance.delivery_company).data
        
        return representation
    
class SubAreaMapSerializer(serializers.ModelSerializer):
    class Meta:
        model = SubAreaMap
        fields = ['id', 'sub_area', 'imported_sub_area', 'imported_sub_area_id', 'parent_area_name', 'delivery_company', 'parent_area_id']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.sub_area:
            representation['sub_area'] = SubAreaSerializer(instance.sub_area).data

        if instance.delivery_company:
            representation['delivery_company'] = DeliveryCompanySerializer(instance.delivery_company).data
        
        return representation
    
class FailedOrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = FailedOrder
        fields = ['id' ,'company', 'order', 'order_sequence', 'error_message', 'job_status', 'job_id', 'created_at', 'updated_at', 'created_by', 'updated_by']

class SystemConfigurationSerializer(serializers.ModelSerializer):
    class Meta:
        model = SystemConfiguration
        fields = ['id', 'support_mobile_number', 'default_release_document']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.default_release_document:
            representation['default_release_document'] = ReleaseDocumentSerializer(instance.default_release_document).data
        
        return representation

class ResellerUserPreferencesSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResellerUserPreferences
        fields = ['id', 'user', 'order_tree_view']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.order_tree_view:
            representation['order_tree_view'] = instance.get_order_tree_view()

        return representation

class StoreUserSerializer(serializers.ModelSerializer):

    class Meta:
        model = StoreUser
        fields = [
            'id', 'created_at', 'updated_at', 'created_by', 'updated_by', 'mobile_number',
            'name', 'email', 'second_phone', 'address',
            'area', 'country', 'country_code', 'is_active','language',
        ]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        # Custom handling for nested fields
        if instance.area:
            representation['area'] = AreaSerializer(instance.area).data
        
        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data
        
        return representation

class StoreUserFavoritesSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = StoreUserFavorites
        fields = ['id', 'product','created_at']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.product:
            representation['product'] = ProductSerializer(instance.product).data
        
        return representation

class StoreBillingAddressSerialaizer(serializers.ModelSerializer):

    class Meta:
        model = StoreBillingAddress
        fields = ['id', "first_name", "surname", "address_line1", "address_line2", "area",
                "sub_area", "country", "mobile_number", "second_mobile", "is_default",]

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data
        
        if instance.area:
            representation['area'] = AreaSerializer(instance.area).data
        
        if instance.sub_area:
            representation['sub_area'] = SubAreaSerializer(instance.sub_area).data
        
        return representation

class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance:
            try:
                permission_description = PermissionDescription.objects.get(permission=instance).description
                representation['description'] = permission_description.description
            except PermissionDescription.DoesNotExist:
                representation['description'] = ''
        
        return representation
    
class UserPreferencesSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserPreferences
        fields = ['id', 'user', 'order_tree_view']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.user:
            representation['user'] = UserSerializer(instance.user).data

        if instance.order_tree_view:
            representation['order_tree_view'] = instance.get_order_tree_view()

        return representation
    
class IntegrationLogsSerializer(serializers.ModelSerializer):
    class Meta:
        model = IntegrationLogs
        fields = [ 'id', 'company', 'integration', 'request_body', 'response_body', 'status_code', 'created_at', 'additional_information']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data
        
        return representation
    
class NotificationBannerSerializer(serializers.ModelSerializer):
    class Meta:
        model = NotificationBanner
        fields = ['id', 'title_en', 'title_ar', 'message_en', 'message_ar', 'start_date', 'end_date', 'active']

class ConnectionTokenSerializer(serializers.ModelSerializer):
    class Meta:
        model = ConnectionToken
        fields = ['id', 'company', 'delivery_company', 'created_at', 'expires_at', 'token', 'is_active']
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data
        
        if instance.delivery_company:
            representation['delivery_company'] = DeliveryCompanySerializer(instance.delivery_company).data
        
        return representation

class CompanyWalletSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyWallet
        fields = ['id', 'company', 'ils_balance', 'jod_balance']

class ClientPaymentSerializer(serializers.ModelSerializer):
    class Meta:
        model = ClientPayment
        fields = ['amount', 'currency', 'created_at', 'company', 'order', 'customer_name', 'provider']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data

        if instance.order:
            representation['order'] = OrderSerializer(instance.order).data

        return representation
    
class NotificationCenterSerializer(serializers.ModelSerializer):
    message = serializers.SerializerMethodField()

    class Meta:
        model = NotificationCenter
        fields = ['id','source','message','seen','orders_sequences', 'context']
    
    def get_message(self, obj):
        temp_context = obj.context
        for key, value in temp_context.items():
            if not isinstance(value, str):
                continue
            temp_context[key] = _(value)
        return _(obj.message).format(**(temp_context or {}))

class DeliveryStatusMapSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeliveryStatusMap
        fields = ['id', 'imported_status_name', 'imported_status_code', 'status', 'delivery_company']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.delivery_company:
            representation['delivery_company'] = DeliveryCompanySerializer(instance.delivery_company).data

        if instance.status:
            representation['status'] = StatusMapSerializer(instance.status).data

        return representation

class CompanyFollowUpSerializer(serializers.ModelSerializer):
    class Meta:
        model = Company
        fields = ['created_at', 'updated_at', 'created_by', 'updated_by', 'company_id', 'company_name',
                'company_mobile','company_area', 'used_free_trial', 'free_trial_end_date',
                'company_image_url','follow_up_link', 'follow_up_priorities', 'follow_up_note', 'owner']
        
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        
        if instance.company_area:
            representation['company_area'] = AreaSerializer(instance.company_area).data

        if instance.owner:
            representation['owner'] = UserSerializer(instance.owner, context={'dont_show_company': True}).data
        
        representation['company_currency'] = instance.company_area.country.currency
        
        return representation


class LimitUpgradeSerializer(serializers.ModelSerializer):
    class Meta:
        model = LimitUpgrade
        fields = ['id', 'no_of_orders', 'price', 'active']

class DeveloperConnectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeveloperConnection
        fields = ['id', 'name', 'description']

    def to_representation(self, instance):
        cache_key = f"developer_connection_{instance.id}"
        cached_representation = cache.get(cache_key)
        if cached_representation:
            return cached_representation
        representation = super().to_representation(instance)
        cache.set(cache_key, representation, timeout=3600)
        return representation
    
class RequestTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = RequestTemplate
        fields = '__all__'

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.prerequisite_links:
            representation['prerequisites'] = RequestTemplatePrerequisiteSerializer(instance.prerequisite_links.all(), many=True).data
        else:
            representation['prerequisites'] = []

        return representation

class RequestTemplatePrerequisiteSerializer(serializers.ModelSerializer):
    class Meta:
        model = RequestTemplatePrerequisite
        fields = '__all__'

    def to_representation(self, instance):
        representation = {
            "prerequisite_id": instance.prerequisite.id,
            "id": instance.id,
            "name": instance.prerequisite.name,
            "input": instance.input,
            "position": instance.position
        }

        return representation
    
class ResponseTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResponseTemplate
        fields = '__all__'

class DynamicResponseSerializer(serializers.Serializer):
    def __init__(self, *args, **kwargs):
        response_schema = kwargs.pop("response_schema", None)
        super().__init__(*args, **kwargs)
        self.response_schema = response_schema

    def parse_response(self, response):
        parsed_response = {}
        if not self.is_successful(response):
            error_path = self.response_schema.get('error_path', '')
            attrs = error_path.split('.')
            value = response.json()
            for attr in attrs:
                if isinstance(value, dict):
                    value = value.get(attr, None)
                else:
                    value = getattr(value, attr, None)
                
                if value is None:
                    return {"success": False, "data": f"Unexcpected Error"}
            return {"success": False, "data": f"{value}"}
        data = self.extract_data(response)

        parsed_response["success"] = True
        parsed_response["data"] = data
        return parsed_response

    def is_successful(self, response):
        success_type = self.response_schema.get("success_type")
        success_values = self.response_schema.get("success_values", [])
        failure_values = self.response_schema.get("failure_values", [])

        if success_type == "status":
            http_status = response.status_code
            return http_status in success_values and http_status not in failure_values
        elif success_type == "field":
            success_path = self.response_schema.get("success_path")
            success_flag = self.get_nested_value(response.json(), success_path)
            return success_flag in success_values and success_flag not in failure_values

        return False

    def extract_data(self, response):
        response_data = response.json()
        data_path = self.response_schema.get("data_path")
        data_mapping = self.response_schema.get("data_mapping", {})
        data = self.get_nested_value(response_data, data_path) if data_path else response_data
        context = {}
        request_context = self.response_schema.get("request_context", {})
        if request_context:
            context['request_context'] = request_context
        process_function = self.response_schema.get("process_function")
        if process_function:
            try:
                local_vars = {"response_data": data, "context": context}
                exec(process_function, {}, local_vars)
                data = local_vars.get("response_data", data)
            except Exception as e:
                print(f"Error executing process_function: {e}")
        if isinstance(data, list):
            return self.handle_list_data(data, data_mapping)
        if isinstance(data, dict):
            return self.map_json_fields(data, data_mapping)
        return data

    def handle_list_data(self, data, data_mapping):
        if data:
            if isinstance(data[0], dict):
                result = []
                for item in data:
                    # Convert the dictionary to a frozenset for comparison
                    item_frozen = frozenset(item.items())
                    if not any(frozenset(existing_item.items()) == item_frozen for existing_item in result):
                        result.append(self.map_json_fields(item, data_mapping))
                return result
        return data

    def map_json_fields(self, data, mapping):
        mapped_data = {}
        if not mapping:
            return data
        for key, path in mapping.items():
            if path in data:
                mapped_data[key] = data[path]
            else:
                mapped_data[key] = self.get_nested_value(data, path)
        return mapped_data

    def get_nested_value(self, data, path):
        keys = path.split(".") if path else []
        for key in keys:
            if isinstance(data, list):
                try:
                    key = int(key)
                    data = data[key] if 0 <= key < len(data) else None
                except ValueError:
                    return None
                except IndexError:
                    return None
            elif isinstance(data, dict):
                data = data.get(key)
            else:
                return None
        return data

class FeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = Feature
        fields = ['key', 'name', 'description', 'is_active']

class CompanyFeatureSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyFeature
        fields = ['id', 'feature', 'enabled']

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.feature:
            representation['feature'] = FeatureSerializer(instance.feature).data

        return representation

class StoclApprovalSerializer(serializers.ModelSerializer):
    class Meta:
        model = StockApproval
        fields = '__all__'

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.warehouse_variant:
            representation['warehouse_variant'] = WarehouseVariantSerializer(instance.warehouse_variant).data

        if instance.user:
            representation['user'] = UserSerializer(instance.user).data

        return representation

class WarehouseLogsSerializer(serializers.ModelSerializer):
    class Meta:
        model = WarehouseTracking
        fields = '__all__'

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.warehouse_variant:
            representation['warehouse_variant'] = WarehouseVariantSerializer(instance.warehouse_variant).data
        
        if instance.order:
            representation['order'] = OrderSerializer(instance.order).data

        return representation
class ErrorLogsSerializer(serializers.ModelSerializer):
    class Meta:
        model = ErrorLogs
        fields = ['id', 'end_point', 'request_body', 'response_body', 'status_code', 'created_at', 'company']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        for field in ['request_body', 'response_body']:
            if isinstance(representation.get(field), str):
                try:
                    representation[field] = str(json.loads(representation[field]))
                except json.JSONDecodeError:
                    pass

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data

        return representation

class ConnectErrorDataSerializer(serializers.ModelSerializer):
    message = serializers.SerializerMethodField()
    what_to_do = serializers.SerializerMethodField()

    class Meta:
        model = ConnectErrorData
        fields = ['id', 'title', 'message', 'what_to_do', 'status']

    def get_message(self, obj):
        return _(obj.message)
    
    def get_what_to_do(self, obj):
        return _(obj.what_to_do)
    
class WoocommerceInformationSerializer(serializers.ModelSerializer):
    class Meta:
        model = WoocommerceInformation
        fields = [ 'consumer_key', 'consumer_secret', 'woocommerce_url']

class CompanyConfSerializer(serializers.ModelSerializer):
    default_connection = ConnectDeliveryCompanySerializer(read_only=True)
    default_delivery_company = DeliveryCompanySerializer(read_only=True)
    default_country = CountrySerializer(read_only=True)
    default_area = AreaSerializer(read_only=True)
    default_category = CategorySerializer(read_only=True)
    default_warehouse = WarehouseSerializer(read_only=True)
    deduct_virtual_quantity = StatusSerializer(read_only=True)
    deduct_physical_quantity = StatusSerializer(read_only=True)
    delivery_request_status = StatusSerializer(read_only=True)
    providers = ProviderSerializer(many=True, read_only=True)
    additional_countries = CountrySerializer(many=True, read_only=True)
    delayed_order_statuses = StatusMapSerializer(many=True, read_only=True)
    default_pricelist = PricelistSerializer(read_only=True)

    order_form_fields = serializers.SerializerMethodField()
    order_waybill_templates = serializers.SerializerMethodField()
    order_mobile_card_fields = serializers.SerializerMethodField()
    number_of_warehouses = serializers.SerializerMethodField()

    class Meta:
        model = CompanyConf
        fields = '__all__'

    def get_order_form_fields(self, obj):
        return obj.order_form_fields.split(",") if obj.order_form_fields else []

    def get_order_waybill_templates(self, obj):
        return obj.order_waybill_templates.split(",") if obj.order_waybill_templates else []

    def get_order_mobile_card_fields(self, obj):
        return obj.order_mobile_card_fields.split(",") if obj.order_mobile_card_fields else []
    
    def get_number_of_warehouses(self, obj):
        return Warehouse.objects.filter(company=obj.company).count()
    
class FinancialReconciliationSerializer(serializers.ModelSerializer):
    class Meta:
        model = FinancialReconciliation
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return representation
    
class OrderDeliveryStatusHistorySerializer(serializers.ModelSerializer):
    order = OrderSerializer()
    status = StatusMapSerializer()
    class Meta:
        model = OrderDeliveryStatusHistory
        fields = "__all__"

class BillingLogSerializer(serializers.ModelSerializer):
    company = CompanySerializer(read_only=True)
    class Meta:
        model = BillingLog
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return representation

class StuckReasonSerializer(serializers.ModelSerializer):
    class Meta:
        model = StuckReason
        fields = "__all__"

class ResellerSerializer(serializers.ModelSerializer):
    class Meta:
        model = Reseller
        fields = [
            'id', 'mobile_number', 'name', 'is_active', 'is_delivery_company', 'created_at', 'updated_at',
            'national_id_url', 'certificate_url', 'company_logo_url', 'address', 'api_documentation', 
            'country', 'area', 'country_code', 'pricelist_items', 'branches', 'wallet', 'limit','company_registry',
            'companies', 'is_approved', 'update_pending', 'delivery_company', 'last_pickup_time', 'delivery_daily','is_aggregator'
        ]
    
    def calculate_profile_completeness(self, instance, is_delivery_company):
        if is_delivery_company:
            fields = [
                'mobile_number', 'name', 'certificate_url', 'company_logo_url', 'address', 
                'api_documentation', 'country', 'area', 'country_code', 'last_pickup_time'
            ]
        else:
            fields = [
                'id', 'mobile_number', 'name', 'national_id_url', 'address', 'country', 'area', 'country_code'
            ]
        special_fields = ['pricelist_items', 'branches']
        completed_fields = 0
        total_fields = len(fields) + len(special_fields) if is_delivery_company else len(fields)
        for field in fields:
            value = getattr(instance, field, None)
            if value:
                completed_fields += 1
        if is_delivery_company:
            if hasattr(instance, 'pricelist_items'):
                pricelist_items = instance.pricelist_items.all()
                if pricelist_items.exists():
                    valid_pricelist_items = pricelist_items.filter(price__gt=0).count()
                    if valid_pricelist_items == pricelist_items.count():
                        completed_fields += 1

            if hasattr(instance, 'branches'):
                branches = instance.branches.all()
                if branches.exists():
                    completed_fields += 1

        completeness_percentage = (completed_fields / total_fields) * 100
        return round(completeness_percentage, 2)

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data

        if instance.area:
            representation['area'] = AreaSerializer(instance.area).data

        if instance.pricelist_items:
            representation['pricelist_items'] = ResellerPricelistItemSerializer(instance.pricelist_items.all(), many=True).data

        if instance.branches:
            representation['branches'] = DeliveryCompanyBranchSerializer(instance.branches.all(), many=True).data

        if instance.wallet:
            representation['wallet'] = ResellerWalletSerializer(instance.wallet).data

        if instance.companies:
            representation['companies'] = instance.companies.count()
        else:
            representation['companies'] = 0

        if instance.delivery_company:
            representation['delivery_company'] = DeliveryCompanySerializer(instance.delivery_company).data

        representation['profile_completeness'] = self.calculate_profile_completeness(instance, instance.is_delivery_company)

        return representation
    
class ResellerPricelistItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResellerPricelistItem
        fields = ['id', 'reseller', 'from_area', 'to_area', 'price']
    
class ResellerPricelistTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResellerPricelistTemplate
        fields = ['id', 'from_area', 'to_area', 'country']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.country:
            representation['country'] = CountrySerializer(instance.country).data

        return representation
    
class DeliveryCompanyBranchSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeliveryCompanyBranch
        fields = ['id', 'reseller', 'name']
    
class ResellerWalletSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResellerWallet
        fields = ['id', 'reseller', 'credit', 'debit']

class ResellerUpdateFieldSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResellerUpdateField
        fields = ['id', 'update', 'field_name', 'old_value', 'new_value', 'new_value_id']

class ResellerPricelistItemUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResellerPricelistItemUpdate
        fields = ['id', 'from_area', 'to_area', 'country', 'price']


class DeliveryCompanyBranchUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeliveryCompanyBranchUpdate
        fields = ['id', 'name']


class ResellerUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResellerUpdate
        fields = ['id', 'reseller', 'created_at', 'updated_at', 'status', 'fields', 'pricelist_items_updates', 'branch_updates']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.fields:
            representation['fields'] = ResellerUpdateFieldSerializer(instance.fields.all(), many=True).data

        if instance.pricelist_items_updates:
            representation['pricelist_items_updates'] = ResellerPricelistItemUpdateSerializer(instance.pricelist_items_updates, many=True).data

        if instance.branch_updates:
            representation['branch_updates'] = DeliveryCompanyBranchUpdateSerializer(instance.branch_updates, many=True).data

        return representation
    
class ResellerCompanySerializer(serializers.ModelSerializer):
    status = serializers.SerializerMethodField()
    class Meta:
        model = ResellerCompany
        fields = ['id', 'reseller', 'company', 'mobile_number', 'status', 'name', 'status_code']

    def get_status(self, obj):
        if obj.status:
            return _(obj.status)
        else:
            return ''

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data
        if instance.reseller:
            representation['reseller'] = ResellerSerializer(instance.reseller).data

        representation['delivery_companies'] = CompanyDeliveryCompanySerializer(CompanyDeliveryCompany.objects.filter(company=instance.company), many=True).data

        return representation
    
class CompanyDeliveryCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyDeliveryCompany
        fields = ['id', 'delivery_company', 'company']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.delivery_company:
            representation['delivery_company'] = DeliveryCompanySerializer(instance.delivery_company).data

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data

        return representation
    
class ResellerDebitPricingSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResellerDebitPricing
        fields = ['id', 'ps_price', 'jo_price']

class CompanyResellerWarehouseSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyResellerWarehouse
        fields = ['id', 'warehouse', 'company']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.warehouse:
            representation['warehouse'] = WarehouseSerializer(instance.warehouse).data

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data

        return representation


class CompanyWarehouseConnectionSerializer(serializers.ModelSerializer):
    class Meta:
        model = CompanyWarehouseConnection
        fields = ['id', 'warehouse', 'company', 'connection_status']

    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.warehouse:
            representation['warehouse'] = WarehouseSerializer(instance.warehouse).data

        if instance.company:
            representation['company'] = CompanySerializer(instance.company).data

        return representation


class StocktakingProductsSerializer(serializers.ModelSerializer):
    class Meta:
        model = StocktakingProducts
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.warehouse_variant is not None:  
            representation['warehouse_variant'] = WarehouseVariantSerializer(instance.warehouse_variant).data
        return representation
class StocktakingSerializer(serializers.ModelSerializer):
    class Meta:
        model = Stocktaking
        fields = "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.stocktaking_Products is not None:  
            representation['stocktaking_Products'] = StocktakingProductsSerializer(instance.stocktaking_Products.all(), many=True).data
        return representation

class LocationProductSerializer(serializers.ModelSerializer):
    product = WarehouseVariantSerializer(read_only=True)

    class Meta:
        model = LocationProduct
        fields = "__all__"
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        return representation

class LocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Location
        fields = "__all__"
    
    def to_representation(self, instance):
        if self.context.get('use_location_with_product_serializer'):
            return LocationWithProductsSerializer(instance).data
        representation = super().to_representation(instance)
        if instance.parent is not None:
            representation['parent'] = LocationSerializer(instance.parent).data
        return representation


class StoreDomainMappingSerializer(serializers.ModelSerializer):
    class Meta:
        model = StoreDomainMapping
        fields = "__all__"
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)

        if instance.mystore:
            representation['mystore'] = MyStoreSerializer(instance.mystore).data

        return representation


class LocationWithProductsSerializer(serializers.ModelSerializer):
    products = serializers.SerializerMethodField()
    class Meta:
        model = Location
        fields = "__all__"

    def get_products(self, instance):
        def get_all_child_and_location_itself(location):
            all_locations = []
            queue = [location]

            while queue:
                current = queue.pop(0)
                all_locations.append(current)
                children = list(current.children.all())
                queue.extend(children)

            return all_locations

        all_locations = get_all_child_and_location_itself(instance)

        location_products = LocationProduct.objects.filter(location__in=all_locations).select_related('product')

        product_quantities = defaultdict(int)
        product_instances = {}

        for location_product in location_products:
            product_id = location_product.product.id
            product_quantities[product_id] += location_product.quantity
            product_instances[product_id] = location_product.product

        result = [
            {
                "product": WarehouseVariantSerializer(product_instances[product_id]).data,
                "quantity": qty
            }
            for product_id, qty in product_quantities.items()
        ]

        return result
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if instance.parent is not None:
            representation['parent'] = LocationSerializer(instance.parent).data
        return representation
    
class FollowUpCommentSerializer(serializers.ModelSerializer):
    class Meta:
        model = FollowUpComment
        fields = "__all__"

class DeveloperConnectionWebhookSerializer(serializers.ModelSerializer):
    developer_connection = DeveloperConnectionSerializer(read_only=True)
    class Meta:
        model = DeveloperConnectionWebhook
        fields = "__all__"
        
class WebhookLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = WebhookLog
        fields = "__all__"
        
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation.pop('developer_connection', None)
        return representation

class IntegrationTemplateSerializer(serializers.ModelSerializer):
    class Meta:
        model = IntegrationTemplate
        fields = "__all__"

MODEL_SERIALIZER_MAP = {
    ProductsImage: ProductsImageSerializer,
    Product: ProductSerializer,
    Order: OrderSerializer,
    OrderType: OrderTypeSerializer,
    OrderLine: OrderLineSerializer,
    DeliveryCompany: DeliveryCompanySerializer,
    Area: AreaSerializer,
    SubArea: SubAreaSerializer,
    Country: CountrySerializer,
    Status: StatusSerializer,
    Pricelist: PricelistSerializer,
    PricelistItem: PricelistItemSerializer,
    OtpCodes: OtpCodesSerializer,
    OtpToken: OtpTokenSerializer,
    EcommerceToken: EcommerceTokenSerializer,
    Logs: LogsSerializer,
    LogsInfo: LogsInfoSerializer,
    TrackedFields: TrackedFieldsSerializer,
    ConnectDeliveryCompany: ConnectDeliveryCompanySerializer,
    ConnectionUser: ConnectionUserSerializer,
    Client: ClientSerializer,
    MyStore:MyStoreSerializer,
    BannerDetail:BannerDetailSerializer,
    Package: PackageSerializer,
    StripeProduct: StripeProductSerializer,
    Role: RoleSerializer,
    Company: CompanySerializer,
    Collection: CollectionSerializer,
    CollectionBank: CollectionBankSerializer,
    CollectionStatus: CollectionStatusSerializer,
    ProductPrice: ProductPriceSerializer,
    FailedOrder: FailedOrderSerializer,
    User: UserSerializer,
    Category:CategorySerializer,
    CompanyPlan: CompanyPlanSerializer,
    Subscription: SubscriptionSerializer,
    Payment: PaymentSerializer,
    NotificationSound: NotificationSoundSerializer,
    Notification: NotificationSerializer,
    AreaMap: AreaMapSerializer,
    SubAreaMap: SubAreaMapSerializer,
    SystemConfiguration: SystemConfigurationSerializer,
    ProductVariant: ProductVariantSerializer,
    Attribute: AttributeSerializer,
    AttributeValue: AttributeValueSerializer,
    Permission: PermissionSerializer,
    Warehouse: WarehouseSerializer,
    WarehouseVariant: WarehouseVariantSerializer,
    VariantAttribute: VariantAttributeSerializer,
    User: UserSerializer,
    BranchPage: BranchPageSerializer,
    Ticket: TicketSerializer,
    ReleaseDocument: ReleaseDocumentSerializer,
    IntegrationLogs: IntegrationLogsSerializer,
    UserPreferences: UserPreferencesSerializer,
    ResellerUserPreferences: ResellerUserPreferencesSerializer,
    NotificationBanner: NotificationBannerSerializer,
    StoreUser:StoreUserSerializer,
    StoreSection:StoreSectionSerializer,
    StoreBillingAddress:StoreBillingAddressSerialaizer,
    ConnectionToken: ConnectionTokenSerializer,
    CompanyWallet: CompanyWalletSerializer,
    ClientPayment: ClientPaymentSerializer,
    Provider: ProviderSerializer,
    NotificationCenter:NotificationCenterSerializer,
    DeliveryStatusMap: DeliveryStatusMapSerializer,
    StatusMap: StatusMapSerializer,
    LimitUpgrade: LimitUpgradeSerializer,
    DeveloperConnection: DeveloperConnectionSerializer,
    CompanyFeature: CompanyFeatureSerializer,
    StockApproval: StoclApprovalSerializer,
    WarehouseTracking: WarehouseLogsSerializer,
    ConnectErrorData: ConnectErrorDataSerializer,
    ErrorLogs: ErrorLogsSerializer,
    RequestTemplate: RequestTemplateSerializer,
    ResponseTemplate: ResponseTemplateSerializer,
    WoocommerceInformation:WoocommerceInformationSerializer,
    FinancialReconciliation:FinancialReconciliationSerializer,
    CompanyConf: CompanyConfSerializer,
    OrderDeliveryStatusHistory: OrderDeliveryStatusHistorySerializer,
    BillingLog: BillingLogSerializer,
    StuckReason: StuckReasonSerializer,
    Reseller: ResellerSerializer,
    ResellerPricelistItem: ResellerPricelistItemSerializer,
    ResellerPricelistTemplate: ResellerPricelistTemplateSerializer,
    DeliveryCompanyBranch: DeliveryCompanyBranchSerializer,
    ResellerWallet: ResellerWalletSerializer,
    ResellerUpdateField: ResellerUpdateFieldSerializer,
    ResellerUpdate: ResellerUpdateSerializer,
    ResellerCompany: ResellerCompanySerializer,
    CompanyDeliveryCompany: CompanyDeliveryCompanySerializer,
    ResellerDebitPricing: ResellerDebitPricingSerializer,
    ResellerPricelistItemUpdate: ResellerPricelistItemUpdateSerializer,
    DeliveryCompanyBranchUpdate: DeliveryCompanyBranchUpdateSerializer,
    CompanyWarehouseConnection: CompanyWarehouseConnectionSerializer,
    CompanyResellerWarehouse: CompanyResellerWarehouseSerializer,
    Stocktaking: StocktakingSerializer,
    StocktakingProducts: StocktakingProductsSerializer,
    Location: LocationSerializer,
    FollowUpComment: FollowUpCommentSerializer,
    LocationProduct:LocationProductSerializer,
    StoreDomainMapping:StoreDomainMappingSerializer,
    DeveloperConnectionWebhook: DeveloperConnectionWebhookSerializer,
    WebhookLog: WebhookLogSerializer,
    IntegrationTemplate: IntegrationTemplateSerializer,
}