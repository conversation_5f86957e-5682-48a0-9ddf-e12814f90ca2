from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from ..util_functions import get_user_from_token
from django.views.decorators.csrf import csrf_exempt
from django.core.paginator import Paginator
from django.db.models import Q
from .client_model import *
from ..serializers import *
from rest_framework.decorators import *
from ..permissions import *
from ..error.error_model import *
from ..util_functions import *
from django.utils.translation import gettext as _

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_client'])])
def check_duplicate_in_order(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    mobile_number = json_data.get('mobile_number')
    one_day_before_now = timezone.now() - datetime.timedelta(days=1)

    orders = Order.objects.filter(customer_mobile=mobile_number, created_at__gte=one_day_before_now, company=user.company, status__code__in=['new_order', 'preparing', 'ready_for_delivery'])

    if orders.exists():
        names = list(set(order.created_by for order in orders))
        return JsonResponse({
                'success': True,
                'has_duplicates': True,
                'no_of_duplicates': orders.count() if orders else 0,
                'message': _(
                    '%(count)s orders for this client already exist and were created by %(names)s'
                ) % {'count': orders.count(), 'names': ', '.join(names)}
            }, status=200)
    
    return JsonResponse({'success': True, 'has_duplicates': False, 'message': _('No orders found for this client')}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_client'])])
def get_my_clients(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    if user_has_group_permission(request.user, 'view_self_client_order'):
        mobile_numbers = Order.objects.filter(company=user.company, created_by_user=user).values_list('customer_mobile', flat=True)
    else:
        mobile_numbers = Order.objects.filter(company=user.company).values_list('customer_mobile', flat=True)
        
    response = get_records('Client', json_data, user, company_filter=Q(mobile_number__in=mobile_numbers))
    return JsonResponse(response, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_client'])])
def get_clients(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response = get_records('Client', json_data, False)
    return JsonResponse(response, status=200)

