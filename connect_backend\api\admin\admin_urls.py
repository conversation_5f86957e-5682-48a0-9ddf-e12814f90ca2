from django.urls import path,include
from .admin_apis import *

urlpatterns = [
    path('delete_user', delete_user, name='delete_user'),
    path('create_package_in_stripe', create_package_in_stripe, name='create_package_in_stripe'),
    path('update_package_in_stripe', update_package_in_stripe, name='update_package_in_stripe'),
    path('get_packages', get_packages, name='get_packages'),
    path('send_unsynced_orders', send_unsynced_orders, name='send_unsynced_orders'),
    path('activate_account', activate_account, name='activate_account'),
    path('get_users', get_users, name='get_users'),
    path('admin_token', admin_token, name='admin_token'),
    path('admin_extend_token', admin_extend_token, name='admin_extend_token'),
    path('add_area', add_area, name='add_area'),
    path('update_area', update_area, name='update_area'),
    path('delete_area', delete_area, name='delete_area'),
    path('add_sub_area', add_sub_area, name='add_sub_area'),
    path('update_sub_area', update_sub_area, name='update_sub_area'),
    path('delete_sub_area', delete_sub_area, name='delete_sub_area'),
    path('update_status_map', update_status_map, name='update_status_map'),
    path('sync_delivery_companies', sync_delivery_companies, name='sync_delivery_companies'),
    path('get_delivery_companies', get_delivery_companies, name='get_delivery_companies'),
    path('impersonate_user', impersonate_user, name='impersonate_user'),
    path('get_waiting_users', get_waiting_users, name='get_waiting_users'),
    path('test_send_notification', test_send_notification, name='test_send_notification'),
    path('get_notifications', get_notifications, name='get_notifications'),
    path('get_sounds', get_sounds, name='get_sounds'),
    path('set_notification_sounds', set_notification_sounds, name='set_notification_sounds'),
    path('get_collections', get_collections, name='get_collections'),
    path('update_collection_status', update_collection_status, name='update_collection_status'),
    path('sync_delivery_areas', sync_delivery_areas, name='sync_delivery_areas'),
    path('get_delivery_company_areas', get_delivery_company_areas, name='get_delivery_company_areas'),
    path('get_delivery_company_sub_areas', get_delivery_company_sub_areas, name='get_delivery_company_sub_areas'),
    path('sync_delivery_sub_areas', sync_delivery_sub_areas, name='sync_delivery_sub_areas'),
    path('get_area_map', get_area_map, name='get_area_map'),
    path('get_sub_area_map', get_sub_area_map, name='get_sub_area_map'),
    path('auto_map_areas', auto_map_areas, name='auto_map_areas'),
    path('get_sub_areas', get_sub_areas, name='get_sub_areas'),
    path('get_failed_orders', get_failed_orders, name='get_failed_orders'),
    path('test_fuzzy', test_fuzzy, name='test_fuzzy'),
    path('set_system_configuration', set_system_configuration, name='set_system_configuration'),
    path('get_companies', get_companies, name='get_companies'),
    path('get_subscription', get_subscription, name='get_subscription'),
    path('update_subscription', update_subscription, name='update_subscription'),
    path('get_permissions', get_permissions, name='get_permissions'),
    path('set_role_permissions', set_role_permissions, name='set_role_permissions'),
    path('set_role', set_role, name='set_role'),
    path('get_roles', get_roles, name='get_roles'),
    path('add_attribute', add_attribute, name='add_attribute'),
    path('update_attribute', update_attribute, name='update_attribute'),
    path('get_integration_logs', get_integration_logs, name='get_integration_logs'),
    path('add_notification_banner', add_notification_banner, name='add_notification_banner'),
    path('update_notification_banner', update_notification_banner, name='update_notification_banner'),
    path('get_notification_banners', get_notification_banners, name='get_notification_banners'),
    path('send_notification_to_users', send_notification_to_users, name='send_notification_to_users'),
    path('update_company_wallet', update_company_wallet, name='update_company_wallet'),
    path('get_company_wallet', get_company_wallet, name='get_company_wallet'),
    path('map_statuses', map_statuses, name='map_statuses'),
    path('get_delivery_company_statuses', get_delivery_company_statuses, name='get_delivery_company_statuses'),
    path('get_delivery_status_map', get_delivery_status_map, name='get_delivery_status_map'),
    path('auto_map_statuses', auto_map_statuses, name='auto_map_statuses'),
    path('update_variant_names', update_variant_names, name='update_variant_names'),
    path('add_limit_upgrade', add_limit_upgrade, name='add_limit_upgrade'),
    path('update_limit_upgrade', update_limit_upgrade, name='update_limit_upgrade'),
    path('get_limit_upgrades', get_limit_upgrades, name='get_limit_upgrades'),
    path('update_company', update_company, name='update_company'),
    path('get_error_logs', get_error_logs, name='get_error_logs'),
    path('get_connect_errors', get_connect_errors, name='get_connect_errors'),
    path('sync_connect_errors', sync_connect_errors, name='sync_connect_errors'),
    path('get_users_otp',get_users_otp, name='get_users_otp'),
    path('get_areas',get_areas, name='get_areas'),
    path('',include('api.admin.admin_dashboard.admin_dashboard_urls')),
    path('get_request_templates', get_request_templates, name='get_request_templates'),
    path('get_response_templates', get_response_templates, name='get_response_templates'),
    path('build_dynamic_request', build_dynamic_request, name='build_dynamic_request'),
    path('build_dynamic_response', build_dynamic_response, name='build_dynamic_response'),
    path('create_request_template', create_request_template, name='create_request_template'),
    path('update_request_template', update_request_template, name='update_request_template'),
    path('create_response_template', create_response_template, name='create_response_template'),
    path('update_response_template', update_response_template, name='update_response_template'),
    path('get_delivery_company_records', get_delivery_company_records, name='get_delivery_company_records'),
    path('add_delivery_company', add_delivery_company, name='add_delivery_company'),
    path('update_delivery_company', update_delivery_company, name='update_delivery_company'),
    path('get_optimus_areas', get_optimus_areas, name='get_optimus_areas'),
    path('get_logestechs_statuses', get_logestechs_statuses, name='get_logestechs_statuses'),
    path('test_fuzzy_rate', test_fuzzy_rate, name='test_fuzzy_rate'),
    path('get_billing_logs', get_billing_logs, name='get_billing_logs'),
    path('get_resellers', get_resellers, name='get_resellers'),
    path('activate_reseller', activate_reseller, name='activate_reseller'),
    path('deactivate_reseller', deactivate_reseller, name='deactivate_reseller'),
    path('impersonate_reseller', impersonate_reseller, name='impersonate_reseller'),
    path('add_reseller_pricelist_item', add_reseller_pricelist_item, name='add_reseller_pricelist_item'),
    path('update_reseller_pricelist_item', update_reseller_pricelist_item, name='update_reseller_pricelist_item'),
    path('delete_reseller_pricelist_item', delete_reseller_pricelist_item, name='delete_reseller_pricelist_item'),
    path('approve_reseller_update', approve_reseller_update, name='approve_reseller_update'),
    path('update_reseller_limit', update_reseller_limit, name='update_reseller_limit'),
    path('get_company_reseller', get_company_reseller, name='get_company_reseller'),
    path('set_business_delivery_companies', set_business_delivery_companies, name='set_business_delivery_companies'),
    path('get_company_delivery_company', get_company_delivery_company, name='get_company_delivery_company'),
    path('update_reseller_wallet', update_reseller_wallet, name='update_reseller_wallet'),
    path('get_debit_pricing', get_debit_pricing, name='get_debit_pricing'),
    path('update_debit_pricing', update_debit_pricing, name='update_debit_pricing'),
    path('approve_reseller', approve_reseller, name='approve_reseller'),
    path('get_reseller_companies', get_reseller_companies, name='get_reseller_companies'),
    path('update_reseller_company', update_reseller_company, name='update_reseller_company'),
    path('add_reseller_company', add_reseller_company, name='add_reseller_company'),
    path('get_reseller_update', get_reseller_update, name='get_reseller_update'),
    path('generate_reseller_token', generate_reseller_token, name='generate_reseller_token'),
    path('set_to_aggregator', set_to_aggregator, name='set_to_aggregator'),
    path('tickets/',include('api.admin.admin_tickets.admin_tickets_urls')),
    path('store-domain-mapping/',include('api.admin.store_domain_mapping.store_domain_mapping_urls')),
    path('add_follow_up_comment', add_follow_up_comment, name='add_follow_up_comment'),
    path('get_follow_up_comment', get_follow_up_comment, name='get_follow_up_comment'),
    path('get_on_boarding_filters', get_on_boarding_filters, name='get_on_boarding_filters'),
    path('add_integration_template', add_integration_template, name='add_integration_template'),
    path('get_integration_templates', get_integration_templates, name='get_integration_templates'),
    path('update_integration_template', update_integration_template, name='update_integration_template'),
    path('reapply_template', reapply_template, name='reapply_template'),
]