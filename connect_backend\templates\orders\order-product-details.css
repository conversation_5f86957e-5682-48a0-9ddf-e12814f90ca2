body,
html {
    font-size: 7.5pt;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.right-header {
  text-align: right;
}

[dir="rtl"]
.right-header {
  text-align: left;
}

table {
  width: 100%;
  border-collapse: collapse;
}

.border, .table-head {
  border: 1px solid #dddddd;
  padding: 4px;
}

.table-head {
  padding: 1rem .5rem;
  font-size: 1rem;
  background: #97c15d;
}

tr:nth-child(even) {
  background-color: #eee;
}

.product-image-container {
  width: 4rem;
  padding-right: 1rem;
}

.product-image {
  max-height: 2.5rem;
  max-width: 4rem;
  border-radius: .25rem;
  padding-left: 0rem;
}

[dir="rtl"]
.product-image {
  padding-right: 0rem;
  padding-left: 1rem;
}

.product-name-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.product-note{
  width: 50%;
  overflow: hidden;
}

tr.space-under>td{
  padding: 1em;
}
