from .logs_model import *

def set_order_tracked_fields(company):
    for field in ["customer_name", "customer_mobile", "country_code", "address", "area", "sub_area", "country", "channel", "product_info", "total_cod", "note", "product_info", "status", "branch_page"]:
        TrackedFields.objects.create(company=company, model='Order', field_name=field)

def set_product_tracked_fields(company):
    for field in ["name", "price", "description", "cost", "product_reference"]:
        TrackedFields.objects.create(company=company, model='Product', field_name=field)

def set_order_line_tracked_fields(company):
    for field in ["product", "quantity", "price"]:
        TrackedFields.objects.create(company=company, model='OrderLine', field_name=field)

def set_user_tracked_fields(company):
    for field in ["mobile_number", "country_code", "address", "area", "sub_area", "country", "user_image_url", "product_info", "email", "role", "is_active"]:
        TrackedFields.objects.create(company=company, model='User', field_name=field)

def set_company_tracked_fields(company):
    for field in ["company_name", "company_mobile", "company_area"]:
        TrackedFields.objects.create(company=company, model='Company', field_name=field)

def set_company_conf_tracked_fields(company):
    for field in ["default_delivery_company", "auto_send_orders", "auto_send_on_status", "default_country_code", "default_country", "default_area"]:
        TrackedFields.objects.create(company=company, model='CompanyConf', field_name=field)

def set_pricelist_tracked_fields(company):
    for field in ["name", "code"]:
        TrackedFields.objects.create(company=company, model='Pricelist', field_name=field)

def set_pricelist_item_tracked_fields(company):
    for field in ["from_area", "to_area", "price"]:
        TrackedFields.objects.create(company=company, model='PricelistItem', field_name=field)

def set_area_tracked_fields(company):
    for field in ["country", "name", "code"]:
        TrackedFields.objects.create(company=company, model='Area', field_name=field)

def set_sub_area_tracked_fields(company):
    for field in ["area", "name", "code"]:
        TrackedFields.objects.create(company=company, model='SubArea', field_name=field)

def set_status_map_tracked_fields(company):
    for field in ["status", "delivery_company_status"]:
        TrackedFields.objects.create(company=company, model='StatusMap', field_name=field)

def set_developer_connection_tracked_fields(company):
    for field in ["name", "description"]:
        TrackedFields.objects.create(company=company, model='DeveloperConnection', field_name=field)

def set_default_tracked_fields(company):
    set_order_tracked_fields(company)
    set_user_tracked_fields(company)
    set_company_tracked_fields(company)
    set_company_conf_tracked_fields(company)
    set_product_tracked_fields(company)
    set_order_line_tracked_fields(company)
    set_pricelist_tracked_fields(company)
    set_pricelist_item_tracked_fields(company)
    set_developer_connection_tracked_fields(company)


    