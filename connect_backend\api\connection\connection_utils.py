from ..users.user_model import Company
from ..delivery_company.delivery_company_model import *
from ..util_functions import *
from ..orders.order_utils import *
from django.http import JsonResponse
from ..notification.notification_utils import *
from ..error.error_model import *
from django.utils.translation import gettext as _
import os

def get_company_delivery_company_area(delivery_company_id, area_id):
    try:
        delivery_company = DeliveryCompany.objects.get(id=delivery_company_id)
        area = Area.objects.get(id=area_id)
        return delivery_company, area
    except DeliveryCompany.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'DeliveryCompany', 'field': 'id', 'value': delivery_company_id})
    except Area.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Area', 'field': 'id', 'value': area_id})
    
def get_company_and_delivery_company(params):
    company_id = params['company_id']
    delivery_company_id = params['delivery_company_id']
    
    try:
        company = Company.objects.get(company_id=company_id)
        delivery_company = DeliveryCompany.objects.get(id=delivery_company_id)
    except Company.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Company', 'field': 'company_id', 'value': company_id})
    except DeliveryCompany.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'DeliveryCompany', 'field': 'id', 'value': delivery_company_id})
    
    return company, delivery_company, params['orders_data']

def process_order_update(connect_reference_id, vals, company, delivery_company, orders_for_notification):
    print('================================================================')
    print(f'order_data: {vals}')
    print(f'order: {connect_reference_id}')
    try:
        delivery_request_statuses_list = get_delivery_request_statuses_list()
        order = Order.objects.get(delivery_company=delivery_company, company=company, order_sequence=connect_reference_id, status__in=delivery_request_statuses_list)
        log = create_log('Order', order.company, 'update', delivery_company.name, order.id)
        order.updated_by = delivery_company.name
        print(f'company: {order.company.company_name}')
        update_order_vhub_fields(order, vals, log)
        order.save()
        if order.channel.lower() == 'woocommerce' and order.order_reference:
            try:
                send_order = send_delivery_status_to_woocommerce(order.order_reference, order.delivery_company_status, order.olivery_sequence, company)
            except:
                pass
        print(f'order delivery status: {order.delivery_company_status}')
        
        if vals.get('state', None):
            status_map = DeliveryStatusMap.objects.get(imported_status_code=vals.get('state'), delivery_company=order.delivery_company)
            status = status_map.status
            orders_for_notification.append({'order': order, 'vhub_vals': {'state': status.status_code}})
        
        delete_log_if_no_changes(log)
    
    except Order.DoesNotExist:
        pass
    except Exception as e:
        raise ValidationError(f"Error updating order: {str(e)}")

def update_order_vhub_fields(order, vhub_vals, log):
    field_mappings = {
        'note': 'Note',
        'stuck_comment': _('Stuck Comment'),
        'reject_reason': _('Stuck Comment'),
        'state': _('Delivery Status'),
        'customer_payment': _('Customer Payment'),
        'delivered_by': _('Delivered By')
    }

    for key, value in vhub_vals.items():
        print(f'key {key}: value {value}')
        if value and key in field_mappings:
            field_name = field_mappings[key]
            old_value = getattr(order, key, None)
            if key == 'state':
                old_value = getattr(order, 'delivery_company_status', None)
                status_map = DeliveryStatusMap.objects.get(imported_status_code=value, delivery_company=order.delivery_company)
                new_value = status_map.status.delivery_company_status
            else:
                new_value = value
            
            LogsInfo.objects.create(
                log=log,
                field_name=field_name,
                old_value=old_value,
                new_value=new_value
            )
            print(f'changed {key} from {old_value} => {new_value}')

    update_order_comments(order, vhub_vals)
    update_order_state(order, vhub_vals)

def update_order_comments(order, vhub_vals):
    order.note = vhub_vals.get('note', '') or order.note
    if not order.stuck_comment:
        order.stuck_comment = ''

    if vhub_vals.get('stuck_comment'):
        order.stuck_comment += f"{vhub_vals.get('stuck_comment')}, "
    
    if vhub_vals.get('reject_reason'):
        order.stuck_comment += f"{vhub_vals.get('reject_reason')}, "

    if vhub_vals.get('delivered_by'):
        order.delivered_by = vhub_vals.get('delivered_by')
        
def update_order_state(order, vhub_vals):
    if vhub_vals.get('state'):
        status_map = DeliveryStatusMap.objects.get(imported_status_code=vhub_vals['state'], delivery_company=order.delivery_company)
        order.delivery_company_status = status_map.status.delivery_company_status

def handle_status_change_notifications(orders_data,delivery_company):
    notification_data =  {
        'users': [],
        'sound': None,
        'title': '',
        'message': '',
        'data': {},
        'source':delivery_company.name
    }
    
    order_sequences = []
    vhub_status = None

    for order_data in orders_data:
        order = order_data.get('order')
        vhub_vals = order_data.get('vhub_vals', {})
        current_status = vhub_vals.get('state')

        if not current_status:
            continue

        vhub_status = current_status
        order_sequences.append(order.order_sequence)

        users = User.objects.filter(
            Q(company=order.company) &
            (
                Q(role__name__in=['Super Manager', 'Manager', 'Sales Manager']) |
                Q(name=order.created_by)
            )
        )
        
        notification_data['users'].extend(list(users))
        notification_data['users'] = list(set(notification_data['users']))
        
    if order_sequences and vhub_status:
        try:
            notification = Notification.objects.get(delivery_status=vhub_status)
            status_map = StatusMap.objects.get(status_code=vhub_status)
            status_message = status_map.delivery_company_status
            notification_data['sound'] = notification.sound

        except Notification.DoesNotExist:
            print(f"Notification not found for status: {vhub_status}")
            return
        except DeliveryStatusMap.DoesNotExist:
            print(f"StatusMap not found for status: {vhub_status}")
            return
        
        message = _('status of {num_of_orders} has been changed to {title} by {delivery_company}. Order sequences:{orders_sequences}')
        title = status_message
        notification_data['data'] = {
            "filters": [{
                "operator": "and",
                "filters": [{
                    "field": "order_sequence",
                    "operator": "in",
                    "value": order_sequences
                }]
            }],
            'order_count': len(order_sequences),
            'title': title
        }
        

        notification_data['message'] = message
        notification_data['title'] = title
        context = {'num_of_orders':len(order_sequences),'title':title,'orders_sequences':order_sequences, 'delivery_company': delivery_company.name}
        send_notification(notification_data,context=context)


def send_connection_request(connection, username, area_map, user):
    name = connection.name.replace(connection.delivery_company.name, '')
    if name and name.startswith('-'):
        name = name[1:]
    params = {
        "jsonrpc": "2.0",
        "params": {
            'values': {
                "username": f'{connection.company.company_name}-{name}' if name else connection.company.company_name,
                "mobile_number": username,
                "area_id": area_map.imported_area_id,
                'address': user.address
            },
            'source_company_url': os.getenv('BASEURL'),
            'company_id': connection.company.company_id,
            'connection_id': connection.id,
            'delivery_company_id': connection.delivery_company.id,
            'company_name': connection.company.company_name,
        }
    }
    return send_api_to_delivery_company(connection.delivery_company, '/create_connection_request', params)

def handle_connection_response(response, connection, company, delivery_company, area, user):
    result = response.get('result', {})
    code = result.get('code')
    message = result.get('message')
    status = result.get('status')
    user_url = result.get('business_user_link', '')

    if status and status == 'accepted':
        status = 'connected'
    
    if user_url:
        user_url = delivery_company.delivery_company_url + user_url

    if code == 200:
        update_or_create_connection(connection, company, delivery_company, area, status, user_url, user)

def update_or_create_connection(connection, company, delivery_company, area, status, user_url, user):
    if connection:
        connection.user_url = user_url
        connection.connection_status = status
        connection.area = area
        connection.save()
    else:
        ConnectDeliveryCompany.objects.create(
            company=company,
            delivery_company=delivery_company,
            connection_status=status,
            user_url=user_url,
            created_by=user.name,
            updated_by=user.name,
            area=area
        )

def get_connection_status(action):
    action_status_map = {
        'accept_request': 'connected',
        'reject_request': 'rejected',
        'cancel_request': 'cancelled'
    }
    return action_status_map.get(action, 'pending')

def check_required_fields(values):
    required_fields = ['company_username', 'company_password', 'pricelist', 'pricelist_items']
    missing_fields = [field for field in required_fields if not values.get(field)]
    return missing_fields

def handle_pricelist(values, connection):
    pricelist_data = values.get('pricelist')[0]
    pricelist_items = values.get('pricelist_items')
    
    pricelist_obj, created = Pricelist.objects.get_or_create(
        connection=connection,
        defaults={'name': pricelist_data.get('name'), 'code': pricelist_data.get('code')}
    )
    if not created:
        pricelist_obj.name = pricelist_data.get('name')
        pricelist_obj.code = pricelist_data.get('code')
        pricelist_obj.save()

    PricelistItem.objects.filter(pricelist=pricelist_obj.id).delete()

    for pricelist_item in pricelist_items:
        from_area_id = pricelist_item['from_area']
        to_area_id = pricelist_item['to_area']
        order_type = pricelist_item['order_type']
        price = pricelist_item['price']
        
        from_area_mapped = map_imported_area_to_system(from_area_id, connection.delivery_company)
        to_area_mapped = map_imported_area_to_system(to_area_id, connection.delivery_company)

        create_mapped_pricelist_items(pricelist_obj, from_area_mapped, to_area_mapped, order_type, price)

def map_imported_area_to_system(imported_area_id, delivery_company):
    try:
        areas = AreaMap.objects.filter(imported_area_id=imported_area_id, delivery_company=delivery_company).values_list('area', flat=True)
        return areas
    except AreaMap.DoesNotExist:
        raise Exception(f'No system areas found for imported area: {imported_area_id}')

def create_mapped_pricelist_items(pricelist, from_areas, to_areas, order_type, price):
    for from_area in from_areas:
        if isinstance(from_area, int):
            try:
                from_area = Area.objects.get(id=from_area)
            except Area.DoesNotExist:
                raise Exception(f'No system area found for imported area: {from_area}')
        for to_area in to_areas:
            if isinstance(to_area, int):
                try:
                    to_area = Area.objects.get(id=to_area)
                except Area.DoesNotExist:
                    raise Exception(f'No system area found for imported area: {from_area}')
            PricelistItem.objects.create(
                pricelist=pricelist, from_area=from_area, to_area=to_area, order_type=order_type, price=price
            )

def send_cancel_request(connection, user):
    params = {
        "jsonrpc": "2.0",
        "params": {
            'source_company_url': os.getenv('BASEURL'),
            'company_id': connection.company.company_id,
            'delivery_company_id': connection.delivery_company.id,
            'connection_id': connection.id,
            'company_name': connection.company.company_name,
            'connect_request_flag': True,
            "lang": 'ar_SY' if translation.get_language() == 'ar' else 'en_US'
        }
    }
    response = send_api_to_delivery_company(connection.delivery_company, '/cancel_connection_request', params)
    create_log('ConnectionDeliveryCompany', connection.company, 'Cancel connection request', user.name, connection.id)
    return response

def sync_pricelist(connection, pricelist, pricelist_items):
    with transaction.atomic():
        pricelist_obj, created = Pricelist.objects.get_or_create(
            connection=connection,
            defaults={'name': pricelist[0].get('name'), 'code': pricelist[0].get('code')}
        )
        if not created:
            pricelist_obj.name = pricelist[0].get('name')
            pricelist_obj.code = pricelist[0].get('code')
            pricelist_obj.save()

        PricelistItem.objects.filter(pricelist=pricelist_obj).delete()

        for pricelist_item in pricelist_items:
            from_area_name = pricelist_item['from_area']
            to_area_name = pricelist_item['to_area']
            order_type = pricelist_item['order_type']
            price = pricelist_item['price']
            
            from_area_mapped = map_imported_area_to_system(from_area_name, connection.delivery_company)
            to_area_mapped = map_imported_area_to_system(to_area_name, connection.delivery_company)

            create_mapped_pricelist_items(pricelist_obj, from_area_mapped, to_area_mapped, order_type, price)
        return True
    
def group_orders_by_connection(json_data):
    driver_orders = []
    delivery_company_orders = {}

    for item in json_data:
        connection_id = item.get('connection','')
        driver_id = item.get('driver_id','')
        order_ids = item.get('order_ids', [])

        if driver_id:
            driver_orders.append((driver_id, order_ids))
        elif connection_id:
            if connection_id not in delivery_company_orders:
                delivery_company_orders[connection_id] = []
            delivery_company_orders[connection_id].extend(order_ids)

    return driver_orders, delivery_company_orders

def process_sending_orders(order_list, user, delivery_request_status, success_messages, fail_messages):

    driver_orders, delivery_company_orders = group_orders_by_connection(order_list)
    driver_success, driver_fail = process_driver_orders_wrapper(driver_orders, user.name)
    delivery_success, delivery_fail = process_delivery_orders_wrapper(delivery_company_orders, user.name, delivery_request_status)
    success_messages.extend(driver_success or [])
    success_messages.extend(delivery_success or [])
    fail_messages.extend(driver_fail or [])
    fail_messages.extend(delivery_fail or [])

