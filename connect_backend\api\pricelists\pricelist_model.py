from django.db import models
from ..delivery_company.delivery_company_model import DeliveryCompany
from ..lookups.lookup_model import Area
from ..users.user_model import Company
from ..connection.connection_model import ConnectDeliveryCompany
from ..models import SuperModel

class Pricelist(SuperModel):
    name = models.CharField(max_length=200)
    code = models.CharField(max_length=200, null=True)
    delivery_company = models.ForeignKey(DeliveryCompany,on_delete=models.SET_NULL, null=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE,null=True)
    connection = models.ForeignKey(ConnectDeliveryCompany, on_delete=models.SET_NULL, null=True)

    def save(self, *args, **kwargs):
        if self.connection:
            self.delivery_company = self.connection.delivery_company
            self.company = self.connection.company
        super(Pricelist, self).save(*args, **kwargs)

class PricelistItem (SuperModel):
    from_area = models.ForeignKey(Area,on_delete=models.SET_NULL, null=True,related_name='from_pricelist_items')
    to_area = models.ForeignKey(Area,on_delete=models.SET_NULL, null=True,related_name='to_pricelist_items')
    order_type = models.CharField(max_length=200, choices=[('Normal', 'Normal'),('VIP','VIP')], default='Normal', null=True)
    price = models.FloatField(null=False)
    pricelist = models.ForeignKey(Pricelist, null=False, on_delete=models.CASCADE)