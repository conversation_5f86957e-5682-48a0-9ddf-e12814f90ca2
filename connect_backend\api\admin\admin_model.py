from django.db import models
from ..users.user_model import Company
from ..orders.order_model import Order
from ..models import SuperModel
from ..lookups.lookup_model import Country
from ..admin.admin_tickets.admin_ticket_model import ReleaseDocument

class FailedOrder(SuperModel):
    company = models.ForeignKey(Company, null=True, blank=True, on_delete=models.CASCADE)
    order = models.ForeignKey(Order, null=True, blank=True, on_delete=models.CASCADE)
    order_sequence = models.IntegerField(null=True, blank=True)
    error_message = models.CharField(max_length=200, null=True)
    job_status = models.CharField(max_length=200, null=True, blank=True)
    job_id = models.CharField(max_length=200, null=True)

class SystemConfiguration(models.Model):
    support_mobile_number = models.CharField(max_length=20, null=True, blank=True)
    business_drivers_limit = models.IntegerField(default=15, null=False)
    default_release_document = models.ForeignKey(ReleaseDocument, null=True, default=None, on_delete=models.SET_NULL)

class IntegrationLogs(models.Model):
    company = models.ForeignKey(Company, null=True, default=None, on_delete=models.SET_NULL)
    integration = models.CharField(max_length=200, null=True)
    request_body = models.TextField()
    response_body = models.TextField(null=True)
    status_code = models.IntegerField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    additional_information = models.TextField(null=True)

class ErrorLogs(models.Model):
    end_point = models.CharField(max_length=200)
    request_body = models.TextField()
    response_body = models.TextField()
    status_code = models.IntegerField()
    created_at = models.DateTimeField(auto_now_add=True)
    company = models.ForeignKey(Company, on_delete=models.SET_NULL, null=True, blank=True)

class ConnectErrorData(models.Model):
    title = models.CharField(max_length=200)
    message = models.TextField()
    what_to_do = models.TextField()
    status = models.IntegerField()

class ResellerPricelistTemplate(models.Model):
    from_area = models.CharField(max_length=255, null=True, blank=True)
    to_area = models.CharField(max_length=255, null=True, blank=True)
    country = models.ForeignKey(Country, null=True, blank=True, on_delete=models.SET_NULL)

class ResellerDebitPricing(models.Model):
    ps_price = models.FloatField(null=True, blank=True, default=1.0)
    jo_price = models.FloatField(null=True, blank=True, default=0.1)


class FollowUpComment(models.Model):
    message = models.TextField()
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, blank=True)
    created_by = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
