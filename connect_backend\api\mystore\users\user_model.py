from django.db import models
from ...models import SuperModel
from django.contrib.auth.models import User as DjUser
from ...users.user_model import Company
from ...lookups.lookup_model import *
from ..mystore_model import MyStore
from django.contrib.auth.models import AbstractBaseUser, BaseUserManager
from ...orders.order_model import *
from ...products.product_model import *

class StoreUserManager(BaseUserManager):
    def create_user(self, name, password=None, **extra_fields):
        if not name:
            raise ValueError("The Username field must be set")
        user = self.model(name=name, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

class StoreUser(AbstractBaseUser):
    mobile_number = models.CharField(max_length=15, unique=True)
    name = models.CharField(max_length=200)
    password = models.CharField(max_length=255)
    email = models.EmailField(null=True, blank=True)
    store = models.ForeignKey(MyStore, on_delete=models.CASCADE , null=False)
    company = models.ForeignKey(Company,on_delete=models.CASCADE, null=False)
    second_phone = models.CharField(max_length=15, null=True, blank=True)
    address = models.CharField(max_length=200)
    area = models.ForeignKey(Area,on_delete=models.CASCADE,null=True)
    country = models.ForeignKey(Country,on_delete=models.CASCADE,null=True)
    country_code = models.CharField(max_length=6)
    is_active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)
    created_by = models.CharField(max_length=200, default=None, null=True, editable=False)
    updated_by = models.CharField(max_length=200, default=None, null=True, editable=False)
    language  =  models.CharField(
        max_length=50,
        choices=[
            ("ar", "Arabic"),
            ("en", "English"),
        ],default='ar')
    objects = StoreUserManager()

    USERNAME_FIELD = 'mobile_number'
    REQUIRED_FIELDS = ['mobile_number']

    class Meta:
        unique_together = ('mobile_number', 'store')
    

class StoreUserFavorites(SuperModel):
    user = models.ForeignKey(StoreUser, on_delete=models.CASCADE, related_name='favorites')
    store = models.ForeignKey(MyStore, on_delete=models.CASCADE, related_name='user_favorites')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='favorited_by')

    class Meta:
        unique_together = ('user', 'product', 'store')  # Ensure unique combination of user, product, and store


class StoreBillingAddress(SuperModel):
    user = models.ForeignKey(StoreUser, on_delete=models.CASCADE, related_name='billing_addresses')
    store = models.ForeignKey(MyStore, on_delete=models.CASCADE, related_name='store_billing_addresses')
    first_name = models.CharField(max_length=100)
    surname = models.CharField(max_length=100)
    address_line1 = models.CharField(max_length=255)
    address_line2 = models.CharField(max_length=255, null=True, blank=True)  
    area = models.ForeignKey(Area, on_delete=models.CASCADE)
    sub_area = models.ForeignKey(SubArea, on_delete=models.CASCADE)
    postal_code = models.CharField(max_length=20, null=True, blank=True)  
    country = models.ForeignKey(Country, on_delete=models.CASCADE)
    mobile_number = models.CharField(max_length=15)  
    second_mobile = models.CharField(max_length=15, null=True, blank=True) 
    is_default = models.BooleanField(default=False)  




