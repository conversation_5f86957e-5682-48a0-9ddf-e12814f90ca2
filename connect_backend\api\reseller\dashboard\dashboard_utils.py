import base64
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage

def upload_temp_image(base64_image, file_name):
    try:
        format, imgstr = base64_image.split(';base64,')
        ext = format.split('/')[-1]
        data = ContentFile(base64.b64decode(imgstr), name=f"{file_name}.{ext}")
        temp_path = f"temp/{file_name}.{ext}"
        temp_file_path = default_storage.save(temp_path, data)
        temp_url = default_storage.url(temp_file_path)
        return temp_url
    except Exception as e:
        raise ValueError(f"Invalid image data: {e}")