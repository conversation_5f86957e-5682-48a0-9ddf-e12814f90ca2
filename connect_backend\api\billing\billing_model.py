from django.db import models
from ..models import SuperModel
from ..users.user_model import Company
from ..lookups.lookup_model import *
from datetime import timedelta

class Package(SuperModel):
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(max_length=255, blank=True, null=True)
    no_of_orders = models.IntegerField(default=500, null=True)
    price_monthly = models.FloatField(default=0, null=True)
    price_yearly = models.FloatField(default=0, null=True)
    is_active = models.BooleanField(default=False)
    tier = models.IntegerField(null=True, default=0)
    stripe_product_id = models.CharField(max_length=255, null=True)

class StripeProduct(SuperModel):
    price = models.DecimalField(max_digits=14, decimal_places=2)
    price_id = models.CharField(max_length=255)
    currency = models.CharField(max_length=10)
    package = models.ForeignKey(Package, on_delete=models.CASCADE, null=True)
    recurring_interval = models.CharField(max_length=20, blank=True, null=True)

    class Meta:
        unique_together = ('recurring_interval', 'package')

class CompanyPlan(SuperModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    package = models.ForeignKey(Package, on_delete=models.SET_NULL, null=True)

class Subscription(models.Model):
    stripe_subscription_id = models.CharField(max_length=255, blank=True, null=True)
    start_date = models.DateTimeField(null=False)
    end_date = models.DateTimeField(null=False)
    company_plan = models.ForeignKey(CompanyPlan, on_delete=models.SET_NULL, null=True)
    order_limit = models.IntegerField(default=100)
    active = models.BooleanField(default=True)
    recurring_interval = models.CharField(max_length=255, blank=True, null=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='subscription_set')
    cancelled = models.BooleanField(default=False)
    is_freemium = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        if self.pk:
            previous_instance = Subscription.objects.get(id=self.pk)
            if not previous_instance.is_freemium and self.is_freemium:
                self.activate_freemium()
        elif self.is_freemium:
            self.activate_freemium()
        super().save(*args, **kwargs)
    
    def activate_freemium(self):
        self.is_freemium = True
        self.order_limit = 50
        self.start_date = timezone.now()
        self.end_date = timezone.now() + timedelta(days=30)

class Payment(models.Model):
    stripe_payment_id = models.CharField(max_length=255)
    amount = models.DecimalField(decimal_places=2, max_digits=12)
    currency = models.CharField(max_length=10)
    created_at = models.DateTimeField(auto_now_add=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    subscription = models.ForeignKey(Subscription, on_delete=models.CASCADE, null=True)
    customer_email = models.CharField(max_length=255, null=True, blank=True)
    customer_name = models.CharField(max_length=255, null=True, blank=True)

class Provider(models.Model):
    name = models.CharField(max_length=200)
    countries = models.ManyToManyField(Country)

class LimitUpgrade(models.Model):
    no_of_orders = models.IntegerField(default = 0)
    price = models.FloatField(default=0)
    active = models.BooleanField(default=False)

class BillingLog(models.Model):
    created_at = models.DateTimeField(auto_now_add=True)
    process = models.TextField()
    success = models.BooleanField(default=False)
    company = models.ForeignKey(Company, null=True, on_delete=models.SET_NULL, related_name='billing_logs')
