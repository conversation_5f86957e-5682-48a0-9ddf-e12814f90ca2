from django.core.management.base import BaseCommand
from api.reseller.models import *
from api.orders.order_model import *
from api.admin.admin_model import *
from django.utils.timezone import localtime

class Command(BaseCommand):
    help = "Calculate reseller wallet debit"

    def handle(self, *args, **kwargs):
        resellers = Reseller.objects.filter(is_delivery_company=True)
        for reseller in resellers:
            reseller_wallet = ResellerWallet.objects.get(reseller=reseller)
            reseller_companies = ResellerCompany.objects.filter(reseller=reseller).values_list('company', flat=True)
            try:
                current_date = localtime(timezone.now()).date()
                count = Order.objects.filter(
                    delivery_company=reseller.delivery_company,
                    delivery_date__date=current_date
                ).exclude(
                    return_date__isnull=False,
                    company__company_id__in=reseller_companies
                ).count()
                pricing, _ = ResellerDebitPricing.objects.get_or_create(id=1)
                if reseller.country.code == 'PS':
                    extra_debit = count * pricing.ps_price
                else:
                    extra_debit = count * pricing.jo_price
                reseller_wallet.debit += extra_debit
                reseller_wallet.save()
            except Exception as e:
                self.stdout.write(self.style.WARNING(f'Failed to update wallet for reseller {reseller.name} due to {str(e)}'))
        self.stdout.write(self.style.SUCCESS(f'Finished recalculating for {resellers.count()} resellers'))
        