from django.db import models
from ..models import SuperModel
from django.db.models import Q

class Category(SuperModel):
    from ..users.user_model import Company
    category_sequence = models.CharField(max_length=15, null=True, blank=True)
    name = models.CharField(max_length=200)
    # ! category_image to be deleted after migration
    category_image = models.ImageField(upload_to='category_images/', null=True, blank=True)
    category_image_url = models.CharField(max_length=200, null=True, blank=True)
    publish = models.BooleanField(default=False)
    active = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=False)

    def save(self, *args, **kwargs):
            if not self.category_sequence:
                if not self.pk:
                    last_category = Category.objects.filter(company=self.company).order_by('-category_sequence').first()
                    if last_category:
                        last_sequence_number = int(last_category.category_sequence.split("-")[1])
                    else:
                        last_sequence_number = 1000000
                    self.category_sequence = f"C-{last_sequence_number + 1}"
            super(Category, self).save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        self.active = False
        self.save()

    def restore(self):
        self.active = True
        self.save()

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['name', 'company'],
                condition=Q(active=True),
                name='unique_active_category_per_company'
            )
        ]



