from django.core.management.base import BaseCommand
from api.products.product_model import Product

class Command(BaseCommand):
    help = 'Set variants in order lines where the product has at least one variant'

    def handle(self, *args, **kwargs):
        products = Product.objects.all()
        for product in products:
            product.save()
            variants = product.variants.all()
            for variant in variants:
                variant.save()
            self.stdout.write(self.style.SUCCESS(f"Total variants updated: {variants.count()}"))

        self.stdout.write(self.style.SUCCESS(f"Total Products updated: {products.count()}"))
