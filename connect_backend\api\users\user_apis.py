from rest_framework.authtoken.models import Token
from django.views.decorators.csrf import csrf_exempt
from datetime import timed<PERSON><PERSON>
from django.utils import timezone
from django.core.files.base import ContentFile
import json
import uuid
import base64
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.forms.models import model_to_dict
from django.db.models import Q
from .user_model import *
from ..auth.auth_model import OtpToken
from ..util_functions import *
from ..orders.order_model import Status
from ..serializers import *
from .user_utils import *
from ..dashboard.dashboard_model import *
from ..permissions import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from django.views.decorators.csrf import csrf_exempt
from ..error.error_model import *
from django.utils.translation import gettext as _

@csrf_exempt
def get_roles(request):
    if request.method == 'GET':
        records = Role.objects.filter(active=True).order_by('role_id')
        serializer = RoleSerializer(records, many=True)
        return JsonResponse({'success': True, 'records': serializer.data}, status=200)
    else:
        return JsonResponse({'error': 'Method not allowed'}, status=405)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_user'])])
@subscription_required
def add_user(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    json_data['company'] = user.company

    required_fields = ['mobile_number', 'name', 'country_code', 'address', 'role', 'area', 'country']
    validation_errors, foreign_key_objects = validate_user(json_data, required_fields)

    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': validation_errors})

    values = build_user_values(json_data, foreign_key_objects, user.company)
    values['is_active'] = False
    values['waiting_confirmation'] = True
    values['created_by'] = user.name

    new_user = create_user(values)
    serializer = UserSerializer(new_user)
    return JsonResponse({'success': True, 'message': 'User Created Successfully', 'user': serializer.data}, status=201)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_user'])])
@csrf_exempt
def get_users(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('User', json_data, user)
    return JsonResponse(response_data, status=200)

@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def update_user(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
        
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
        
    user_to_edit = user
    if json_data.get('id', ''):
        user_to_edit = User.objects.get(id=json_data['id'])

    values = json_data
    company = user.company
    values['id'] = user_to_edit.id
    values['company'] = company
    values['updated_by'] = user.name
    validation_errors, foreign_key_objects = validate_user(values, [])
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ', '.join(validation_errors)})
    values = build_user_values(values, foreign_key_objects, company)
    if not user_has_group_permission(user.user, 'deactivate_user') and 'is_active' in values:
        values.pop('is_active')
    if user.id == user_to_edit.id and'is_active' in values and not values['is_active']:
        raise get_error_response('AUTH_417')
    user = update_user_util(values)
    user.user.groups.clear()
    user.user.groups.add(user.role.group)
    return JsonResponse({'success': True, 'message': 'User updated successfully'}, status=200)
    
@csrf_exempt
def generate_link(request):
    if request.method == 'POST':
        try:
            # Parse the JSON body
            json_data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
        
        user = get_user_from_token(request)
        if user is None:
            return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
        
        if user.role.name == 'Sales':
            return JsonResponse({'success': False, 'error': 'Unauthorized'}, status=403)
        
        company_id = user.company.company_id
        mobile_number = json_data.get('mobile_number')

        if mobile_number is None and company_id is not None:
            return JsonResponse({'success': False, 'error': 'missing information'}, status=400)
        
        if User.objects.filter(mobile_number=mobile_number,company_id=company_id,is_active=True).exists():
            return JsonResponse({'success': False, 'error': 'a user with the same mobile_number and same company and is an active user already exists'}, status=400)

        try:
            token, created = OtpToken.objects.get_or_create(mobile_number=mobile_number)
            token.expires = timezone.now() + timedelta(minutes=30)
            token.save()
            return JsonResponse({'success':True,'token': token.token}, status=200)
        except OtpToken.DoesNotExist:
            return JsonResponse({'success':False,'error': 'Error creating token'}, status=400)
    else:
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_user_info(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    serializer = UserSerializer(user)
    return JsonResponse({'success': True, 'user': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_company'])])
def get_company_info(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    company = user.company

    serializer = CompanySerializer(company)
    return JsonResponse({'success': True, 'company': serializer.data})
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_companyconf'])])
@csrf_exempt
def set_conf(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
        
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    validation_errors, foreign_key_objects = validate_conf(json_data, user.company)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
        
    values = {**json_data, **foreign_key_objects, 'company': user.company}
    values['updated_by'] = user.name

    company_conf = update_conf_util(values, user)
    serializer = CompanyConfSerializer(company_conf)
        
    return JsonResponse({'success': True, 'conf': serializer.data}, status=200)
    
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_companyconf'])])
@csrf_exempt
def get_conf(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    company_conf = CompanyConf.objects.get(company=user.company)
    serializer = CompanyConfSerializer(company_conf)
    return JsonResponse({'success': True, 'conf': serializer.data}, status=200)
    
@csrf_exempt
def set_player_id(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    player_id = json_data.get('player_id')
    
    try:
        user.player_id = player_id
        user.save()
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
    return JsonResponse({'success': True,'message': 'Player ID updated successfully'}, status=200)

@csrf_exempt
def delete_user(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    try:
        user.is_deleted = True
        user.save()
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
    return JsonResponse({'success': True,'message': 'User deleted successfully'}, status=200)


@csrf_exempt
def get_system_configuration(request):
    if request.method != 'GET':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    system_config = SystemConfiguration.objects.first()
    serializer = SystemConfigurationSerializer(system_config)
    return JsonResponse({'success': True, 'system_configuration': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_company'])])
def update_company(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    values = json_data
    values['updated_by'] = user.name
    company = user.company
    company = update_company_util(json_data, company)
    return JsonResponse({'success': True, 'message': _('Company Updated Successfully')})
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def set_user_preferences(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    values = json_data
    values['updated_by'] = user.name
    user_preferences, _ = UserPreferences.objects.get_or_create(user=user)
    user_preferences.set_order_tree_view(json_data.get('order_tree_view', []))
    user_preferences.save()
    serializer = UserPreferencesSerializer(user_preferences)
    return JsonResponse({'success': True, 'user_preferences': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_user_preferences(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    try:
        user_preferences = UserPreferences.objects.get(user=user)
    except UserPreferences.DoesNotExist:
        user_preferences = UserPreferences.objects.create(user=user)
    serializer = UserPreferencesSerializer(user_preferences)
    return JsonResponse({'success': True, 'user_preferences': serializer.data}, status=200)

@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['reset_user_password'])])
def reset_user_password(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    mobile_number = json_data.get('mobile_number')
    password = json_data.get('password')
    if not mobile_number or not password:
        raise get_error_response('GENERAL_003', {'fields': 'mobile_number, password'})
    
    try:
        user = User.objects.get(mobile_number=mobile_number)
    except User.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'User', 'field': 'user', 'value': mobile_number})
    
    if user.is_deleted:
        return JsonResponse({'success': False, 'error': _('User with provided mobile number does not exist')}, status=400)
    
    if not user.is_active:
        raise get_error_response('AUTH_415', {})
    
    user.user.set_password(password)
    user.user.save()
    user.save()
    return JsonResponse({'success': True, 'message': 'Password updated successfully'}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_companywallet'])])
def get_company_wallet(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    wallet, _ = CompanyWallet.objects.get_or_create(company=user.company)
    serializer = CompanyWalletSerializer(wallet)
    return JsonResponse({'success': True, 'company_wallet': serializer.data}, status=200)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_drivers'])])
@csrf_exempt
def get_drivers(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('User', json_data, user, custom_filter=Q(role__name__iexact='driver', is_active=True, user__isnull=False))
    return JsonResponse(response_data, status=200)