from django.views.decorators.csrf import csrf_exempt
import json
from django.http import JsonResponse
from ..serializers import *
from ..util_functions import *
from .pricelist_utils import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ..permissions import *
from ..error.error_model import *
from ..permissions import *

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_pricelist'])])
@csrf_exempt
def add_pricelist(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
        
    values = json_data
    pricelist_items = values.pop('pricelist_items', [])
    values['company'] = user.company
    values['created_by'] = values['updated_by'] = user.name
    values['code'] = set_code(values)
    if values.get('connection', None):
        connection = ConnectDeliveryCompany.objects.get(id=values['connection'], company=user.company)
        values['connection'] = connection
    else:
        values['connection'] = None

    required_fields = ['name', 'code']
    validation_errors, foreign_key_objects = validate_pricelist(values, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'message': validation_errors}, status=400)
    values = {**values, **foreign_key_objects}
    pricelist = create_pricelist(values, pricelist_items)
    serializer = PricelistSerializer(pricelist)

    return JsonResponse({'success': True, 'pricelist': serializer.data}, status=201)

@csrf_exempt
def update_pricelist(request):
    if request.method != 'PUT':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
        
    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
        
    if user.role.name == 'Sales':
        return JsonResponse({'success': False, 'error': 'Unauthorized'}, status=403)
        
    values = json_data
    values['company'] = user.company
    values['updated_by'] = user.name
    pricelist_items = values.pop('pricelist_items')

    if values.get('connection', None):
        connection = ConnectDeliveryCompany.objects.get(id=values['connection'])
        values['connection'] = connection
    else:
        values['connection'] = None
    try:
        pricelist = update_pricelist_util(values, pricelist_items)
    except ValidationError as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)
    if not pricelist:
        return JsonResponse({'success': False, 'error': 'ERROR_UPDATING_PRICELIST_ITEMS'}, status=500)
            
    return JsonResponse({'success': True, 'message': 'Pricelist updated successfully'}, status=200)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_pricelist'])])
@csrf_exempt
def get_all_pricelist(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    result = get_records('Pricelist', json_data, user)
    return JsonResponse(result, status=200)
        
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_pricelistitem'])])
@csrf_exempt
def get_pricelist_by_id(request):
    try:
        try:
            pricelist_id = int(request.GET.get('id'))
        except (TypeError, ValueError):
            raise get_error_response('GENERAL_003', {'fields': 'id'})
    
        try:
            user = User.objects.get(user=request.user)
        except User.DoesNotExist:
            raise get_error_response('AUTH_400', {'username': request.user.username})
    
        pricelist = get_pricelist(pricelist_id, user.company)
        if not pricelist:
            raise get_error_response('GENERAL_002', {'model': 'Pricelist', 'field': 'id', 'value': pricelist_id})
    
        pricelist_items = get_pricelist_items(pricelist)
        
        pricelist_serializer = PricelistSerializer(pricelist)
        pricelist_items_serializer = PricelistItemSerializer(pricelist_items, many=True)
        
        return JsonResponse({
            'success': True,
            'pricelist': pricelist_serializer.data,
            'pricelist_items': pricelist_items_serializer.data
        })
    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

# returns the pricelist and its items for a specific delivery company or driver and fallback to the default pricelist if not provided
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_pricelistitem'])])
@csrf_exempt
def get_pricelist_for_assignee(request):
    try:
        try:
            user = User.objects.get(user=request.user)
        except User.DoesNotExist:
            raise get_error_response('AUTH_400', {'username': request.user.username})
        driver_id = request.GET.get('driver_id')
        connection_id = request.GET.get('connection_id')
        pricelist_items = []
        if driver_id:
            driver = User.objects.filter(id=driver_id, company=user.company).first()
            if not driver:
                return JsonResponse({'success': False, 'error': 'Driver not found'}, status=404)
            if driver.role.code != 'driver':
                return JsonResponse({'success': False, 'error': 'Unauthorized'}, status=403)
            pricelist, pricelist_items = get_pricelist_items_by_driver(driver)
        elif connection_id:
            pricelist, pricelist_items = get_pricelist_items_by_connection(connection_id, user.company)

        if not pricelist_items:
            # get company default pricelist if no specific driver or delivery company is provided
            company_conf = CompanyConf.objects.filter(company=user.company).first()
            pricelist = company_conf.default_pricelist
            pricelist_items = PricelistItem.objects.filter(pricelist=company_conf.default_pricelist)

        pricelist_serializer = PricelistSerializer(pricelist)
        pricelist_items_serializer = PricelistItemSerializer(pricelist_items, many=True)

        return JsonResponse({
            'success': True,
            'pricelist': pricelist_serializer.data,
            'pricelist_items': pricelist_items_serializer.data
        })
    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
def delete_pricelist(request):
    if request.method != 'DELETE':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    try:
        pricelist_id = int(request.GET.get('id'))
    except ValueError:
        return JsonResponse({'success': False, 'error': 'Invalid pricelist ID'}, status=400)
    
    user = get_user_from_token(request)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    if user.role.name == 'Sales':
        return JsonResponse({'success': False, 'error': 'Unauthorized'}, status=403)

    pricelist = Pricelist.objects.filter(id=pricelist_id, company=user.company).first()

    if not pricelist:
        return JsonResponse({'success': False, 'error': 'Pricelist not found'}, status=404)

    if pricelist.delivery_company:
        return JsonResponse({'success': False, 'error': 'cannot delete pricelist from delivery company'}, status=403)
    
    try:
        with transaction.atomic():
            PricelistItem.objects.filter(pricelist=pricelist).delete()
            pricelist.delete()
            return JsonResponse({'success': True,'message': 'Pricelist deleted successfully'})
    except:
        return JsonResponse({'success': False, 'error': 'Error deleting pricelist'}, status=500)