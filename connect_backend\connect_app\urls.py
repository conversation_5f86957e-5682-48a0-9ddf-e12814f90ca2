from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from api.views import *

urlpatterns = [
    path('dj-admin/', admin.site.urls),
    path('api-auth/', include('rest_framework.urls', namespace='rest_framework')),
    path('auth/', include('api.auth.auth_urls')),
    path('user/', include('api.users.user_urls')),
    path('order/', include('api.orders.order_urls')),
    path('lookup/', include('api.lookups.lookup_urls')),
    path('delivery_company/', include('api.delivery_company.delivery_company_urls')),
    path('pricelist/', include('api.pricelists.pricelist_urls')),
    path('connection/', include('api.connection.connection_urls')),
    path('integration/', include('api.integrations.integration_urls')),
    path('dashboard/', include('api.dashboard.dashboard_urls')),
    path('admin/', include('api.admin.admin_urls')),
    path('logs/', include('api.logs.logs_urls')),
    path('product/', include('api.products.product_urls')),
    path('release_document/', include('api.release_document.release_document_urls')),
    path('client/', include('api.client.client_urls')),
    path('mystore/',include('api.mystore.mystore_urls')),
    path('billing/', include('api.billing.billing_urls')),
    path('media/<path:path>', serve_media, name='serve_media'),
    path('collection/', include('api.collection.collection_urls')),
    path('general/', include('api.general.general_urls')),
    path('warehouse/', include('api.warehouse.warehouse_urls')),
    path('category/',include('api.category.category_urls')),
    path('notification/',include('api.notification.notification_urls')),
    path('helper/',include('api.helpers.helpers_urls')),
    path('delivery_integration/',include('api.delivery_integrations.delivery_integrations_urls')),
    path('developer_connection/', include('api.developer_connection.developer_connection_urls')),
    path('feature/', include('api.feature.feature_urls')),
    path('financial_reconciliation/', include('api.financial_reconciliation.financial_reconciliation_urls')),
    path('reseller/',include('api.reseller.reseller_urls')),
    path('public-api/',include('api.public_api.public_api_urls')),
]

urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

urlpatterns += [
    re_path(r'.*$', lambda request: handle_404(request)),
]