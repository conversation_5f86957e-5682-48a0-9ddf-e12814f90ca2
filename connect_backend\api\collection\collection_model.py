from django.db import models
from ..users.user_model import Company
from ..lookups.lookup_model import Country
from ..models import SuperModel

class ConversionRate(models.Model):
    currency_from = models.CharField(max_length=50)
    currency_to = models.CharField(max_length=50)
    rate = models.DecimalField(max_digits=10, decimal_places=2)
    date = models.DateField(auto_now_add=True)

class CollectionStatus(models.Model):
    code = models.CharField(max_length=200)
    name = models.CharField(max_length=200)

class CollectionType(models.Model):
    code = models.CharField(max_length=200)
    name = models.Char<PERSON>ield(max_length=200)

class CollectionBank(models.Model):
    code = models.Char<PERSON>ield(max_length=200)
    name = models.CharField(max_length=200)
    country = models.ForeignKey(Country, on_delete=models.CASCADE)

class Collection(SuperModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    amount =  models.DecimalField(decimal_places=2, max_digits=12, default=0)
    collection_status = models.ForeignKey(CollectionStatus, on_delete=models.CASCADE)
    collection_type = models.ForeignKey(CollectionType, on_delete=models.CASCADE)
    currency = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    collection_bank = models.ForeignKey(CollectionBank, on_delete=models.SET_NULL, null=True)
    iban = models.CharField(max_length=200, null=True, blank=True)
    is_archive = models.BooleanField(default=False)

