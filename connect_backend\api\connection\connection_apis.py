from django.views.decorators.csrf import csrf_exempt
import requests
import json
from django.http import JsonResponse
from django.forms.models import model_to_dict
from django.db.models import Q
from ..delivery_company.delivery_company_model import DeliveryCompany
from ..users.user_model import User, Company
from .connection_model import *
from ..pricelists.pricelist_model import *
from ..orders.order_model import Order, StatusMap
from ..orders.order_utils import *
from ..util_functions import *
from django.utils.translation import gettext as _
from rest_framework.decorators import *
from ..permissions import *
from rest_framework.permissions import AllowAny
from .connection_utils import *
import os
from dotenv import load_dotenv
from ..error.error_model import *

load_dotenv()

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_connectdeliverycompany'])])
@subscription_required
def connect_with_delivery_company(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    company = user.company
    delivery_company_id = json_data.get('id')
    area_id = json_data.get('area_id')
    delivery_company, area = get_company_delivery_company_area(delivery_company_id, area_id)
    name = delivery_company.name
    username = company.company_mobile

    try:
        area_map = AreaMap.objects.get(area=area, delivery_company=delivery_company)
    except AreaMap.DoesNotExist:
        raise get_error_response('AREA_900')
    
    if not area_map.imported_area_id:
        raise get_error_response('AREA_900')

    if ConnectionUser.objects.filter(company_username=username, delivery_company=delivery_company, company=company).exists():
        raise get_error_response('CONNECTION_321', {'delivery_company': delivery_company.name, 'username': username})
    connection = ConnectDeliveryCompany.objects.create(name=name, delivery_company=delivery_company, area=area, company=company)
    ConnectionUser.objects.create(company_username=username, delivery_company=delivery_company, company=company, connection=connection)
        
    try:
        response = send_connection_request(connection, username, area_map, user)
    except ConnectError as e:
        connection.credentials.delete()
        connection.delete()
        raise e

    handle_connection_response(response, connection, company, delivery_company, area, user)
    serializer = ConnectDeliveryCompanySerializer(connection)
    return JsonResponse({'success': True, 'connection': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_connectdeliverycompany'])])
@subscription_required
def add_connection(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    company = user.company
    name = json_data.get('name')
    username = json_data.get('username')
    delivery_company_id = json_data.get('delivery_company_id')
    area_id = json_data.get('area_id')
    delivery_company, area = get_company_delivery_company_area(delivery_company_id, area_id)

    if ConnectDeliveryCompany.objects.filter(delivery_company=delivery_company, company=company).count() >= 8:
        raise get_error_response('CONNECTION_322', {'delivery_company': delivery_company.name})

    try:
        area_map = AreaMap.objects.get(area=area, delivery_company=delivery_company)
    except AreaMap.DoesNotExist:
        raise get_error_response('AREA_900')
    
    if not area_map.imported_area_id:
        raise get_error_response('AREA_900')

    if ConnectionUser.objects.filter(company_username=username, delivery_company=delivery_company, company=company).exists():
        raise get_error_response('CONNECTION_321', {'delivery_company': delivery_company.name, 'username': username})
    connection = ConnectDeliveryCompany.objects.create(name=name, delivery_company=delivery_company, area=area, company=company)
    ConnectionUser.objects.create(company_username=username, delivery_company=delivery_company, company=company, connection=connection)
        
    try:
        response = send_connection_request(connection, username, area_map, user)
    except ConnectError as e:
        connection.credentials.delete()
        connection.delete()
        raise e

    handle_connection_response(response, connection, company, delivery_company, area, user)
    serializer = ConnectDeliveryCompanySerializer(connection)
    return JsonResponse({'success': True, 'connection': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_connectdeliverycompany'])])
def edit_connection(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    company = user.company
    connection_id = json_data.get('connection_id')
    name = json_data.get('name')
    username = json_data.get('username')
    area_id = json_data.get('area_id')
    connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    area = Area.objects.get(id=area_id)
    delivery_company = connection.delivery_company

    if not connection.is_connected():
        raise get_error_response('CONNECTION_319', {'delivery_company': delivery_company.name})

    try:
        area_map = AreaMap.objects.get(area=area, delivery_company=delivery_company)
    except AreaMap.DoesNotExist:
        raise get_error_response('AREA_900')
    
    if not area_map.imported_area_id:
        raise get_error_response('AREA_900')
    
    name = name.replace(connection.delivery_company.name, '')
    if name and name.startswith('-'):
        name = name[1:]
    params = {
        "jsonrpc": "2.0",
        "params": {
            "db": connection.delivery_company.delivery_company_db,
            "login": connection.credentials.company_username,
            "password": connection.credentials.company_password,
            'values':{
                'area_id': area_map.imported_area_id,
                "username": f'{connection.company.company_name}-{name}' if name else connection.company.company_name,
                "commercial_name": f'{connection.company.company_name}-{name}' if name else connection.company.company_name,
                'connect_request_flag': True
            }
        }
    }
    if connection.credentials.company_username != username:
        params['params']['values']['mobile_number'] = username
    
    response = send_api_to_delivery_company(delivery_company, '/connection/edit_user', params)
    if response.get('code') == 200:
        connection.name = name
        connection.area = area
        connection.credentials.company_username = username
        connection.credentials.save()
        connection.save()

    serializer = ConnectDeliveryCompanySerializer(connection)
    return JsonResponse({'success': True, 'connection': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def update_connection_request(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
    
        values = json_data.get('params').get('values')
        company_id = json_data.get('params').get('company_id')
        connection_id = json_data.get('params').get('connection_id')
        delivery_company_id = json_data.get('params').get('delivery_company_id')
        action = values.get('action')
    
        try:
            delivery_company = DeliveryCompany.objects.get(id=delivery_company_id)
            company = Company.objects.get(company_id=company_id)
        except DeliveryCompany.DoesNotExist as e:
            raise get_error_response('GENERAL_002', {'model': 'DeliveryCompany', 'field': 'id', 'value': delivery_company_id})
        except Company.DoesNotExist as e:
            raise get_error_response('GENERAL_002', {'model': 'Company', 'field': 'id', 'value': company_id})

        status = get_connection_status(action)
        if not status:
            raise get_error_response('CONNECTION_311', {'action': action, 'delivery_company': delivery_company})
        
        connection = ConnectDeliveryCompany.objects.get(id=connection_id)
        if not connection:
            raise get_error_response('GENERAL_002', {'model': 'ConnectDeliveryCompany', 'field': 'Company, DeliveryCompany', 'value': ', '.join([company, delivery_company])})

        connection.connection_status = status
        connection.save()

        try:
            connection_user = ConnectionUser.objects.update_or_create(connection=connection, defaults={'company_username': values.get('company_username'), 'company_password': values.get('company_password')})
        except Exception as e:
            connection.connection_status = 'pending'
            connection.save()
            raise get_error_response('CONNECTION_312', {'delivery_company': delivery_company, 'error': str(e)})

        if connection.is_connected():
            missing_info = check_required_fields(values)
            print(values)
            if missing_info:
                if 'pricelist_items' in missing_info:
                    connection.connection_status = "pending"
                    connection.save()
                    raise get_error_response('CONNECTION_318', {})
                connection.connection_status = "pending"
                connection.save()
                raise get_error_response('CONNECTION_313', {'delivery_company': delivery_company, 'missing_info': ', '.join(missing_info)})
            
            try:
                handle_pricelist(values, connection)
                return JsonResponse({'result': {'code': 200, 'success': True, 'message': 'Connection completed'}}, status=200)
            except Exception as e:
                connection.connection_status = "pending"
                connection.save()
                raise get_error_response('CONNECTION_314', {'error': str(e)})
            
        return JsonResponse({'result': {'code': 200, 'success': True, 'message': 'Connection completed'}}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        response['error']['code'] = 401
        print(response)
        return JsonResponse({'result': response}, status=200)
    except Exception as e:
        print(f'Connection error {str(e)}')
        return JsonResponse({'result': {'code': 401, 'success': False, 'message': f'Error updating connection {str(e)}'}}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['send_orders_to_delivery_company'])])
def send_to_delivery_company(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    company = user.company
    success_messages = []
    fail_messages = []

    delivery_request_status = get_company_delivery_request_status(company)
    if json_data['orders_to_sent']:
        process_sending_orders(json_data['orders_to_sent'], user, delivery_request_status, success_messages, fail_messages)
        
    need_to_sent_ids = []
    if json_data['orders_already_sent']:
        order_ids = json_data['orders_already_sent'].get('order_ids',[])
        connection_id = json_data['orders_already_sent'].get('connection',False)
        driver_id = json_data['orders_already_sent'].get('driver_id',False)
        for order_id in order_ids:
            order = Order.objects.get(id=order_id)
            if order.driver and driver_id:
                need_to_sent_ids.append(order_id)
                continue
            same_connection = connection_id and order.connection and order.connection.id == connection_id
            same_driver = driver_id and order.driver and order.driver.id == driver_id
            has_sequence = getattr(order, 'olivery_sequence', None)
            is_olivery = (order.connection and 
              getattr(order.connection, 'delivery_company', None) and 
              getattr(order.connection.delivery_company, 'is_olivery_company', False))
            if has_sequence:
            # Skip if same connection and not olivery
                if (same_connection or same_driver) and not is_olivery:
                    continue

                # Cancel in vhub if different connection and olivery
                if not (same_connection or same_driver) and is_olivery:
                    vhub_vals = {
                        'partner_status': 'Cancelled',
                        'internal_partner_status': 'canceled',
                        'state': 'canceled'
                    }
                    response = update_order_to_vhub(
                        vhub_vals,
                        order.olivery_sequence,
                        order.connection.delivery_company,
                        order.connection.credentials,
                        company
                    )
            
            need_to_sent_ids.append(order_id)
        already_sent_data = {
            'order_ids': need_to_sent_ids,
            'connection': connection_id,
            'driver_id': driver_id
        }
        process_sending_orders([already_sent_data], user, delivery_request_status, success_messages, fail_messages)
        

    return JsonResponse({
        'success': True, 
        'success_messages': success_messages,
        'fail_messages': fail_messages
    }, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_connectionuser'])])
def get_user_credentials(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    connection_id = request.GET.get('id')

    if not connection_id:
        raise get_error_response('GENERAL_003', {'fields': ', '.join([connection_id])})
    
    try:
        connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    except ConnectDeliveryCompany.DoesNotExist as e:
        raise get_error_response('GENERAL_002', {'model': 'ConnectDeliveryCompany', 'field': 'id', 'value': connection_id})
    
    connection_user = connection.credentials
    serializer = ConnectionUserSerializer(connection)
    
    return JsonResponse({'success': True, 'user_credentials': serializer.data})
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def update_order_vhub(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
        
        params = json_data.get('params')
        company, delivery_company, orders_data = get_company_and_delivery_company(params)
        vals = orders_data['vals']
        orders = orders_data['orders']
        
        orders_for_notification = []
        
        for order in orders:
            process_order_update(order, vals, company, delivery_company, orders_for_notification)
        handle_status_change_notifications(orders_for_notification,delivery_company)
            
        
        return JsonResponse({'result': {'code': 200, 'message': _('Order updated successfully')}}, status=200)
    
    except ConnectError as e:
        response = e.to_dict()
        response['error']['code'] = 401
        return JsonResponse({'result': response}, status=200)
    
    except Exception as e:
        return JsonResponse({'result': {'code': 401, 'message': str(e)}}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_connectdeliverycompany'])])
def get_connected_companies(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    response_data = get_records('ConnectDeliveryCompany', json_data, user)
    return JsonResponse(response_data, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_connectdeliverycompany'])])
def cancel_connection(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    connection_id = json_data.get('connection_id')
    company = user.company

    try:
        connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    except ConnectDeliveryCompany.DoesNotExist as e:
        raise get_error_response('GENERAL_002', {'model': 'ConnectDeliveryCompany', 'field': 'id', 'value': connection_id})
    
    status = connection.connection_status
    if status not in ['not_conencted', 'cancelled']:
        response = send_cancel_request(connection, user)
        if response.get('success'):
            connection.connection_status = 'cancelled'
            connection.save()
    else:
        raise get_error_response('CONNECTION_317', {'status': status})
    serializer = ConnectDeliveryCompanySerializer(connection)
    return JsonResponse({'success': True, 'connection': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_connectdeliverycompany'])])
def uncancel_connection(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    connection_id = json_data.get('connection_id')
    company = user.company

    try:
        connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    except ConnectDeliveryCompany.DoesNotExist as e:
        raise get_error_response('GENERAL_002', {'model': 'ConnectDeliveryCompany', 'field': 'id', 'value': connection_id})
    
    try:
        area_map = AreaMap.objects.get(area=connection.area, delivery_company=connection.delivery_company)
    except AreaMap.DoesNotExist:
        raise get_error_response('AREA_900')
    
    if not area_map.imported_area_id:
        raise get_error_response('AREA_900')

    response = send_connection_request(connection, connection.credentials.company_username, area_map, user)

    handle_connection_response(response, connection, company, connection.delivery_company, connection.area, user)
    serializer = ConnectDeliveryCompanySerializer(connection)
    return JsonResponse({'success': True, 'connection': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def sync_connection(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        params = json_data.get('params')
        connection_id = params['connection_id']
        
        try:
            connection = ConnectDeliveryCompany.objects.get(id=connection_id)
        except ConnectDeliveryCompany.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'ConnectDeliveryCompany', 'field': 'id', 'value': connection_id})
        
        pricelist = params.get('values').get('pricelist')
        pricelist_items = params.get('values').get('pricelist_items')
        if not pricelist_items or not pricelist:
            raise get_error_response('CONNECTION_318', {})
        try:
            sync_pricelist(connection, pricelist, pricelist_items)
        except Exception as e:
            raise get_error_response('CONNECTION_314', {'error': str(e)})
        return JsonResponse({'result': {'code': 200, 'message': _('Syncing with Connect-Plus was successful')}}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        response['error']['code'] = 401
        return JsonResponse({'result': response}, status=200)
    except Exception as e:
        return JsonResponse({'result': {'code': 401, 'error': str(e)}}, status=200)
    
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_connectionuser'])])
def change_password(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    new_password = json_data.get('password')
    connection_id = json_data.get('connection_id')

    try:
        connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    except ConnectDeliveryCompany.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'ConnectDeliveryCompany', 'field': 'id', 'value': connection_id})
    
    params = {
        "jsonrpc": "2.0",
        "params": {
            "db": connection.delivery_company.delivery_company_db,
            "login": connection.credentials.company_username,
            "password": connection.credentials.company_password,
            'values':{
                'password': new_password,
                'connect_request_flag': True
            }
        }
    }
    response = send_api_to_delivery_company(connection.delivery_company, '/connection/edit_user', params)

    if response.get('code') == 200:
        connection.credentials.company_password = new_password
        connection.credentials.save()
        
    return JsonResponse({'result': {'code': 200, 'message': _('User updated successfully')}}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_connectdeliverycompany'])])
@subscription_required
def add_dynamic_connection(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    name = json_data.get('name')
    delivery_company_id = json_data.get('delivery_company_id')
    area_id = json_data.get('area_id')
    username = json_data.get('username', '')
    password = json_data.get('password', '')
    token = json_data.get('token', '')
    meta_data = json_data.get('meta_data', '')

    delivery_company = DeliveryCompany.objects.get(id=delivery_company_id)
    area = Area.objects.get(id=area_id)

    if ConnectDeliveryCompany.objects.filter(delivery_company=delivery_company, company=user.company).count() >= 3:
        raise get_error_response('CONNECTION_322', {'delivery_company': delivery_company.name})

    connection = ConnectDeliveryCompany.objects.create(
        name=name,
        delivery_company=delivery_company,
        company=user.company,
        area=area,
        connection_status='connected'
    )
    connection_user = ConnectionUser.objects.create(
        connection = connection,
        company_username=username,
        company_password=password,
        token=token,
        meta_data=meta_data
    )
    serializer = ConnectDeliveryCompanySerializer(connection)
    return JsonResponse({'success': True, 'connection': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_connectdeliverycompany'])])
def update_dynamic_connection(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    name = json_data.get('name')
    connection_id = json_data.get('connection_id')
    status = json_data.get('connection_status')
    area_id = json_data.get('area_id')
    username = json_data.get('username', '')
    password = json_data.get('password', '')
    token = json_data.get('token', '')
    meta_data = json_data.get('meta_data', '')
    area = Area.objects.get(id=area_id)
    connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    try:
        connection.name = name
        connection.area = area
        connection.connection_status = status
        connection.credentials.company_username = username
        connection.credentials.company_password = password
        connection.credentials.token = token
        connection.credentials.meta_data = meta_data
        connection.credentials.save()
        connection.save()
    except Exception as e:
        raise get_error_response('GENERAL_007', {'error': str(e)})
    serializer = ConnectDeliveryCompanySerializer(connection)
    return JsonResponse({'success': True, 'connection': serializer.data}, status=200)

@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_connectdeliverycompany'])])
def update_custom_fields_defaults(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    connection_id = json_data.get('id')
    connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    custom_field_defaults = json_data.get('custom_field_defaults', '')
    connection.custom_field_defaults = custom_field_defaults
    connection.save()
    serializer = ConnectDeliveryCompanySerializer(connection)
    return JsonResponse({'success': True, 'connection': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_options_list(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    connection_id = json_data.get('connection')
    connection = ConnectDeliveryCompany.objects.get(id=connection_id)
    request_template = json_data.get('request_template', '')
    if not request_template:
        raise get_error_response('GENERAL_003', {'fields': 'request_template'})
    request_template = RequestTemplate.objects.get(name=request_template, delivery_company=connection.delivery_company)
    
    params = build_request_params(request_template, connection)
    response = send_dynamic_api_request(request_template, params)
    options_list = response.get('data', [])

    return JsonResponse({'success': True, 'options_list': options_list}, status=200)