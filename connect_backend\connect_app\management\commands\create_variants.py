from django.core.management.base import BaseCommand
from api.products.product_model import Product, ProductVariant, ProductsImage

class Command(BaseCommand):
    help = 'Create default variants from products'

    def handle(self, *args, **kwargs):
        products = Product.objects.all()
        created_variants_count = 0

        for product in products:
            # Check if a variant already exists for the product
            if not product.variants.exists():
                # Create a default variant with the same name and SKU derived from the product's attributes
                ProductVariant.objects.create(
                    product=product,
                    name=product.name,
                    sku=f"{product.reference_sequence or 'SKU-DEFAULT'}",
                    variant_image_url=product.images.first()
                )
                if product.product_image:
                    ProductsImage.objects.create(product=product, product_image_url=product.product_image)
                created_variants_count += 1
                self.stdout.write(self.style.SUCCESS(f"Created default variant for product: {product.name}"))

        if created_variants_count:
            self.stdout.write(self.style.SUCCESS(f"{created_variants_count} default variants created successfully."))
        else:
            self.stdout.write(self.style.WARNING("No new variants created. All products already have variants."))
