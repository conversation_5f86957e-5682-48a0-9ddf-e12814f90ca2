from django.core.management.base import BaseCommand
from api.util_functions import reset_warehouse
from api.warehouse.warehouse_model import Warehouse

class Command(BaseCommand):
    help = "Resets the warehouse for a given company"

    def add_arguments(self, parser):
        parser.add_argument('company_id', type=int, help='ID of the company whose warehouse needs to be reset')

    def handle(self, *args, **options):
        try:
            company_id = options['company_id']
            warehouse = Warehouse.objects.get(company_id=company_id)
            reset_warehouse(warehouse)
            self.stdout.write(self.style.SUCCESS(f'Company Warehouse Reset Successfully for Company ID {company_id}'))
        except Warehouse.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'No warehouse found for Company ID {company_id}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error: {e}'))
