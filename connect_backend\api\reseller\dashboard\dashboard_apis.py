from ...util_functions import *
from .dashboard_utils import *
from ..models import *
from django.utils.translation import gettext as _
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from django.http import JsonResponse
from rest_framework.parsers import <PERSON><PERSON>artPars<PERSON>, FormParser
from ...auth.auth_utils import *
from django.utils.timezone import localtime

@api_view(['GET'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_info(request):
    try:
        reseller = request.user
        serializer = ResellerSerializer(reseller)
        return JsonResponse({'success': True, 'reseller': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['GET'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_wallet(request):
    try:
        reseller = request.user
        wallet, _ = ResellerWallet.get_or_create(reseller=reseller)
        serializer = ResellerWalletSerializer(wallet)
        return JsonResponse({'success': True, 'reseller': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
@parser_classes([MultiPartParser, FormParser])
def update_profile(request):
    try:
        reseller = request.user
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        with transaction.atomic():
            reseller_update, created = ResellerUpdate.objects.get_or_create(
                reseller=reseller, 
                status='pending'
            )

            for key, new_val in json_data.items():
                if key in ['id', 'pricelist_items', 'branches', 'is_active', 'waiting_confirmation']:
                    continue

                old_val = getattr(reseller, key, None)
                field = Reseller._meta.get_field(key)

                if isinstance(field, models.ImageField):
                    if new_val and new_val.startswith("data:image/"):
                        try:
                            file_name = f"{reseller.id}_{key}"
                            temp_url = upload_temp_image(new_val, file_name)

                            ResellerUpdateField.objects.update_or_create(
                                update=reseller_update,
                                field_name=key,
                                defaults={
                                    'old_value': old_val.url if old_val else None,
                                    'new_value': temp_url,
                                }
                            )
                        except Exception as e:
                            print(f"Failed to process image for field {key}: {e}")
                        continue
                    else:
                        continue
                
                if field.is_relation and field.many_to_one:
                    if key == 'country':
                        new_val = Country.objects.get(code=new_val)
                    old_val_display = old_val.id if old_val else None
                    new_val_display = new_val.id if isinstance(new_val, field.related_model) else new_val
                else:
                    old_val_display = old_val
                    new_val_display = new_val

                update_field = ResellerUpdateField.objects.filter(
                    update=reseller_update, field_name=key
                ).first()

                if str(old_val_display) == str(new_val_display):
                    if update_field:
                        update_field.delete()
                else:
                    if update_field:
                        update_field.old_value = old_val_display
                        update_field.new_value = new_val_display
                        update_field.new_value_id = new_val_display if field.is_relation else None
                        update_field.save()
                    else:
                        ResellerUpdateField.objects.create(
                            update=reseller_update,
                            field_name=key,
                            old_value=old_val_display,
                            new_value=new_val_display,
                            new_value_id=new_val_display if field.is_relation else None
                        )
            pricelist_items = json_data.get('pricelist_items', [])
            ResellerPricelistItemUpdate.objects.filter(update=reseller_update).delete()
            for item in pricelist_items:
                ResellerPricelistItemUpdate.objects.create(
                    update=reseller_update,
                    from_area=item.get('from_area'),
                    to_area=item.get('to_area'),
                    price=item.get('price')
                )
            branches = json_data.get('branches', [])
            DeliveryCompanyBranchUpdate.objects.filter(update=reseller_update).delete()
            for branch in branches:
                DeliveryCompanyBranchUpdate.objects.create(
                    update=reseller_update,
                    name=branch.get('name')
                )
            if not DeliveryCompanyBranchUpdate.objects.filter(update=reseller_update).exists() and not ResellerPricelistItemUpdate.objects.filter(update=reseller_update).exists() and not ResellerUpdateField.objects.filter(update=reseller_update).exists():
                reseller_update.delete()
                raise get_error_response('RESELLER_1203')
            reseller.update_pending = True
            reseller.save()
            serializer = ResellerUpdateSerializer(reseller_update)

        return JsonResponse({
            'success': True,
            'reseller': serializer.data,
        }, status=200)

    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def add_company(request):
    try:
        reseller = request.user
        json_data = parse_request_body(request) or request.data
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        mobile_number = json_data.get('mobile_number')
        delivery_companies = json_data.pop('delivery_companies', [])
        warehouses = json_data.pop('warehouses', [])
        default_warehouse = json_data.pop('default_warehouse', [])
        default_delivery_company = json_data.pop('default_delivery_company', [])

        if Company.objects.filter(company_mobile=mobile_number).exists():
            raise get_error_response('RESELLER_1200', {'mobile_number': mobile_number})
        
        if ResellerCompany.objects.filter(reseller=reseller).count() >= reseller.limit:
            raise get_error_response('RESELLER_1201', {})
        
        reseller_company = ResellerCompany.objects.create(
                    reseller=reseller,
                    name=json_data.get('name', ''),
                    company=None,
                    mobile_number=mobile_number,
                    status='Pending',
                    default_warehouse = Warehouse.objects.get(id=default_warehouse) if default_warehouse else None,
                    default_delivery_company=DeliveryCompany.objects.get(id=default_delivery_company) if default_delivery_company else None,
                    auto_send_orders = json_data.get('auto_send_orders', False),
                    status_code='pending')

        for delivery_company in delivery_companies:
            ResellerCompanyDeliveryCompany.objects.get_or_create(reseller_company=reseller_company, delivery_company=DeliveryCompany.objects.get(id=delivery_company))

        for warehouse in warehouses:
            ResellerCompanyWarehouse.objects.get_or_create(reseller_company=reseller_company, warehouse=Warehouse.objects.get(id=warehouse))

        serializer = ResellerCompanySerializer(reseller_company)
        
        return JsonResponse({'success': True, 'reseller_company': serializer.data}, status=200)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_invitation_link(request):
    try:
        reseller = request.user
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
        
        id = json_data.get('id')

        try:
            reseller_company = ResellerCompany.objects.get(id=id)
        except ResellerCompany.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'ResellerCompany', 'field': 'id', 'value': id})
        
        token, created = ResellerToken.objects.get_or_create(reseller_company=reseller_company, reseller=reseller)
        token.expires_at = timezone.now() + timedelta(hours=72)
        token.save()
        
        return JsonResponse({'success': True, 'token': token.token}, status=200)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def activate_account(request):
    try:
        reseller = request.user
        json_body = parse_request_body(request)
        if not json_body:
            raise get_error_response('GENERAL_001', {})
        
        mobile_number = json_body.get('mobile_number')

        try:
            user = User.objects.get(mobile_number=mobile_number)
        except User.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'User', 'field':'mobile_number', 'value': mobile_number})

        user.is_active = True
        user.waiting_confirmation = False
        user.is_deleted = False
        user.updated_by = reseller.name
        user.save()

        user.company.free_trial_end_date = timezone.now() + datetime.timedelta(days=3)
        user.company.save()

        company_plan = CompanyPlan.objects.create(company=user.company, package=None)
        Subscription.objects.create(start_date=timezone.now(), end_date=timezone.now() + datetime.timedelta(days=3), order_limit=500, active=True, recurring_interval='month', company=user.company, company_plan=company_plan)

        try:
            reseller_company = ResellerCompany.objects.get(company=user.company)
            reseller_company.status = _('Activated')
            reseller_company.status_code = 'activated'
            reseller_company.save()
        except ResellerCompany.DoesNotExist:
            pass

        return JsonResponse({'success': True,'message': f'User with mobile number {mobile_number} has been activated'}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def set_business_delivery_companies(request):
    try:
        reseller = request.user
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        company_id = json_data.get('company_id')
        delivery_companies = json_data.get('delivery_companies', [])
        company = Company.objects.get(company_id=company_id)

        CompanyDeliveryCompany.objects.filter(company=company).delete()
        for delivery_company in delivery_companies:
            CompanyDeliveryCompany.objects.get_or_create(company=company, delivery_company=DeliveryCompany.objects.get(id=delivery_company))

        return JsonResponse({'success': True, 'message': _('Company Updated Successfully')}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def update_company(request):
    try:
        reseller = request.user
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
        
        delivery_companies = json_data.get('delivery_companies', [])
        id = json_data.get('id')

        try:
            reseller_company = ResellerCompany.objects.get(id=id)
        except ResellerCompany.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'ResellerCompany', 'field': 'id', 'value': id})

        for delivery_company in delivery_companies:
            CompanyDeliveryCompany.objects.get_or_create(company=reseller_company.company, delivery_company=DeliveryCompany.objects.get(id=delivery_company))

        CompanyDeliveryCompany.objects.filter(mobile_number=reseller_company.company.company_mobile).exclude(id__in=delivery_companies).delete()
        serializer = ResellerCompanySerializer(reseller_company)
        
        return JsonResponse({'success': True, 'reseller_company': serializer.data}, success=200)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_companies(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        reseller = request.user

        response_data = get_records('ResellerCompany', json_data, False, company_filter=Q(reseller=reseller))
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_debit(request):
    try:
        reseller = request.user
        reseller_wallet = ResellerWallet.objects.get(reseller=reseller)
        try:
            current_date = localtime(timezone.now()).date()
            count = Order.objects.filter(
                delivery_company__name=reseller.name,
                delivery_date__date=current_date
            ).exclude(
                return_date__isnull=False
            ).count()
            pricing, _ = ResellerDebitPricing.objects.get_or_create(id=1)
            if reseller.country.code == 'PS':
                extra_debit = count * pricing.ps_price
            else:
                extra_debit = count * pricing.jo_price
            debit = reseller_wallet.debit + extra_debit
        except Exception as e:
            raise get_error_response('GENERAL_007', {'error': str(e)})
        
        return JsonResponse({'success': True, 'debit': float(debit)}, status=200)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_delivery_companies(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('DeliveryCompany', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
