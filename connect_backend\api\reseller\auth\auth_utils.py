from ...util_functions import *

def validate_reseller(values, required_fields=[]):
    validation_errors = []
    foreign_key_objects = {}

    for field in required_fields:
        if not values.get(field):
            validation_errors.append(f'Missing field: {field}')

    foreign_key_objects.update(get_reseller_foreign_keys(values, validation_errors))

    return validation_errors, foreign_key_objects

def get_reseller_foreign_keys(values, validation_errors):
    foreign_key_objects = {}

    if 'country' in values:
        foreign_key_objects['country'] = get_country(values, validation_errors)

    if 'area' in values:
        foreign_key_objects['area'] = get_area(values, validation_errors, foreign_key_objects['country'] if foreign_key_objects['country'] else None)

    return foreign_key_objects

def create_reseller_util(values, branches, pricelist_items):
    try:
        with transaction.atomic():
            password = values.pop('password')
            reseller = Reseller.objects.create(**values)
            reseller.set_password(password)
            reseller.save()
            if reseller.is_delivery_company:
                create_branches(reseller, branches)
                create_pricelist_items(reseller, pricelist_items)
            ResellerWallet.objects.create(reseller=reseller)
            return reseller
    except Exception as e:
        raise e
    
def validate_mobile_number(mobile_number):
    return bool(re.match(r'^0[0-9]{9}$', mobile_number))

def create_branches(reseller, branches):
    for branch in branches:
        DeliveryCompanyBranch.objects.create(reseller=reseller, **branch)

def create_pricelist_items(reseller, pricelist_items):
    for item in pricelist_items:
        item.pop('id', None)
        ResellerPricelistItem.objects.create(reseller=reseller, **item)