from api.notification.notification_utils import send_notification
from .integration_model import *
from ..util_functions import *
from ..billing.billing_utils import *
from ..orders.order_utils import *
from ..error.error_model import *
import math
from django.utils import translation
from django.utils.translation import gettext as _
from datetime import timedelta

def create_ecommerce_token(company, ecommerce, username):
    return EcommerceToken.objects.create(
        company=company,
        created_at=timezone.now(),
        ecommerce=ecommerce,
        expires_at=timezone.now() + timedelta(hours=720),
        created_by=username,
        updated_by=username
    )

def encode_token(ecommerce_token, ecommerce):
    payload = {
        'company': ecommerce_token.company.company_id,
        'channel': ecommerce,
        'created_at': ecommerce_token.created_at.isoformat(),
        'expires_at': ecommerce_token.expires_at.isoformat(),
        'token_id': ecommerce_token.id
    }
    secret_key = settings.SECRET_KEY
    return jwt.encode(payload, secret_key, algorithm='HS256')

def decode_token(token):
    secret_key = settings.SECRET_KEY

    try:
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return {'error': 'Token has expired'}
    except jwt.InvalidTokenError:
        return {'error': 'Invalid token'}

def validate_token(request):
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Token '):
        return decode_token(auth_header[6:])
    return None

def validate_orders(orders_list, company, required_fields):
    validation_errors = []
    foreign_key_objects_list = []
    company_conf = get_company_conf(company)
    default_area = company_conf.default_area
    default_country = company_conf.default_country
    for order in orders_list:
        normalize_country_and_mobile(order)
        if not order.get('country', None):
            order['country'] = default_country.code
        try:
            errors, foreign_key_objects = validate_order(order, company, required_fields)
        except ConnectError as e:
            return handle_connect_error(e)
        except Exception as e:
            return [str(e)], None
        apply_default_mappings(order, errors, foreign_key_objects, default_country, default_area)
        remove_resolved_errors(errors)
        order = adjust_order_values(order, company, foreign_key_objects, company_conf)
        validation_errors.extend(errors if isinstance(errors, list) else [errors])
        foreign_key_objects_list.append(foreign_key_objects)

    return validation_errors, foreign_key_objects_list

def handle_connect_error(error):
    response = error.to_dict()
    return [response['error']['message']], None

def apply_default_mappings(order, errors, foreign_key_objects, default_country, default_area):
    default_mappings = {
        'Invalid country name': ('country', default_country),
        'Invalid country code': ('country', default_country),
        'Invalid area name': ('area', default_area),
        'Invalid sub area name': (
            'sub_area',
            SubArea.objects.filter(area=foreign_key_objects.get('area'), is_default_for_area=True).first()
            if foreign_key_objects.get('area')
            else SubArea.objects.filter(area=default_area, is_default_for_area=True).first()
        ),
    }
    for error_key, (fk_key, default_value) in default_mappings.items():
        if error_key in errors:
            update_area_mismatch(order, error_key, fk_key)
            foreign_key_objects[fk_key] = default_value

    if 'Invalid area name' in errors and 'Invalid sub area name' not in errors:
        foreign_key_objects['area'] = foreign_key_objects['sub_area'].area

def remove_resolved_errors(errors):
    resolved_errors = [
        'Invalid country name',
        'Invalid country code',
        'Invalid area name',
        'Invalid sub area name',
    ]
    for resolved_error in resolved_errors:
        if resolved_error in errors:
            errors.remove(resolved_error)

def adjust_order_values(order, company, foreign_key_objects, company_conf):
    if company_conf.rounding_up_integration_cod:
        round_cod_up(order)
    if order['country_code'] == 'TEMP' and order.get('country', None):
        country = foreign_key_objects.get('country', None)
        mobile_intro = country.mobile_intro.split('|')[0] if country.code == 'PS' else country.mobile_intro
        order['country_code'] = order.get('country_code', mobile_intro)
    if order.get('paid') and order.get('total_cod', 0) > 0:
        order['amount_received'] = order.get('total_cod')
    if order.get('total_cod', 0) == 0:
        order['amount_received'] = float(order.get('package_cost', 0)) + float(order.get('delivery_fee', 0)) + float(order.get('extra_delivery_fee', 0))
    order['product_info'] = process_product_info(order['product_info'])
    order['is_fixed_total'] = True
    calculate_cod_and_fees(order, company, foreign_key_objects)
    return order

def process_product_info(product_info):
    if not product_info:
        return ''
    try:
        product_info_obj = json.loads(product_info) if isinstance(product_info, str) else product_info
    except (TypeError, json.JSONDecodeError):
        product_info_obj = product_info

    product_info_as_string = product_info_obj
    if isinstance(product_info_obj, list):
        if isinstance(product_info_obj[0], dict):
            try:
                product_info_as_string = [
                    " || ".join(
                        f"{label}: {product[field]}"
                        for field, label in [('sku', 'SKU'), ('name', 'Name'), ('quantity', 'Quantity')]
                        if product.get(field)
                    )
                    for product in product_info_obj
                ]
            except Exception:
                return product_info


    return "\n".join(product_info_as_string)

def process_integration_orders(values):
    orders_list = values.get('orders_list')
    foreign_key_objects_list = values.get('foreign_key_objects_list')
    company = values.get('company')
    integration_token = values.get('integration_token')
    order_sequences, order_references, auto_send_list = [], [], []
    try:
        with transaction.atomic():
            for order, foreign_key_objects in zip(orders_list, foreign_key_objects_list):
                order.update({
                    'channel': integration_token.get('channel'),
                })
                order_lines = order.pop('order_lines', [])
                order_values = build_order_values(order, foreign_key_objects, company, order['channel'])
                order_instance = create_order(order_values, order_lines)
                if integration_token.get('developer_connection'):
                    developer_connection_id = integration_token.get('developer_connection')
                    handle_developer_connection_order(order_instance, developer_connection_id)
                order_sequences.append(order_instance.order_sequence)
                order_references.append(order_instance.order_reference)
                auto_send_list.append(order_instance)
    except Exception as e:
        raise e
    return order_sequences, order_references, auto_send_list

def handle_send_notification(orders_sequences,channel):
    notification_data = {
        'users': [],
        'sound': None,
        'title': '',
        'message': '',
        'data': {},
        'source':channel
    }
    orders = Order.objects.filter(order_sequence__in=orders_sequences)
    for order in orders:
        users = User.objects.filter(
            Q(company=order.company) &
            (
                Q(role__name__in=['Super Manager', 'Manager', 'Sales Manager']) |
                Q(name=order.created_by)
            )
        )

        notification_data['users'].extend(list(users))
        notification_data['users'] = list(set(notification_data['users']))
    try:
        notification = Notification.objects.get(source=channel)
        notification_data['sound'] = notification.sound

    except Notification.DoesNotExist:
        print(f"Notification not found for channel: {channel}")
        return
    

    title = 'New Order'
    message = _("{num_of_orders} New orders has been created through {channel}. Order sequences: {orders_sequences}")

    notification_data['data'] = {
        "filters": [{
            "operator": "and",
            "filters": [{
                "field": "order_sequence",
                "operator": "in",
                "value": orders_sequences
            }]
        }],
        'order_count': len(orders_sequences),
        'title': title
    }
    notification_data['message'] = message
    notification_data['title'] = title
    context = {'num_of_orders':len(orders_sequences),'channel':channel,'orders_sequences':orders_sequences}
    send_notification(notification_data,context=context)
        
def round_cod_up(order_data):
    order_data["total_cod"] = math.ceil(float(order_data.get('total_cod', 0)))
        
def handle_developer_connection_order(order, developer_connection_id):
    try:
        developer_integration = DeveloperConnection.objects.get(id=developer_connection_id)
    except DeveloperConnection.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Developer Connection not found'}, status=404)
    developer_order = DeveloperOrder.objects.create(
        developer_connection = developer_integration,
        order = order,
    )
    developer_order.save()

def get_connected_companies_list(company):
    return ConnectDeliveryCompany.objects.filter(company=company, connection_status='connected')

def update_woocommerce_information_util(values):
    try:
        woocommerce_information = WoocommerceInformation.objects.get(company=values['company'])
    except WoocommerceInformation.DoesNotExist:
        raise ValidationError("Woocommerce Information not found")
    
    log = create_log('WoocommerceInformation', values['company'], 'update', values['updated_by'], woocommerce_information.id)
    update_woocommerce_information_fields(woocommerce_information, values, log)
    delete_log_if_no_changes(log)
    woocommerce_information.save()
    return woocommerce_information

def update_woocommerce_information_fields(woocommerce_information, values, log):
    fields = get_tracked_fields(woocommerce_information.company, 'WoocommerceInformation')

    for key, value in values.items():
        old_value = getattr(woocommerce_information, key, None)
        if old_value != value:
            log_and_update_field(woocommerce_information, key, value, old_value, log, bool(key in fields))
