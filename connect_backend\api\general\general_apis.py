from ..util_functions import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from ..error.error_model import *
from django.utils.translation import gettext as _
from django.db.models import Count, F


@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def read_group(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    if not request.user.is_superuser:
        user = User.objects.get(user=request.user)
    
    model = json_data.get('model')
    if not model:
        raise get_error_response('GENERAL_002', {'fields': 'model'})
    
    model_class = apps.get_model('api', model)
    if model_class is None:
        raise get_error_response('GENERAL_002', {'model': model})
    
    group_by = json_data.get('group_by', [])
    if not group_by:
        raise get_error_response('GENERAL_002', {'fields': 'group_by'})
            
    level = int(json_data.get('level'))
    filters = json_data.get('filters', [])
    search = json_data.get('search')
    q_filters = build_filters(filters)
    values = json_data.get('group_values', [])
    foreign_key_serializer = None

    if level >= len(group_by):
        raise get_error_response('GENERAL_005', {})

    dynamic_filters = q_filters
    custom_filter = Q()

    if 'company' in [field.name for field in model_class._meta.get_fields()]:
        dynamic_filters &= Q(company=user.company)
    elif model == 'WarehouseTracking':
        dynamic_filters &= Q(warehouse_variant__warehouse__company=user.company)

    permission_string = f'view_self_{model.lower()}'
    if user_has_group_permission(request.user, permission_string):
        dynamic_filters &= Q(created_by_user=user)

    if user_has_group_permission(request.user, 'view_driver_orders') and model == 'Order':
        custom_filter = Q(driver=user, status__code='with_driver')

    search_filter = get_search_filter(search, model_class)
    dynamic_filters &= search_filter

    for i in range(level):
        group_field = group_by[i]
        value = values[i]
        field = model_class._meta.get_field(group_field)

        if isinstance(field, models.DateTimeField):
            date_value = datetime.datetime.strptime(value, "%Y-%m-%d").date()
            start_of_day = datetime.datetime.combine(date_value, datetime.time.min)
            end_of_day = datetime.datetime.combine(date_value, datetime.time.max)
            dynamic_filters &= Q(**{f"{group_field}__range": (start_of_day, end_of_day)})
        else:
            dynamic_filters &= Q(**{f"{group_field}__exact": value})

    current_group = group_by[level]
    field = model_class._meta.get_field(current_group)
    id_field = 'id'
    if model_class == Company:
        id_field = 'company_id'

    if isinstance(field, models.DateTimeField):
        results = (
            model_class.objects.filter(dynamic_filters).filter(custom_filter).distinct()
            .annotate(group_date=TruncDate(current_group))
            .values("group_date")
            .annotate(count=Count(id_field, distinct=True))
            .order_by("-group_date")
        )
        group_field = "group_date"
    else:
        results = (
            model_class.objects.filter(dynamic_filters).filter(custom_filter).distinct()
            .values(current_group)
            .annotate(count=Count(id_field, distinct=True))
            .order_by(current_group)
        )
        group_field = current_group

    foreign_key_serializer = None
    if field.is_relation and field.related_model:
        foreign_key_serializer = MODEL_SERIALIZER_MAP.get(field.related_model)

    serialized_results = []
    summary = {}
    for result in results:
        group_key = result[group_field]
        
        if isinstance(field, models.DateTimeField):
            filter_key = {
                "field": f"{current_group}__date",
                "operator": "exact",
                "value": group_key.strftime("%Y-%m-%d") if group_key else None,
            }
        else:
            filter_key = {
                "field": current_group,
                "operator": "exact",
                "value": group_key,
            }
        if model == 'Order':
            fields = {}
            if user_has_group_permission(request.user, 'view_total_order_due_amount_when_group_orders'):
                fields['order_due_amount'] = 'order_due_amount'
            if user_has_group_permission(request.user, 'view_total_profit_when_group_orders'):
                fields['profit'] = 'profit'
            if user_has_group_permission(request.user, 'view_total_package_cost_when_group_orders'):
                fields['package_cost'] = 'package_cost'
            if user_has_group_permission(request.user, 'view_total_delivery_fee_when_group_orders'):
                fields['delivery_fee'] = 'delivery_fee'
                fields['extra_delivery_fee'] = 'extra_delivery_fee'
            if user_has_group_permission(request.user, 'view_total_cod_sum_when_group_orders'):
                fields['total_cod'] = 'total_cod'
            if user_has_group_permission(request.user, 'view_total_amount_received_when_group_orders'):
                fields['amount_received'] = 'amount_received'

            if len(fields.keys()):
                filter_item = build_single_filter(filter_key)
                queryset = model_class.objects.filter(dynamic_filters).filter(filter_item).filter(custom_filter).distinct()
                currency_aggregates = queryset.values(id_field, 'currency').annotate(**{
                    f'{sum_field}': Sum(sum_field, distinct=True) for sum_field in fields
                })
                summary = {key: Decimal(0) for key in fields.keys()}
                company_currency = user.company.company_area.country.currency

                for aggregate in currency_aggregates:
                    order_currency = aggregate['currency']
                    conversion_rate = get_conversion_rate(order_currency, company_currency)

                    for key in fields.keys():
                        value = Decimal(aggregate.get(key, 0))
                        converted_value = value * conversion_rate
                        summary[key] += round_two_decimals(converted_value)
                if summary.get('extra_delivery_fee', 0):
                    summary['delivery_fee'] += summary['extra_delivery_fee']
        if foreign_key_serializer and isinstance(group_key, int):
            foreign_instance = field.related_model.objects.get(pk=group_key)
            serialized_group = foreign_key_serializer(foreign_instance).data
        else:
            serialized_group = _(group_key) if group_key and isinstance(group_key, str) else group_key

        serialized_results.append({
            "group": serialized_group,
            "count": result["count"],
            "filter": filter_key,
            "sum_fields": summary
        })
    
    response_data = {
        "level": level,
        "group_by": current_group,
        "results": serialized_results,
    }

    return JsonResponse({"success": True, "data": response_data}, status=200)