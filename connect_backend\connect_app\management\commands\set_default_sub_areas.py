from django.core.management.base import BaseCommand
from api.lookups.lookup_model import Area, SubArea, FuzzyArea, FuzzySubArea

class Command(BaseCommand):
    help = 'Set variants in order lines where the product has at least one variant'

    def handle(self, *args, **kwargs):
        for area in Area.objects.all():
            sub_areas = SubArea.objects.filter(area=area, is_default_for_area=True)
            for sub_area in sub_areas:
                sub_area.is_default_for_area = False
                sub_area.save()
            if not SubArea.objects.filter(area=area, name=area.name).exists():
                SubArea.objects.create(area=area, name=area.name, code=area.code, is_default_for_area = True)
                fuzzy_area = FuzzyArea.objects.filter(area_name_arabic=area.name).first()
                FuzzySubArea.objects.create(sub_area_name_arabic=fuzzy_area.area_name_arabic, sub_area_name_english=fuzzy_area.area_name_english, sub_area_name_hebrew=fuzzy_area.area_name_hebrew)
                self.stdout.write(self.style.SUCCESS(f"Created default subarea for {area.name}"))
            elif SubArea.objects.filter(area=area, name=area.name).exists():
                sub_area = SubArea.objects.get(area=area, name=area.name)
                sub_area.is_default_for_area = True
                sub_area.save()
                self.stdout.write(self.style.SUCCESS(f"Set default subarea for {area.name}"))
        self.stdout.write(self.style.SUCCESS("Default subareas set successfully"))
