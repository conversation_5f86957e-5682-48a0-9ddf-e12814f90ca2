<!DOCTYPE html>

{% if meta.lang == 'ar' %}
<html lang='ar' dir="rtl">

</html>
{% else %}
<html lang='en' dir="ltr">

</html>
{% endif %}

<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_run_sheet_for_drivers.css">
</head>

{% if meta.lang == 'ar' %}

<body class="rtl">
    {% else %}

    <body class="ltr">
        {% endif %}
        <div class="header">
            <div class="business-logo">
                <img src="{{docs[0].company_logo}}" class="business-logo">
                <div class="text-center fw-bold">{{docs[0].company_registry}}</div>
            </div>
            <h2 class="info-item">
                {{docs[0].company_name}}
            </h2>
            <div class="date-time fw-bold">
                {{meta.formatted_now}}
            </div>
        </div>

        <table>
            <tr>
                <th>#</th>
                <th>{{meta.labels.NAME}}</th>
                <th>{{meta.labels.MOBILE_NUMBER}}</th>
                <th>{{meta.labels.ADDRESS}}</th>
                <th>{{meta.labels.DELIVERY_FEE}}</th>
                <th>{{meta.labels.COD}}</th>
            </tr>
            {% set ns = namespace(total_cod=0, total_delivery_fee=0) %}
            {% for doc in docs %}
            {% set ns.total_cod = ns.total_cod + (doc.order.total_cod or 0) %}
            {% set ns.total_delivery_fee = ns.total_delivery_fee + (doc.order.delivery_fee or 0) %}
            
            <tr>
                <td>{{loop.index}}</td>
                <td>{{doc.order.customer_name}}</td>
                <td>{{doc.order.customer_mobile}}</td>
                <td>
                    {{doc.order.area.name}},
                    {{doc.order.sub_area.name}},
                    {{doc.order.address}}
                </td>
                <td>{{doc.order.delivery_fee}}</td>
                <td>{{doc.order.total_cod}}</td>
            </tr>
            {% endfor %}
            <tr>
                <td colspan="4" style="text-align: right;"><strong>{{meta.labels.TOTAL}}</strong></td>
                <td><strong>{{ns.total_delivery_fee}}</strong></td>
                <td><strong>{{ns.total_cod}}</strong></td>
            </tr>
        </table>

    </body>

    </html>