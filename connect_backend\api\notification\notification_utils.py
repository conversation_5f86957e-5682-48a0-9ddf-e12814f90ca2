# notifications.py
from collections import defaultdict
from onesignal_sdk.client import Client
from django.conf import settings
from .notification_model import *
from django.utils import translation
from django.utils.translation import gettext as _

def create_notification(values):
    notification = Notification.objects.create(**values)
    return notification

def create_notification_banner(values):
    notification_banner = NotificationBanner.objects.create(**values)
    return notification_banner

def update_notification_banner_util(values):
    notification_banner = NotificationBanner.objects.get(id=values['id'])
    for key, value in values.items():
        setattr(notification_banner, key, value)
    notification_banner.save()
    return notification_banner

def send_notification(notification_data, context={}):
    title = notification_data.get('title', 'Default Title')
    message = notification_data.get('message', 'Default Message')
    sound = notification_data.get('sound', None)
    url = notification_data.get('url', None)
    source = notification_data.get('source', None)
    onesignal_client = Client(
        app_id=settings.ONESIGNAL_APP_ID,
        rest_api_key=settings.ONESIGNAL_API_KEY
    )
    grouped_users = defaultdict(list)
    
    for user in notification_data['users']:
        grouped_users[user.lang].append(user)

    for lang, users in grouped_users.items():
        translation.activate(lang)
        temp_context = context
        for key, value in temp_context.items():
            if not isinstance(value, str):
                continue
            temp_context[key] = _(value)
        localized_message = _(message).format(**(temp_context or {}))
        localized_title = _(title)
        
        player_ids = [user.player_id for user in users if user.player_id]
        
        notification_content = {
            'headings': {"en": localized_title},
            'contents': {"en": localized_message},
            'include_player_ids': player_ids,
            'data': notification_data.get('data', {})
        }

        if sound:
            notification_content['android_channel_id'] = sound.channel
            notification_content['ios_sound'] = f"{sound.name}.wav"
            notification_content['existing_android_channel_id'] = sound.channel
            notification_content['priority'] = 10

        if url:
            notification_content['url'] = url

        translation.deactivate()

        try:
            orders_sequences = context.get('orders_sequences', [])
            
            notification = NotificationCenter.objects.create(
                message=message,
                source=source,
                orders_sequences=orders_sequences,
                context=context
            )
            notification.users.set(users)
            
            if notification_content['include_player_ids']:
                response = onesignal_client.send_notification(notification_content)
        except Exception as e:
            print("Error sending notification:", str(e))
            return None