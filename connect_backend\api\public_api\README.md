# Public Product Search API

This directory contains a public API for searching products without authentication. It provides PostgreSQL full-text search capabilities for the product catalog.

## Features

- **No Authentication Required**: All endpoints are publicly accessible
- **PostgreSQL Full-Text Search**: Advanced search capabilities with ranking
- **PostgreSQL Full-Text Search**: Advanced search capabilities with ranking and fuzzy matching
- **Caching**: Results are cached for improved performance
- **Flexible Input**: Supports both GET and POST requests
- **Comprehensive Matching**: Multiple fallback strategies to ensure best possible matches

## API Endpoints

### 1. Best Match Product Search
- **URL**: `/public-api/best-match/`
- **Methods**: `GET`, `POST`
- **Authentication**: None required

#### GET Request Example:
```
GET /public-api/best-match/?query=laptop&company_id=1&use_fuzzy=true
```

#### POST Request Example:
```json
{
    "query": "laptop computer",
    "company_id": 1,
    "use_fuzzy": true
}
```

#### Response:
```json
{
    "success": true,
    "message": "Found perfect matching product",
    "result": {
        "id": 1,
        "name": "Gaming Laptop",
        "price": "999.99",
        "description": "High-performance gaming laptop",
        "product_sequence": "P-1000001",
        "reference_sequence": "LAP001",
        "active": true,
        "publish": true,
        "category": {
            "id": 1,
            "name": "Electronics"
        },
        "virtual_stock": 50,
        "physical_stock": 50,
        "images": [...],
        "variants": [...]
    },
    "match_info": {
        "query": "laptop computer",
        "match_type": "full_text",
        "confidence_score": 95.0,
        "product_id": 1,
        "product_name": "Gaming Laptop"
    }
}
```



## Search Capabilities

The API provides advanced product search functionality:

**Best Match Search**: Advanced PostgreSQL full-text search that guarantees a result using multiple fallback methods including:
- Exact match search (case insensitive)
- PostgreSQL full-text search with weighted ranking
- Fuzzy matching with trigram similarity
- Character-by-character matching (especially effective for Arabic text)
- Arabic root-based similarity for Arabic language support

## Parameters

### Best Match Search Parameters
- **query** (string, required): Search query text
- **company_id** (integer, required): Company ID to filter products
- **use_fuzzy** (boolean, optional): Enable fuzzy matching (default: true)

## Response Format

All endpoints return JSON responses with the following structure:

```json
{
    "success": boolean,
    "message": string,
    "count": integer,
    "results": array,
    "query_info": object (for search endpoints)
}
```

## Error Handling

Error responses include:
- HTTP status codes (400, 404, 500)
- Error details in JSON format
- Helpful error messages

## Caching

- Best match search results are cached for 5 minutes for improved performance

## Files in this Directory

- `public_api_model.py`: ApiProducts proxy model with search methods
- `public_api_views.py`: API view functions for all endpoints
- `public_api_serializers.py`: Serializers for request/response data
- `public_api_urls.py`: URL routing configuration
- `README.md`: This documentation file

## Integration Example

```python
# Using the public API in your code
from api.public_api.public_api_model import ApiProducts

# Best match search (always returns a product)
product, match_info = ApiProducts.get_guaranteed_best_match("laptop", company_id=1)

# Using fuzzy matching for better results
fuzzy_match = ApiProducts.get_best_match_fuzzy("laptop", company_id=1)

# Direct character matching (good for non-Latin scripts)
character_match = ApiProducts.get_character_match("لابتوب", company_id=1)
```

## Integration with Existing Codebase

This public API integrates seamlessly with the existing Django project:

- Uses the existing Product and ProductVariant models
- Follows the same URL pattern conventions
- Uses the project's caching and logging infrastructure
- Respects the existing model relationships and constraints
- Provides comprehensive documentation via drf-yasg (Swagger UI)

## Security Considerations

- No authentication required (by design)
- Only exposes public product information
- Sensitive fields (cost, company info, etc.) are excluded
- Only active and published products are searchable
- Input validation prevents malicious queries
- Rate limiting should be implemented at the web server level if needed
