@page {
    size: 70mm 70mm;
    margin: 0;
}

body,
html {
    font-size: 5.25pt;
}

.o-table .cell-truncate {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 9rem;
    width: 9rem;
}

.waybill-container {
    padding: 1.58rem;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.o-table {
    border-spacing: 0 0.4rem;
}

.o-table td {
    padding: .5rem 1rem;
    width: 33%;
}

.date-time {
    font-size: 1.13rem;
}

.card {
    border: solid 0.033rem;
    border-radius: .5rem;
    padding: .5rem;
    margin-top: .5rem;
}

.card-header {
    font-size: 1.25rem;
    font-weight: bold;
    border-bottom: .15rem solid;
    padding-bottom: .3rem;
    margin-bottom: .3rem;
}

.left-cards-container {
    display: inline-block;
    width: 43%;
    /* padding: .3rem .5rem .8rem 0rem; */
    padding: .3rem 0rem .8rem .5rem;
}

.right-cards-container {
    display: inline-block;
    width: 55%;
    padding: .3rem 0rem .8rem 0rem;
    vertical-align: top;
}

.notes-content {
    height: 2.2rem;
    margin-bottom: .6rem;
    overflow: hidden;
}

.products-container {
    height: 16.6rem;
    max-height: 13.1rem;
    position: relative;
    font-size: .9rem;
    overflow: hidden
}

.products-container .total-row {
    position: absolute;
    bottom: 0rem;
    width: 100%;
}

.header-barcode-container {
    width: 18rem;
    height: 5.8rem;
}