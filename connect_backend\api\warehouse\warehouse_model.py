from ..models import *
from ..lookups.lookup_model import Country, Area
from django.utils.translation import gettext as _

class Warehouse(models.Model):
    name = models.CharField(max_length=200)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True)
    area = models.ForeignKey(Area, on_delete=models.SET_NULL, null=True)
    address = models.CharField(max_length=200, null=True, blank=True)
    company = models.ForeignKey('Company', on_delete=models.SET_NULL, null=True)
    reseller = models.ForeignKey('Reseller', on_delete=models.SET_NULL, null=True)

class WarehouseVariant(SuperModel):
    warehouse = models.ForeignKey(Warehouse, related_name='variants', on_delete=models.CASCADE)
    variant = models.ForeignKey('ProductVariant', related_name='warehouse_variants', on_delete=models.CASCADE)
    physical_quantity = models.IntegerField(null=False, default=0)
    virtual_quantity = models.IntegerField(null=False, default=0)
    reserved_quantity = models.IntegerField(null=False, default=0)

    def save(self, *args, **kwargs):
        if self.physical_quantity is not None and self.virtual_quantity is not None:
            self.reserved_quantity = self.physical_quantity - self.virtual_quantity

        super(WarehouseVariant, self).save(*args, **kwargs)
        total_physical_quantity = WarehouseVariant.objects.filter(variant__product=self.variant.product).aggregate(
            total=models.Sum('physical_quantity')
        )['total'] or 0

        total_virtual_quantity = WarehouseVariant.objects.filter(variant__product=self.variant.product).aggregate(
            total=models.Sum('virtual_quantity')
        )['total'] or 0

        self.variant.product.virtual_stock = total_virtual_quantity
        self.variant.product.physical_stock = total_physical_quantity
        self.variant.product.save()

class StockApproval(models.Model):
    warehouse_variant = models.ForeignKey(
        WarehouseVariant,
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    new_physical_quantity = models.IntegerField(null=False, default=0)
    new_virtual_quantity = models.IntegerField(null=False, default=0)
    new_reserved_quantity = models.IntegerField(null=False, default=0)
    user = models.ForeignKey(
        'User',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )
    status = models.CharField(
        max_length=50,
        choices=[("pending", "Pending"),("pending_warehouse_approval", "Pending Warehouse Approval"), ("approved", "Approved"), ("rejected", "Rejected"), ("warehouse_rejected", "Warehouse Rejected")]
    )
    difference_user_added = models.IntegerField(null=False, default=0)
    sequence = models.CharField(max_length=8, unique=True, null=True, blank=True)
    excel_file = models.CharField(max_length=200, null=True, blank=True)
    company = models.ForeignKey('Company', null=True ,on_delete=models.CASCADE)
    # This need to be set because excel files has no warehouse variant
    warehouse = models.ForeignKey(Warehouse, null=True ,on_delete=models.CASCADE)

    def save(self, *args, **kwargs):
        if not self.sequence:
            last_sequence = StockApproval.objects.aggregate(max_seq=models.Max('sequence'))['max_seq']
            if last_sequence:
                next_sequence = str(int(last_sequence) + 1).zfill(8)
            else:
                next_sequence = "00000001"
            self.sequence = next_sequence

        super().save(*args, **kwargs)

class WarehouseTracking(SuperModel):
    warehouse_variant = models.ForeignKey(WarehouseVariant,on_delete=models.CASCADE)
    physical_change = models.IntegerField(null=False, default=0)
    virtual_change = models.IntegerField(null=False, default=0)
    order = models.ForeignKey('Order',null=True,on_delete=models.SET_NULL)
    user = models.CharField(max_length=80,null=True,blank=True)
    previous_physical =  models.IntegerField(null=False, default=0) 
    previous_virtual = models.IntegerField(null=False, default=0)
    
class StocktakingProducts(models.Model):
    warehouse_variant = models.ForeignKey(WarehouseVariant,null=True ,on_delete=models.CASCADE)
    scanned_quantity = models.IntegerField(null=False, default=0)
    actual_quantity = models.IntegerField(null=False, default=0)

class Stocktaking(SuperModel):
    stocktaking_Products = models.ManyToManyField(StocktakingProducts,related_name='stocktaking')
    user = models.CharField(max_length=80,null=True,blank=True)
    sequence = models.CharField(max_length=8, unique=True, null=True, blank=True)
    company = models.ForeignKey('Company', null=True ,on_delete=models.CASCADE)
    reseller = models.ForeignKey('Reseller', on_delete=models.SET_NULL, null=True)
    
    def save(self, *args, **kwargs):
        if not self.sequence:
            last_sequence = Stocktaking.objects.aggregate(max_seq=models.Max('sequence'))['max_seq']
            if last_sequence:
                next_sequence = str(int(last_sequence) + 1).zfill(8)
            else:
                next_sequence = "00000001"
            self.sequence = next_sequence

        super().save(*args, **kwargs)


class Location(models.Model):

    name = models.CharField(max_length=100)
    type = models.CharField(
        max_length=10,
        choices=[
            ('rack', _('Rack')),
            ('shelf', _('Shelf')),
            ('bin', _('Bin')),
            ('palette', _('Palette')),
        ]
    )
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='children')
    warehouse = models.ForeignKey(Warehouse, on_delete=models.CASCADE)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['name', 'warehouse'],
                name='unique_location_name_per_warehouse'
            ),
            
        ]
        
    def save(self, *args, **kwargs):
        if self.type:
            self.type = self.type.lower()
        super().save(*args, **kwargs)

class LocationProduct(SuperModel):
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='location_products')
    product = models.ForeignKey(WarehouseVariant, on_delete=models.CASCADE, related_name='product_locations')
    quantity = models.PositiveIntegerField(default=0)


