from django.db import models
from ..users.user_model import Company
from ..delivery_company.delivery_company_model import DeliveryCompany
from ..connection.connection_model import ConnectDeliveryCompany
from datetime import timedelta, timezone, datetime
from django.conf import settings
import jwt
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.authentication import BaseAuthentication

class ConnectionToken(models.Model):
    company=models.ForeignKey(Company,on_delete=models.CASCADE)
    delivery_company = models.ForeignKey(DeliveryCompany,on_delete=models.CASCADE)
    connection = models.ForeignKey(ConnectDeliveryCompany, on_delete=models.SET_NULL, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_active = models.BooleanField(default=True)
    is_authenticated = models.BooleanField(default=True)
    token = models.CharField(max_length=300, null=True, blank=True)

    def generate_token(self):
        now = datetime.now(timezone.utc)
        payload = {
            'id': self.id,
            'company_id': self.company.company_id,
            'delivery_company_id': self.delivery_company.id,
            'exp': int((now + timedelta(days=30)).timestamp()),
            'iat': int(now.timestamp())
        }
        token = jwt.encode(payload, settings.SECRET_KEY, algorithm='HS256')
        return token

    def save(self, *args, **kwargs):
        self.expires_at = datetime.now(timezone.utc) + timedelta(days=30)
        if not self.pk:
            super().save(*args, **kwargs)
        self.token = self.generate_token()
        super().save(update_fields=["token", "expires_at", "is_active"])

class DeliveryIntegrationAuthentication(BaseAuthentication):
    def authenticate(self, request):
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Token '):
            raise AuthenticationFailed('Authorization header must start with "Token "')

        token = auth_header.split(' ')[1]
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
        except jwt.ExpiredSignatureError:
            raise AuthenticationFailed('Token has expired')
        except jwt.InvalidTokenError:
            raise AuthenticationFailed('Invalid token')

        try:
            connection = ConnectionToken.objects.get(id=payload['id'])
        except ConnectionToken.DoesNotExist:
            raise AuthenticationFailed('Connection not found')
        
        if connection.token != token:
            raise AuthenticationFailed('Token is expired due to a new token being generated')
        
        if not connection.is_active:
            raise AuthenticationFailed('Connection is not active')
        
        if not connection.expires_at > datetime.now(timezone.utc):
            raise AuthenticationFailed('Token has expired')

        return (connection, None)