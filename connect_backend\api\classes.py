from django.http import JsonResponse
import requests


WHATSAPP_API_CHAT_TOKEN = "RqA8EkXSTfDI"
WHATSAPP_API_CLIENT_ID = '25175'


class Whatsapp:
    
    def send_message(mobile_number,message):

        url = "https://gate.whapi.cloud/messages/text"

        payload = {
            "typing_time": 0,
            "to": mobile_number,
            "body": message
        }
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "authorization": "Bearer nQpKCHb6VkN01Z5yBP0fdEsNmmPybhUM"
        }

        response = requests.post(url, json=payload, headers=headers)

        if response.status_code == 200:
            return JsonResponse({'message': 'Request was successful', 'data': response.json()}, status=200)
        else:
            # Raise an HTTPError for bad responses (4xx and 5xx)
            response.raise_for_status()

    def refine_mobile_number(country_code, mobile_number):
            if mobile_number.startswith("0"):
                cleaned_mobile_number = mobile_number[1:]
            full_mobile_number = country_code + cleaned_mobile_number
            if full_mobile_number.startswith("+"):
                full_mobile_number = full_mobile_number[1:]
            return full_mobile_number