from slack_sdk import Web<PERSON><PERSON>
from slack_sdk.errors import SlackApiError
import os

SLACK_OTP_APP_KEY = os.getenv('SLACK_OTP_APP_KEY')
FRONT_BASEURL = os.getenv('FRONT_BASEURL')

# Initialize the Slack client with bot token
slack_token = SLACK_OTP_APP_KEY
client = WebClient(token=slack_token)

def send_slack_otp(message_text):
    try:
        # Post the message to the slack channel channel
        response = client.chat_postMessage(
            channel="#connect-otp-codes",
            text=f"Environment: {FRONT_BASEURL}. {message_text}"
        )
    except SlackApiError as e:
        # Handle any errors
        print(f"Error sending message: {e.response['error']}")

