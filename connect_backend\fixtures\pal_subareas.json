[{"model": "api.subarea", "pk": 1, "fields": {"name": "ابو غوش", "code": 1, "area": 1}}, {"model": "api.subarea", "pk": 2, "fields": {"name": "بيت نقوبا", "code": 2, "area": 1}}, {"model": "api.subarea", "pk": 3, "fields": {"name": "عين رافا", "code": 3, "area": 1}}, {"model": "api.subarea", "pk": 4, "fields": {"name": "عين نقوبا", "code": 4, "area": 1}}, {"model": "api.subarea", "pk": 5, "fields": {"name": "الجفتلك", "code": 5, "area": 2}}, {"model": "api.subarea", "pk": 6, "fields": {"name": "الزبيدات", "code": 6, "area": 2}}, {"model": "api.subarea", "pk": 7, "fields": {"name": "العوجا", "code": 7, "area": 2}}, {"model": "api.subarea", "pk": 8, "fields": {"name": "النبي موسى", "code": 8, "area": 2}}, {"model": "api.subarea", "pk": 9, "fields": {"name": "النويعمة", "code": 9, "area": 2}}, {"model": "api.subarea", "pk": 10, "fields": {"name": "حي الخديوي", "code": 10, "area": 2}}, {"model": "api.subarea", "pk": 11, "fields": {"name": "<PERSON><PERSON> القصب", "code": 11, "area": 2}}, {"model": "api.subarea", "pk": 12, "fields": {"name": "عين الديوك الفوقا", "code": 12, "area": 2}}, {"model": "api.subarea", "pk": 13, "fields": {"name": "فروش بيت دجن", "code": 13, "area": 2}}, {"model": "api.subarea", "pk": 14, "fields": {"name": "فصايل", "code": 14, "area": 2}}, {"model": "api.subarea", "pk": 15, "fields": {"name": "م<PERSON>يم عقبة جبر", "code": 15, "area": 2}}, {"model": "api.subarea", "pk": 16, "fields": {"name": "م<PERSON>يم عين السلطان", "code": 16, "area": 2}}, {"model": "api.subarea", "pk": 17, "fields": {"name": "مدينة اريحا", "code": 17, "area": 2}}, {"model": "api.subarea", "pk": 18, "fields": {"name": "شارع القدس", "code": 18, "area": 2}}, {"model": "api.subarea", "pk": 19, "fields": {"name": "شارع فلسطين", "code": 19, "area": 2}}, {"model": "api.subarea", "pk": 20, "fields": {"name": "وسط البلد", "code": 20, "area": 2}}, {"model": "api.subarea", "pk": 21, "fields": {"name": "الديوك", "code": 21, "area": 2}}, {"model": "api.subarea", "pk": 22, "fields": {"name": "النويعمة", "code": 22, "area": 2}}, {"model": "api.subarea", "pk": 23, "fields": {"name": "<PERSON>ي البياض", "code": 23, "area": 2}}, {"model": "api.subarea", "pk": 24, "fields": {"name": "شارع الاستراحة", "code": 24, "area": 2}}, {"model": "api.subarea", "pk": 25, "fields": {"name": "شارع البنوك", "code": 25, "area": 2}}, {"model": "api.subarea", "pk": 26, "fields": {"name": "شارع المغطس", "code": 26, "area": 2}}, {"model": "api.subarea", "pk": 27, "fields": {"name": "شارع المنتزهات", "code": 27, "area": 2}}, {"model": "api.subarea", "pk": 28, "fields": {"name": "شارع عمان", "code": 28, "area": 2}}, {"model": "api.subarea", "pk": 29, "fields": {"name": "شارع قصر بن هشام", "code": 29, "area": 2}}, {"model": "api.subarea", "pk": 30, "fields": {"name": "عقبة جبر", "code": 30, "area": 2}}, {"model": "api.subarea", "pk": 31, "fields": {"name": "عين السلطان", "code": 31, "area": 2}}, {"model": "api.subarea", "pk": 32, "fields": {"name": "كت<PERSON> الواد", "code": 32, "area": 2}}, {"model": "api.subarea", "pk": 33, "fields": {"name": "مرج الغزال", "code": 33, "area": 2}}, {"model": "api.subarea", "pk": 34, "fields": {"name": "مرج نعجة", "code": 34, "area": 2}}, {"model": "api.subarea", "pk": 35, "fields": {"name": "بيسان", "code": 35, "area": 3}}, {"model": "api.subarea", "pk": 36, "fields": {"name": "طمرة الزعبية", "code": 36, "area": 3}}, {"model": "api.subarea", "pk": 37, "fields": {"name": "ابطن", "code": 37, "area": 3}}, {"model": "api.subarea", "pk": 38, "fields": {"name": "ابو سنان", "code": 38, "area": 3}}, {"model": "api.subarea", "pk": 39, "fields": {"name": "اكسال", "code": 39, "area": 3}}, {"model": "api.subarea", "pk": 40, "fields": {"name": "البعنة", "code": 40, "area": 3}}, {"model": "api.subarea", "pk": 41, "fields": {"name": "الجش", "code": 41, "area": 3}}, {"model": "api.subarea", "pk": 42, "fields": {"name": "الحسينية", "code": 42, "area": 3}}, {"model": "api.subarea", "pk": 43, "fields": {"name": "الحلف", "code": 43, "area": 3}}, {"model": "api.subarea", "pk": 44, "fields": {"name": "الخضيرة", "code": 44, "area": 3}}, {"model": "api.subarea", "pk": 45, "fields": {"name": "الخوالد", "code": 45, "area": 3}}, {"model": "api.subarea", "pk": 46, "fields": {"name": "الرامة", "code": 46, "area": 3}}, {"model": "api.subarea", "pk": 47, "fields": {"name": "الريحانية", "code": 47, "area": 3}}, {"model": "api.subarea", "pk": 48, "fields": {"name": "الرينة", "code": 48, "area": 3}}, {"model": "api.subarea", "pk": 49, "fields": {"name": "الزرازير", "code": 49, "area": 3}}, {"model": "api.subarea", "pk": 50, "fields": {"name": "الشيخ دنون", "code": 50, "area": 3}}, {"model": "api.subarea", "pk": 51, "fields": {"name": "الطيبة الزعبية", "code": 51, "area": 3}}, {"model": "api.subarea", "pk": 52, "fields": {"name": "العزير", "code": 52, "area": 3}}, {"model": "api.subarea", "pk": 53, "fields": {"name": "الفريديس", "code": 53, "area": 3}}, {"model": "api.subarea", "pk": 54, "fields": {"name": "الكرمل", "code": 54, "area": 3}}, {"model": "api.subarea", "pk": 55, "fields": {"name": "الكعبية", "code": 55, "area": 3}}, {"model": "api.subarea", "pk": 56, "fields": {"name": "المزرعة (قضاء عكا)", "code": 56, "area": 3}}, {"model": "api.subarea", "pk": 57, "fields": {"name": "المشهد", "code": 57, "area": 3}}, {"model": "api.subarea", "pk": 58, "fields": {"name": "المغار", "code": 58, "area": 3}}, {"model": "api.subarea", "pk": 59, "fields": {"name": "المقيبلة", "code": 59, "area": 3}}, {"model": "api.subarea", "pk": 60, "fields": {"name": "الناصرة", "code": 60, "area": 3}}, {"model": "api.subarea", "pk": 61, "fields": {"name": "الناصرة العليا", "code": 61, "area": 3}}, {"model": "api.subarea", "pk": 62, "fields": {"name": "الناعورة", "code": 62, "area": 3}}, {"model": "api.subarea", "pk": 63, "fields": {"name": "ام الغنم", "code": 63, "area": 3}}, {"model": "api.subarea", "pk": 64, "fields": {"name": "اور عكيبا", "code": 64, "area": 3}}, {"model": "api.subarea", "pk": 65, "fields": {"name": "بردس حنا", "code": 65, "area": 3}}, {"model": "api.subarea", "pk": 66, "fields": {"name": "بسمة طبعون", "code": 66, "area": 3}}, {"model": "api.subarea", "pk": 67, "fields": {"name": "بعينة النجيدات", "code": 67, "area": 3}}, {"model": "api.subarea", "pk": 68, "fields": {"name": "بيت جن", "code": 68, "area": 3}}, {"model": "api.subarea", "pk": 69, "fields": {"name": "بير الامير", "code": 69, "area": 3}}, {"model": "api.subarea", "pk": 70, "fields": {"name": "بير المكسور", "code": 70, "area": 3}}, {"model": "api.subarea", "pk": 71, "fields": {"name": "ترشيحا", "code": 71, "area": 3}}, {"model": "api.subarea", "pk": 72, "fields": {"name": "جديدة المكر", "code": 72, "area": 3}}, {"model": "api.subarea", "pk": 73, "fields": {"name": "جسر الزرقا", "code": 73, "area": 3}}, {"model": "api.subarea", "pk": 74, "fields": {"name": "جش", "code": 74, "area": 3}}, {"model": "api.subarea", "pk": 75, "fields": {"name": "جول<PERSON>", "code": 75, "area": 3}}, {"model": "api.subarea", "pk": 76, "fields": {"name": "حي الصفافرة", "code": 76, "area": 3}}, {"model": "api.subarea", "pk": 77, "fields": {"name": "حيفا", "code": 77, "area": 3}}, {"model": "api.subarea", "pk": 78, "fields": {"name": "دالية الكرمل", "code": 78, "area": 3}}, {"model": "api.subarea", "pk": 79, "fields": {"name": "دبورية", "code": 79, "area": 3}}, {"model": "api.subarea", "pk": 80, "fields": {"name": "دير الاسد", "code": 80, "area": 3}}, {"model": "api.subarea", "pk": 81, "fields": {"name": "دير حنا", "code": 81, "area": 3}}, {"model": "api.subarea", "pk": 82, "fields": {"name": "راس العين", "code": 82, "area": 3}}, {"model": "api.subarea", "pk": 83, "fields": {"name": "راس علي", "code": 83, "area": 3}}, {"model": "api.subarea", "pk": 84, "fields": {"name": "رمانة", "code": 84, "area": 3}}, {"model": "api.subarea", "pk": 85, "fields": {"name": "زخرون يعقوب", "code": 85, "area": 3}}, {"model": "api.subarea", "pk": 86, "fields": {"name": "ساجور", "code": 86, "area": 3}}, {"model": "api.subarea", "pk": 87, "fields": {"name": "سخنين", "code": 87, "area": 3}}, {"model": "api.subarea", "pk": 88, "fields": {"name": "سولم", "code": 88, "area": 3}}, {"model": "api.subarea", "pk": 89, "fields": {"name": "شعب", "code": 89, "area": 3}}, {"model": "api.subarea", "pk": 90, "fields": {"name": "شفاعمرو", "code": 90, "area": 3}}, {"model": "api.subarea", "pk": 91, "fields": {"name": "شلومي", "code": 91, "area": 3}}, {"model": "api.subarea", "pk": 92, "fields": {"name": "ص<PERSON>د", "code": 92, "area": 3}}, {"model": "api.subarea", "pk": 93, "fields": {"name": "صندلة", "code": 93, "area": 3}}, {"model": "api.subarea", "pk": 94, "fields": {"name": "ضميده", "code": 94, "area": 3}}, {"model": "api.subarea", "pk": 95, "fields": {"name": "طباش", "code": 95, "area": 3}}, {"model": "api.subarea", "pk": 96, "fields": {"name": "طبريا", "code": 96, "area": 3}}, {"model": "api.subarea", "pk": 97, "fields": {"name": "طرعان", "code": 97, "area": 3}}, {"model": "api.subarea", "pk": 98, "fields": {"name": "طمرة", "code": 98, "area": 3}}, {"model": "api.subarea", "pk": 99, "fields": {"name": "طوبا الهيب", "code": 99, "area": 3}}, {"model": "api.subarea", "pk": 100, "fields": {"name": "طيرة الكرمل", "code": 100, "area": 3}}, {"model": "api.subarea", "pk": 101, "fields": {"name": "عب<PERSON>ين", "code": 101, "area": 3}}, {"model": "api.subarea", "pk": 102, "fields": {"name": "عتليت", "code": 102, "area": 3}}, {"model": "api.subarea", "pk": 103, "fields": {"name": "عرابة البطوف", "code": 103, "area": 3}}, {"model": "api.subarea", "pk": 104, "fields": {"name": "عرب الشبلي", "code": 104, "area": 3}}, {"model": "api.subarea", "pk": 105, "fields": {"name": "عرب النعيم", "code": 105, "area": 3}}, {"model": "api.subarea", "pk": 106, "fields": {"name": "عرب الهيب", "code": 106, "area": 3}}, {"model": "api.subarea", "pk": 107, "fields": {"name": "عسفيا", "code": 107, "area": 3}}, {"model": "api.subarea", "pk": 108, "fields": {"name": "عفولة", "code": 108, "area": 3}}, {"model": "api.subarea", "pk": 109, "fields": {"name": "عقبرا", "code": 109, "area": 3}}, {"model": "api.subarea", "pk": 110, "fields": {"name": "عكا", "code": 110, "area": 3}}, {"model": "api.subarea", "pk": 111, "fields": {"name": "عيلبون", "code": 111, "area": 3}}, {"model": "api.subarea", "pk": 112, "fields": {"name": "عيلوط", "code": 112, "area": 3}}, {"model": "api.subarea", "pk": 113, "fields": {"name": "عين الاسد", "code": 113, "area": 3}}, {"model": "api.subarea", "pk": 114, "fields": {"name": "<PERSON><PERSON><PERSON> حوض", "code": 114, "area": 3}}, {"model": "api.subarea", "pk": 115, "fields": {"name": "عين ماهل", "code": 115, "area": 3}}, {"model": "api.subarea", "pk": 116, "fields": {"name": "فرديس", "code": 116, "area": 3}}, {"model": "api.subarea", "pk": 117, "fields": {"name": "كابول", "code": 117, "area": 3}}, {"model": "api.subarea", "pk": 118, "fields": {"name": "كرمئيل", "code": 118, "area": 3}}, {"model": "api.subarea", "pk": 119, "fields": {"name": "كريات اتا", "code": 119, "area": 3}}, {"model": "api.subarea", "pk": 120, "fields": {"name": "كريات بياليك", "code": 120, "area": 3}}, {"model": "api.subarea", "pk": 121, "fields": {"name": "كريات طبعون", "code": 121, "area": 3}}, {"model": "api.subarea", "pk": 122, "fields": {"name": "كريات موتسكن", "code": 122, "area": 3}}, {"model": "api.subarea", "pk": 123, "fields": {"name": "كريات يام", "code": 123, "area": 3}}, {"model": "api.subarea", "pk": 124, "fields": {"name": "كسرى", "code": 124, "area": 3}}, {"model": "api.subarea", "pk": 125, "fields": {"name": "كفر سميع", "code": 125, "area": 3}}, {"model": "api.subarea", "pk": 126, "fields": {"name": "ك<PERSON>ر كما", "code": 126, "area": 3}}, {"model": "api.subarea", "pk": 127, "fields": {"name": "ك<PERSON>ر كنا", "code": 127, "area": 3}}, {"model": "api.subarea", "pk": 128, "fields": {"name": "<PERSON><PERSON><PERSON> مصر", "code": 128, "area": 3}}, {"model": "api.subarea", "pk": 129, "fields": {"name": "ك<PERSON>ر مندا", "code": 129, "area": 3}}, {"model": "api.subarea", "pk": 130, "fields": {"name": "كفرياسيف", "code": 130, "area": 3}}, {"model": "api.subarea", "pk": 131, "fields": {"name": "كفر ياسيف", "code": 131, "area": 3}}, {"model": "api.subarea", "pk": 132, "fields": {"name": "كمانة", "code": 132, "area": 3}}, {"model": "api.subarea", "pk": 133, "fields": {"name": "كوكب ابو الهيجا", "code": 133, "area": 3}}, {"model": "api.subarea", "pk": 134, "fields": {"name": "كيبوتس عوفر", "code": 134, "area": 3}}, {"model": "api.subarea", "pk": 135, "fields": {"name": "مج<PERSON> الكروم", "code": 135, "area": 3}}, {"model": "api.subarea", "pk": 136, "fields": {"name": "مجدال هعيمك", "code": 136, "area": 3}}, {"model": "api.subarea", "pk": 137, "fields": {"name": "معالوت ترشيحا", "code": 137, "area": 3}}, {"model": "api.subarea", "pk": 138, "fields": {"name": "معجان ميخائيل", "code": 138, "area": 3}}, {"model": "api.subarea", "pk": 139, "fields": {"name": "معليا", "code": 139, "area": 3}}, {"model": "api.subarea", "pk": 140, "fields": {"name": "منشية الزبدة", "code": 140, "area": 3}}, {"model": "api.subarea", "pk": 141, "fields": {"name": "ناعورة", "code": 141, "area": 3}}, {"model": "api.subarea", "pk": 142, "fields": {"name": "نحف", "code": 142, "area": 3}}, {"model": "api.subarea", "pk": 143, "fields": {"name": "نهاريا", "code": 143, "area": 3}}, {"model": "api.subarea", "pk": 144, "fields": {"name": "نوف هجليل", "code": 144, "area": 3}}, {"model": "api.subarea", "pk": 145, "fields": {"name": "نين", "code": 145, "area": 3}}, {"model": "api.subarea", "pk": 146, "fields": {"name": "وادي الحمام", "code": 146, "area": 3}}, {"model": "api.subarea", "pk": 147, "fields": {"name": "وادي سلامة", "code": 147, "area": 3}}, {"model": "api.subarea", "pk": 148, "fields": {"name": "يافة الناصرة", "code": 148, "area": 3}}, {"model": "api.subarea", "pk": 149, "fields": {"name": "يان<PERSON><PERSON> جت", "code": 149, "area": 3}}, {"model": "api.subarea", "pk": 150, "fields": {"name": "يركا", "code": 150, "area": 3}}, {"model": "api.subarea", "pk": 151, "fields": {"name": "الريحية", "code": 151, "area": 4}}, {"model": "api.subarea", "pk": 152, "fields": {"name": "اذنا", "code": 152, "area": 4}}, {"model": "api.subarea", "pk": 153, "fields": {"name": "السموع", "code": 153, "area": 4}}, {"model": "api.subarea", "pk": 154, "fields": {"name": "الشيوخ", "code": 154, "area": 4}}, {"model": "api.subarea", "pk": 155, "fields": {"name": "الظاهرية", "code": 155, "area": 4}}, {"model": "api.subarea", "pk": 156, "fields": {"name": "الظاهرية -رابود", "code": 156, "area": 4}}, {"model": "api.subarea", "pk": 157, "fields": {"name": "العروب", "code": 157, "area": 4}}, {"model": "api.subarea", "pk": 158, "fields": {"name": "بني نعيم", "code": 158, "area": 4}}, {"model": "api.subarea", "pk": 159, "fields": {"name": "بيت امر", "code": 159, "area": 4}}, {"model": "api.subarea", "pk": 160, "fields": {"name": "بيت أولا", "code": 160, "area": 4}}, {"model": "api.subarea", "pk": 161, "fields": {"name": "بيت اولا", "code": 161, "area": 4}}, {"model": "api.subarea", "pk": 162, "fields": {"name": "بيت عوا", "code": 162, "area": 4}}, {"model": "api.subarea", "pk": 163, "fields": {"name": "بيت عينون", "code": 163, "area": 4}}, {"model": "api.subarea", "pk": 164, "fields": {"name": "بيت كاحل", "code": 164, "area": 4}}, {"model": "api.subarea", "pk": 165, "fields": {"name": "ترقوميا", "code": 165, "area": 4}}, {"model": "api.subarea", "pk": 166, "fields": {"name": "تفوح", "code": 166, "area": 4}}, {"model": "api.subarea", "pk": 167, "fields": {"name": "<PERSON><PERSON><PERSON>و<PERSON>", "code": 167, "area": 4}}, {"model": "api.subarea", "pk": 168, "fields": {"name": "خاراس", "code": 168, "area": 4}}, {"model": "api.subarea", "pk": 169, "fields": {"name": "<PERSON>لة الدار", "code": 169, "area": 4}}, {"model": "api.subarea", "pk": 170, "fields": {"name": "دورا", "code": 170, "area": 4}}, {"model": "api.subarea", "pk": 171, "fields": {"name": "سعير", "code": 171, "area": 4}}, {"model": "api.subarea", "pk": 172, "fields": {"name": "صوريف", "code": 172, "area": 4}}, {"model": "api.subarea", "pk": 173, "fields": {"name": "م<PERSON>يم الفوار", "code": 173, "area": 4}}, {"model": "api.subarea", "pk": 174, "fields": {"name": "نوبا", "code": 174, "area": 4}}, {"model": "api.subarea", "pk": 175, "fields": {"name": "واد السمن", "code": 175, "area": 4}}, {"model": "api.subarea", "pk": 176, "fields": {"name": "يطا", "code": 176, "area": 4}}, {"model": "api.subarea", "pk": 177, "fields": {"name": "باب زاوية", "code": 177, "area": 4}}, {"model": "api.subarea", "pk": 178, "fields": {"name": "جبل شريف", "code": 178, "area": 4}}, {"model": "api.subarea", "pk": 179, "fields": {"name": "دوار المختار", "code": 179, "area": 4}}, {"model": "api.subarea", "pk": 180, "fields": {"name": "لوزا", "code": 180, "area": 4}}, {"model": "api.subarea", "pk": 181, "fields": {"name": "مفر<PERSON> الصاحب", "code": 181, "area": 4}}, {"model": "api.subarea", "pk": 182, "fields": {"name": "واد القطع", "code": 182, "area": 4}}, {"model": "api.subarea", "pk": 183, "fields": {"name": "مدينة الخليل", "code": 183, "area": 4}}, {"model": "api.subarea", "pk": 184, "fields": {"name": "ابو اكتيلة", "code": 184, "area": 4}}, {"model": "api.subarea", "pk": 185, "fields": {"name": "البلدة القديمة", "code": 185, "area": 4}}, {"model": "api.subarea", "pk": 186, "fields": {"name": "الجلدة", "code": 186, "area": 4}}, {"model": "api.subarea", "pk": 187, "fields": {"name": "الحاوز الأول", "code": 187, "area": 4}}, {"model": "api.subarea", "pk": 188, "fields": {"name": "الحاوز الثاني", "code": 188, "area": 4}}, {"model": "api.subarea", "pk": 189, "fields": {"name": "الحرس", "code": 189, "area": 4}}, {"model": "api.subarea", "pk": 190, "fields": {"name": "دوار المنارة", "code": 190, "area": 4}}, {"model": "api.subarea", "pk": 191, "fields": {"name": "وسط البلد", "code": 191, "area": 4}}, {"model": "api.subarea", "pk": 192, "fields": {"name": "المحاور", "code": 192, "area": 4}}, {"model": "api.subarea", "pk": 193, "fields": {"name": "المنطقة الجنوبية", "code": 193, "area": 4}}, {"model": "api.subarea", "pk": 194, "fields": {"name": "بير المحجر", "code": 194, "area": 4}}, {"model": "api.subarea", "pk": 195, "fields": {"name": "جبل ابو رمان", "code": 195, "area": 4}}, {"model": "api.subarea", "pk": 196, "fields": {"name": "ج<PERSON><PERSON> الرحمة", "code": 196, "area": 4}}, {"model": "api.subarea", "pk": 197, "fields": {"name": "حارة الشيخ", "code": 197, "area": 4}}, {"model": "api.subarea", "pk": 198, "fields": {"name": "حي الجامعه", "code": 198, "area": 4}}, {"model": "api.subarea", "pk": 199, "fields": {"name": "دوار ابن رشد", "code": 199, "area": 4}}, {"model": "api.subarea", "pk": 200, "fields": {"name": "دوار الصحه", "code": 200, "area": 4}}, {"model": "api.subarea", "pk": 201, "fields": {"name": "دويربان", "code": 201, "area": 4}}, {"model": "api.subarea", "pk": 202, "fields": {"name": "راس الجورة", "code": 202, "area": 4}}, {"model": "api.subarea", "pk": 203, "fields": {"name": "سنجر", "code": 203, "area": 4}}, {"model": "api.subarea", "pk": 204, "fields": {"name": "شارع السلام", "code": 204, "area": 4}}, {"model": "api.subarea", "pk": 205, "fields": {"name": "شارع الشلالة", "code": 205, "area": 4}}, {"model": "api.subarea", "pk": 206, "fields": {"name": "شارع العدل", "code": 206, "area": 4}}, {"model": "api.subarea", "pk": 207, "fields": {"name": "شارع الملك فيصل", "code": 207, "area": 4}}, {"model": "api.subarea", "pk": 208, "fields": {"name": "شارع بير السبع", "code": 208, "area": 4}}, {"model": "api.subarea", "pk": 209, "fields": {"name": "شعابة", "code": 209, "area": 4}}, {"model": "api.subarea", "pk": 210, "fields": {"name": "ضاحية البلدية", "code": 210, "area": 4}}, {"model": "api.subarea", "pk": 211, "fields": {"name": "ضاحية الرامة", "code": 211, "area": 4}}, {"model": "api.subarea", "pk": 212, "fields": {"name": "عقبة تفوح", "code": 212, "area": 4}}, {"model": "api.subarea", "pk": 213, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON>", "code": 213, "area": 4}}, {"model": "api.subarea", "pk": 214, "fields": {"name": "عين سارة", "code": 214, "area": 4}}, {"model": "api.subarea", "pk": 215, "fields": {"name": "فرش الهوى", "code": 215, "area": 4}}, {"model": "api.subarea", "pk": 216, "fields": {"name": "قرن الثور", "code": 216, "area": 4}}, {"model": "api.subarea", "pk": 217, "fields": {"name": "قيزون", "code": 217, "area": 4}}, {"model": "api.subarea", "pk": 218, "fields": {"name": "نمرة", "code": 218, "area": 4}}, {"model": "api.subarea", "pk": 219, "fields": {"name": "واد التفاح", "code": 219, "area": 4}}, {"model": "api.subarea", "pk": 220, "fields": {"name": "واد الزرازير", "code": 220, "area": 4}}, {"model": "api.subarea", "pk": 221, "fields": {"name": "واد الغروس", "code": 221, "area": 4}}, {"model": "api.subarea", "pk": 222, "fields": {"name": "واد الهرية", "code": 222, "area": 4}}, {"model": "api.subarea", "pk": 223, "fields": {"name": "مربعة سبتة", "code": 223, "area": 4}}, {"model": "api.subarea", "pk": 224, "fields": {"name": "ابو الطور", "code": 224, "area": 5}}, {"model": "api.subarea", "pk": 225, "fields": {"name": "الب<PERSON><PERSON> الجديد", "code": 225, "area": 5}}, {"model": "api.subarea", "pk": 226, "fields": {"name": "البلدة القديمة", "code": 226, "area": 5}}, {"model": "api.subarea", "pk": 227, "fields": {"name": "التلة الفرنسية", "code": 227, "area": 5}}, {"model": "api.subarea", "pk": 228, "fields": {"name": "الثوري", "code": 228, "area": 5}}, {"model": "api.subarea", "pk": 229, "fields": {"name": "الجامعة العبرية", "code": 229, "area": 5}}, {"model": "api.subarea", "pk": 230, "fields": {"name": "الشيخ جراح", "code": 230, "area": 5}}, {"model": "api.subarea", "pk": 231, "fields": {"name": "الصوانة", "code": 231, "area": 5}}, {"model": "api.subarea", "pk": 232, "fields": {"name": "الطور", "code": 232, "area": 5}}, {"model": "api.subarea", "pk": 233, "fields": {"name": "الطوري", "code": 233, "area": 5}}, {"model": "api.subarea", "pk": 234, "fields": {"name": "العيساوية", "code": 234, "area": 5}}, {"model": "api.subarea", "pk": 235, "fields": {"name": "القسطل", "code": 235, "area": 5}}, {"model": "api.subarea", "pk": 236, "fields": {"name": "القطمون", "code": 236, "area": 5}}, {"model": "api.subarea", "pk": 237, "fields": {"name": "المالحة", "code": 237, "area": 5}}, {"model": "api.subarea", "pk": 238, "fields": {"name": "المحطة المركزية", "code": 238, "area": 5}}, {"model": "api.subarea", "pk": 239, "fields": {"name": "المسكوبية", "code": 239, "area": 5}}, {"model": "api.subarea", "pk": 240, "fields": {"name": "المصرارة", "code": 240, "area": 5}}, {"model": "api.subarea", "pk": 241, "fields": {"name": "المطلع", "code": 241, "area": 5}}, {"model": "api.subarea", "pk": 242, "fields": {"name": "النبي صمويل", "code": 242, "area": 5}}, {"model": "api.subarea", "pk": 243, "fields": {"name": "النبي يعقوب", "code": 243, "area": 5}}, {"model": "api.subarea", "pk": 244, "fields": {"name": "ام طوبا", "code": 244, "area": 5}}, {"model": "api.subarea", "pk": 245, "fields": {"name": "امليسون", "code": 245, "area": 5}}, {"model": "api.subarea", "pk": 246, "fields": {"name": "ام ليسون", "code": 246, "area": 5}}, {"model": "api.subarea", "pk": 247, "fields": {"name": "باب الاسباط", "code": 247, "area": 5}}, {"model": "api.subarea", "pk": 248, "fields": {"name": "باب الجنا<PERSON>ز", "code": 248, "area": 5}}, {"model": "api.subarea", "pk": 249, "fields": {"name": "با<PERSON> الحديد", "code": 249, "area": 5}}, {"model": "api.subarea", "pk": 250, "fields": {"name": "باب الخليل", "code": 250, "area": 5}}, {"model": "api.subarea", "pk": 251, "fields": {"name": "باب الرحمة", "code": 251, "area": 5}}, {"model": "api.subarea", "pk": 252, "fields": {"name": "باب الزاهرة", "code": 252, "area": 5}}, {"model": "api.subarea", "pk": 253, "fields": {"name": "باب الساهرة", "code": 253, "area": 5}}, {"model": "api.subarea", "pk": 254, "fields": {"name": "باب العامود", "code": 254, "area": 5}}, {"model": "api.subarea", "pk": 255, "fields": {"name": "باب القطامين", "code": 255, "area": 5}}, {"model": "api.subarea", "pk": 256, "fields": {"name": "باب المثلث", "code": 256, "area": 5}}, {"model": "api.subarea", "pk": 257, "fields": {"name": "باب المزدوج", "code": 257, "area": 5}}, {"model": "api.subarea", "pk": 258, "fields": {"name": "باب المطهرة", "code": 258, "area": 5}}, {"model": "api.subarea", "pk": 259, "fields": {"name": "باب المغاربة", "code": 259, "area": 5}}, {"model": "api.subarea", "pk": 260, "fields": {"name": "باب الناظر", "code": 260, "area": 5}}, {"model": "api.subarea", "pk": 261, "fields": {"name": "باب الو<PERSON><PERSON>د", "code": 261, "area": 5}}, {"model": "api.subarea", "pk": 262, "fields": {"name": "باب حطة", "code": 262, "area": 5}}, {"model": "api.subarea", "pk": 263, "fields": {"name": "بسجات زئيف", "code": 263, "area": 5}}, {"model": "api.subarea", "pk": 264, "fields": {"name": "بيت حنينا (قدس)", "code": 264, "area": 5}}, {"model": "api.subarea", "pk": 265, "fields": {"name": "بيت صفافا", "code": 265, "area": 5}}, {"model": "api.subarea", "pk": 266, "fields": {"name": "تل بيوت", "code": 266, "area": 5}}, {"model": "api.subarea", "pk": 267, "fields": {"name": "ج<PERSON><PERSON> الزيتون", "code": 267, "area": 5}}, {"model": "api.subarea", "pk": 268, "fields": {"name": "ج<PERSON><PERSON> المشارف", "code": 268, "area": 5}}, {"model": "api.subarea", "pk": 269, "fields": {"name": "<PERSON><PERSON><PERSON> المكبر", "code": 269, "area": 5}}, {"model": "api.subarea", "pk": 270, "fields": {"name": "جبل صهيون", "code": 270, "area": 5}}, {"model": "api.subarea", "pk": 271, "fields": {"name": "جفعات زئيف", "code": 271, "area": 5}}, {"model": "api.subarea", "pk": 272, "fields": {"name": "جفعات شاؤل", "code": 272, "area": 5}}, {"model": "api.subarea", "pk": 273, "fields": {"name": "جفعات شموئيل", "code": 273, "area": 5}}, {"model": "api.subarea", "pk": 274, "fields": {"name": "جن<PERSON> عدن", "code": 274, "area": 5}}, {"model": "api.subarea", "pk": 275, "fields": {"name": "جيلو", "code": 275, "area": 5}}, {"model": "api.subarea", "pk": 276, "fields": {"name": "حارة النصارى", "code": 276, "area": 5}}, {"model": "api.subarea", "pk": 277, "fields": {"name": "<PERSON><PERSON> البستان", "code": 277, "area": 5}}, {"model": "api.subarea", "pk": 278, "fields": {"name": "دوار النخيل", "code": 278, "area": 5}}, {"model": "api.subarea", "pk": 279, "fields": {"name": "دوار بطن الهوا", "code": 279, "area": 5}}, {"model": "api.subarea", "pk": 280, "fields": {"name": "راس العامود", "code": 280, "area": 5}}, {"model": "api.subarea", "pk": 281, "fields": {"name": "سلوان", "code": 281, "area": 5}}, {"model": "api.subarea", "pk": 282, "fields": {"name": "شارع الزهراء", "code": 282, "area": 5}}, {"model": "api.subarea", "pk": 283, "fields": {"name": "شارع السلطان سليمان", "code": 283, "area": 5}}, {"model": "api.subarea", "pk": 284, "fields": {"name": "شارع صلاح الدين", "code": 284, "area": 5}}, {"model": "api.subarea", "pk": 285, "fields": {"name": "شارع يافا", "code": 285, "area": 5}}, {"model": "api.subarea", "pk": 286, "fields": {"name": "شرفات", "code": 286, "area": 5}}, {"model": "api.subarea", "pk": 287, "fields": {"name": "شعفاط", "code": 287, "area": 5}}, {"model": "api.subarea", "pk": 288, "fields": {"name": "شعفاط الابراج", "code": 288, "area": 5}}, {"model": "api.subarea", "pk": 289, "fields": {"name": "شيخ سعد القدس", "code": 289, "area": 5}}, {"model": "api.subarea", "pk": 290, "fields": {"name": "صور باهر", "code": 290, "area": 5}}, {"model": "api.subarea", "pk": 291, "fields": {"name": "عطروت", "code": 291, "area": 5}}, {"model": "api.subarea", "pk": 292, "fields": {"name": "عين اللوزة", "code": 292, "area": 5}}, {"model": "api.subarea", "pk": 293, "fields": {"name": "<PERSON>ي<PERSON> كارم", "code": 293, "area": 5}}, {"model": "api.subarea", "pk": 294, "fields": {"name": "كريات مناحيم", "code": 294, "area": 5}}, {"model": "api.subarea", "pk": 295, "fields": {"name": "كريات يوفيل", "code": 295, "area": 5}}, {"model": "api.subarea", "pk": 296, "fields": {"name": "محنة يودا", "code": 296, "area": 5}}, {"model": "api.subarea", "pk": 297, "fields": {"name": "نزلة حزما القدس", "code": 297, "area": 5}}, {"model": "api.subarea", "pk": 298, "fields": {"name": "هداسا العيساوية", "code": 298, "area": 5}}, {"model": "api.subarea", "pk": 299, "fields": {"name": "واد الحمص", "code": 299, "area": 5}}, {"model": "api.subarea", "pk": 300, "fields": {"name": "واد قدوم", "code": 300, "area": 5}}, {"model": "api.subarea", "pk": 301, "fields": {"name": "وادي الجوز", "code": 301, "area": 5}}, {"model": "api.subarea", "pk": 302, "fields": {"name": "وادي الدم", "code": 302, "area": 5}}, {"model": "api.subarea", "pk": 303, "fields": {"name": "وادي الربابة", "code": 303, "area": 5}}, {"model": "api.subarea", "pk": 304, "fields": {"name": "وادي حلوة", "code": 304, "area": 5}}, {"model": "api.subarea", "pk": 305, "fields": {"name": "الجبعة", "code": 305, "area": 6}}, {"model": "api.subarea", "pk": 306, "fields": {"name": "الحلقوم", "code": 306, "area": 6}}, {"model": "api.subarea", "pk": 307, "fields": {"name": "الخاص", "code": 307, "area": 6}}, {"model": "api.subarea", "pk": 308, "fields": {"name": "الشواورة", "code": 308, "area": 6}}, {"model": "api.subarea", "pk": 309, "fields": {"name": "العبيدية", "code": 309, "area": 6}}, {"model": "api.subarea", "pk": 310, "fields": {"name": "الفريديس", "code": 310, "area": 6}}, {"model": "api.subarea", "pk": 311, "fields": {"name": "المعصرة", "code": 311, "area": 6}}, {"model": "api.subarea", "pk": 312, "fields": {"name": "المنشية", "code": 312, "area": 6}}, {"model": "api.subarea", "pk": 313, "fields": {"name": "المنيا", "code": 313, "area": 6}}, {"model": "api.subarea", "pk": 314, "fields": {"name": "الولجة", "code": 314, "area": 6}}, {"model": "api.subarea", "pk": 315, "fields": {"name": "ام سلمونة", "code": 315, "area": 6}}, {"model": "api.subarea", "pk": 316, "fields": {"name": "بتير", "code": 316, "area": 6}}, {"model": "api.subarea", "pk": 317, "fields": {"name": "بيت تعمر", "code": 317, "area": 6}}, {"model": "api.subarea", "pk": 318, "fields": {"name": "بيت فجار", "code": 318, "area": 6}}, {"model": "api.subarea", "pk": 319, "fields": {"name": "تقوع", "code": 319, "area": 6}}, {"model": "api.subarea", "pk": 320, "fields": {"name": "ج<PERSON> الذيب", "code": 320, "area": 6}}, {"model": "api.subarea", "pk": 321, "fields": {"name": "جناتا", "code": 321, "area": 6}}, {"model": "api.subarea", "pk": 322, "fields": {"name": "جورة الشمعة", "code": 322, "area": 6}}, {"model": "api.subarea", "pk": 323, "fields": {"name": "حوسان", "code": 323, "area": 6}}, {"model": "api.subarea", "pk": 324, "fields": {"name": "خربة تقوع", "code": 324, "area": 6}}, {"model": "api.subarea", "pk": 325, "fields": {"name": "<PERSON>لة الحداد", "code": 325, "area": 6}}, {"model": "api.subarea", "pk": 326, "fields": {"name": "خلة اللوزة", "code": 326, "area": 6}}, {"model": "api.subarea", "pk": 327, "fields": {"name": "خلة النعمان", "code": 327, "area": 6}}, {"model": "api.subarea", "pk": 328, "fields": {"name": "<PERSON>لة حمامة", "code": 328, "area": 6}}, {"model": "api.subarea", "pk": 329, "fields": {"name": "خلة سكاريا", "code": 329, "area": 6}}, {"model": "api.subarea", "pk": 330, "fields": {"name": "دار صلاح", "code": 330, "area": 6}}, {"model": "api.subarea", "pk": 331, "fields": {"name": "زعترة", "code": 331, "area": 6}}, {"model": "api.subarea", "pk": 332, "fields": {"name": "عرب الرشايدة", "code": 332, "area": 6}}, {"model": "api.subarea", "pk": 333, "fields": {"name": "كيسان", "code": 333, "area": 6}}, {"model": "api.subarea", "pk": 334, "fields": {"name": "مراح رباح", "code": 334, "area": 6}}, {"model": "api.subarea", "pk": 335, "fields": {"name": "مراح معلا", "code": 335, "area": 6}}, {"model": "api.subarea", "pk": 336, "fields": {"name": "نحالين", "code": 336, "area": 6}}, {"model": "api.subarea", "pk": 337, "fields": {"name": "هندازة وبريضعة", "code": 337, "area": 6}}, {"model": "api.subarea", "pk": 338, "fields": {"name": "وادي النيص", "code": 338, "area": 6}}, {"model": "api.subarea", "pk": 339, "fields": {"name": "وادي امحيميد", "code": 339, "area": 6}}, {"model": "api.subarea", "pk": 340, "fields": {"name": "وادي رحال", "code": 340, "area": 6}}, {"model": "api.subarea", "pk": 341, "fields": {"name": "وادي فوكين", "code": 341, "area": 6}}, {"model": "api.subarea", "pk": 342, "fields": {"name": "ارطاس", "code": 342, "area": 6}}, {"model": "api.subarea", "pk": 343, "fields": {"name": "الخضر", "code": 343, "area": 6}}, {"model": "api.subarea", "pk": 344, "fields": {"name": "الدوحة", "code": 344, "area": 6}}, {"model": "api.subarea", "pk": 345, "fields": {"name": "بئر عونة", "code": 345, "area": 6}}, {"model": "api.subarea", "pk": 346, "fields": {"name": "بيت جالا", "code": 346, "area": 6}}, {"model": "api.subarea", "pk": 347, "fields": {"name": "بيت ساحور", "code": 347, "area": 6}}, {"model": "api.subarea", "pk": 348, "fields": {"name": "شارع القدس الخليل", "code": 348, "area": 6}}, {"model": "api.subarea", "pk": 349, "fields": {"name": "مخيم الدهيشة", "code": 349, "area": 6}}, {"model": "api.subarea", "pk": 350, "fields": {"name": "م<PERSON>ي<PERSON> العزة", "code": 350, "area": 6}}, {"model": "api.subarea", "pk": 351, "fields": {"name": "مخيم عايدة", "code": 351, "area": 6}}, {"model": "api.subarea", "pk": 352, "fields": {"name": "مدينة بيت لحم", "code": 352, "area": 6}}, {"model": "api.subarea", "pk": 353, "fields": {"name": "دوار ارارات", "code": 353, "area": 6}}, {"model": "api.subarea", "pk": 354, "fields": {"name": "شارع الجبل", "code": 354, "area": 6}}, {"model": "api.subarea", "pk": 355, "fields": {"name": "شارع الكركفة", "code": 355, "area": 6}}, {"model": "api.subarea", "pk": 356, "fields": {"name": "شارع المهد", "code": 356, "area": 6}}, {"model": "api.subarea", "pk": 357, "fields": {"name": "واد شاهين", "code": 357, "area": 6}}, {"model": "api.subarea", "pk": 358, "fields": {"name": "واد معالي", "code": 358, "area": 6}}, {"model": "api.subarea", "pk": 359, "fields": {"name": "واد معالي-شارع الصف", "code": 359, "area": 6}}, {"model": "api.subarea", "pk": 360, "fields": {"name": "وسط البلد", "code": 360, "area": 6}}, {"model": "api.subarea", "pk": 361, "fields": {"name": "الجديدة", "code": 361, "area": 7}}, {"model": "api.subarea", "pk": 362, "fields": {"name": "الجلمة", "code": 362, "area": 7}}, {"model": "api.subarea", "pk": 363, "fields": {"name": "الحارة الشرقية", "code": 363, "area": 7}}, {"model": "api.subarea", "pk": 364, "fields": {"name": "الخلجان", "code": 364, "area": 7}}, {"model": "api.subarea", "pk": 365, "fields": {"name": "الرامة", "code": 365, "area": 7}}, {"model": "api.subarea", "pk": 366, "fields": {"name": "الزاوية", "code": 366, "area": 7}}, {"model": "api.subarea", "pk": 367, "fields": {"name": "الزبابدة", "code": 367, "area": 7}}, {"model": "api.subarea", "pk": 368, "fields": {"name": "الشهداء", "code": 368, "area": 7}}, {"model": "api.subarea", "pk": 369, "fields": {"name": "الضمايرة", "code": 369, "area": 7}}, {"model": "api.subarea", "pk": 370, "fields": {"name": "الطرم", "code": 370, "area": 7}}, {"model": "api.subarea", "pk": 371, "fields": {"name": "الطيبة", "code": 371, "area": 7}}, {"model": "api.subarea", "pk": 372, "fields": {"name": "العرقة", "code": 372, "area": 7}}, {"model": "api.subarea", "pk": 373, "fields": {"name": "العصاعصة", "code": 373, "area": 7}}, {"model": "api.subarea", "pk": 374, "fields": {"name": "العطارة", "code": 374, "area": 7}}, {"model": "api.subarea", "pk": 375, "fields": {"name": "الفندقومية", "code": 375, "area": 7}}, {"model": "api.subarea", "pk": 376, "fields": {"name": "المطلة", "code": 376, "area": 7}}, {"model": "api.subarea", "pk": 377, "fields": {"name": "المغير", "code": 377, "area": 7}}, {"model": "api.subarea", "pk": 378, "fields": {"name": "المنصورة", "code": 378, "area": 7}}, {"model": "api.subarea", "pk": 379, "fields": {"name": "الهاشمية", "code": 379, "area": 7}}, {"model": "api.subarea", "pk": 380, "fields": {"name": "اليامون", "code": 380, "area": 7}}, {"model": "api.subarea", "pk": 381, "fields": {"name": "أم التوت", "code": 381, "area": 7}}, {"model": "api.subarea", "pk": 382, "fields": {"name": "أ<PERSON> الريحان", "code": 382, "area": 7}}, {"model": "api.subarea", "pk": 383, "fields": {"name": "أم دار", "code": 383, "area": 7}}, {"model": "api.subarea", "pk": 384, "fields": {"name": "إمريحة", "code": 384, "area": 7}}, {"model": "api.subarea", "pk": 385, "fields": {"name": "برطعة (ضفة)", "code": 385, "area": 7}}, {"model": "api.subarea", "pk": 386, "fields": {"name": "برقين", "code": 386, "area": 7}}, {"model": "api.subarea", "pk": 387, "fields": {"name": "بيت قاد (الجنوبي)", "code": 387, "area": 7}}, {"model": "api.subarea", "pk": 388, "fields": {"name": "بير الباشا", "code": 388, "area": 7}}, {"model": "api.subarea", "pk": 389, "fields": {"name": "تعنك", "code": 389, "area": 7}}, {"model": "api.subarea", "pk": 390, "fields": {"name": "تنين", "code": 390, "area": 7}}, {"model": "api.subarea", "pk": 391, "fields": {"name": "جبع", "code": 391, "area": 7}}, {"model": "api.subarea", "pk": 392, "fields": {"name": "جلبون", "code": 392, "area": 7}}, {"model": "api.subarea", "pk": 393, "fields": {"name": "جلقموس", "code": 393, "area": 7}}, {"model": "api.subarea", "pk": 394, "fields": {"name": "حي الزهراء", "code": 394, "area": 7}}, {"model": "api.subarea", "pk": 395, "fields": {"name": "خربة عبد الله اليونس", "code": 395, "area": 7}}, {"model": "api.subarea", "pk": 396, "fields": {"name": "دير ابو ضعيف", "code": 396, "area": 7}}, {"model": "api.subarea", "pk": 397, "fields": {"name": "دير غزالة", "code": 397, "area": 7}}, {"model": "api.subarea", "pk": 398, "fields": {"name": "رابا", "code": 398, "area": 7}}, {"model": "api.subarea", "pk": 399, "fields": {"name": "رمانة", "code": 399, "area": 7}}, {"model": "api.subarea", "pk": 400, "fields": {"name": "زبدة", "code": 400, "area": 7}}, {"model": "api.subarea", "pk": 401, "fields": {"name": "زبوبا", "code": 401, "area": 7}}, {"model": "api.subarea", "pk": 402, "fields": {"name": "سيريس", "code": 402, "area": 7}}, {"model": "api.subarea", "pk": 403, "fields": {"name": "سيلة الحارثية", "code": 403, "area": 7}}, {"model": "api.subarea", "pk": 404, "fields": {"name": "سيلة الظهر", "code": 404, "area": 7}}, {"model": "api.subarea", "pk": 405, "fields": {"name": "صانور", "code": 405, "area": 7}}, {"model": "api.subarea", "pk": 406, "fields": {"name": "صير", "code": 406, "area": 7}}, {"model": "api.subarea", "pk": 407, "fields": {"name": "طورة الشرقية", "code": 407, "area": 7}}, {"model": "api.subarea", "pk": 408, "fields": {"name": "طورة الغربية", "code": 408, "area": 7}}, {"model": "api.subarea", "pk": 409, "fields": {"name": "<PERSON><PERSON><PERSON> العبد", "code": 409, "area": 7}}, {"model": "api.subarea", "pk": 410, "fields": {"name": "<PERSON><PERSON><PERSON> المالح", "code": 410, "area": 7}}, {"model": "api.subarea", "pk": 411, "fields": {"name": "عابا (الغربية)", "code": 411, "area": 7}}, {"model": "api.subarea", "pk": 412, "fields": {"name": "عانين", "code": 412, "area": 7}}, {"model": "api.subarea", "pk": 413, "fields": {"name": "عجة", "code": 413, "area": 7}}, {"model": "api.subarea", "pk": 414, "fields": {"name": "عرابة", "code": 414, "area": 7}}, {"model": "api.subarea", "pk": 415, "fields": {"name": "عرانة", "code": 415, "area": 7}}, {"model": "api.subarea", "pk": 416, "fields": {"name": "عربونة", "code": 416, "area": 7}}, {"model": "api.subarea", "pk": 417, "fields": {"name": "عنزة", "code": 417, "area": 7}}, {"model": "api.subarea", "pk": 418, "fields": {"name": "فحمة", "code": 418, "area": 7}}, {"model": "api.subarea", "pk": 419, "fields": {"name": "فحمة الجديدة", "code": 419, "area": 7}}, {"model": "api.subarea", "pk": 420, "fields": {"name": "فقوعة", "code": 420, "area": 7}}, {"model": "api.subarea", "pk": 421, "fields": {"name": "قباطية", "code": 421, "area": 7}}, {"model": "api.subarea", "pk": 422, "fields": {"name": "كفر دان", "code": 422, "area": 7}}, {"model": "api.subarea", "pk": 423, "fields": {"name": "كفر راعي", "code": 423, "area": 7}}, {"model": "api.subarea", "pk": 424, "fields": {"name": "<PERSON><PERSON><PERSON> قود", "code": 424, "area": 7}}, {"model": "api.subarea", "pk": 425, "fields": {"name": "كفيرت", "code": 425, "area": 7}}, {"model": "api.subarea", "pk": 426, "fields": {"name": "م<PERSON>ي<PERSON> جنين", "code": 426, "area": 7}}, {"model": "api.subarea", "pk": 427, "fields": {"name": "مدينة جنين", "code": 427, "area": 7}}, {"model": "api.subarea", "pk": 428, "fields": {"name": "الجابريات", "code": 428, "area": 7}}, {"model": "api.subarea", "pk": 429, "fields": {"name": "الجامعة الامريكية-جنين", "code": 429, "area": 7}}, {"model": "api.subarea", "pk": 430, "fields": {"name": "الحي الشرقي", "code": 430, "area": 7}}, {"model": "api.subarea", "pk": 431, "fields": {"name": "الحي الغربي", "code": 431, "area": 7}}, {"model": "api.subarea", "pk": 432, "fields": {"name": "السويطات", "code": 432, "area": 7}}, {"model": "api.subarea", "pk": 433, "fields": {"name": "المغشية", "code": 433, "area": 7}}, {"model": "api.subarea", "pk": 434, "fields": {"name": "المنطقة الصناعية -جنين", "code": 434, "area": 7}}, {"model": "api.subarea", "pk": 435, "fields": {"name": "جنين البلدة القديمة", "code": 435, "area": 7}}, {"model": "api.subarea", "pk": 436, "fields": {"name": "الدوار", "code": 436, "area": 7}}, {"model": "api.subarea", "pk": 437, "fields": {"name": "وسط البلد", "code": 437, "area": 7}}, {"model": "api.subarea", "pk": 438, "fields": {"name": "<PERSON>ي البساتين", "code": 438, "area": 7}}, {"model": "api.subarea", "pk": 439, "fields": {"name": "<PERSON><PERSON> الهدف", "code": 439, "area": 7}}, {"model": "api.subarea", "pk": 440, "fields": {"name": "خروبة", "code": 440, "area": 7}}, {"model": "api.subarea", "pk": 441, "fields": {"name": "سهل مرج بن عامر", "code": 441, "area": 7}}, {"model": "api.subarea", "pk": 442, "fields": {"name": "شارع أبو بكر", "code": 442, "area": 7}}, {"model": "api.subarea", "pk": 443, "fields": {"name": "شارع الجامعة", "code": 443, "area": 7}}, {"model": "api.subarea", "pk": 444, "fields": {"name": "شارع الحسبة-جنين", "code": 444, "area": 7}}, {"model": "api.subarea", "pk": 445, "fields": {"name": "شارع الملك فيصل-جنين", "code": 445, "area": 7}}, {"model": "api.subarea", "pk": 446, "fields": {"name": "شارع الناصرة جنين", "code": 446, "area": 7}}, {"model": "api.subarea", "pk": 447, "fields": {"name": "شارع حيفا جنين", "code": 447, "area": 7}}, {"model": "api.subarea", "pk": 448, "fields": {"name": "شارع نابلس جنين", "code": 448, "area": 7}}, {"model": "api.subarea", "pk": 449, "fields": {"name": "شارع يحيى عياش", "code": 449, "area": 7}}, {"model": "api.subarea", "pk": 450, "fields": {"name": "ضاحية الجنان", "code": 450, "area": 7}}, {"model": "api.subarea", "pk": 451, "fields": {"name": "ضاحية الزيتونة", "code": 451, "area": 7}}, {"model": "api.subarea", "pk": 452, "fields": {"name": "كفريات", "code": 452, "area": 7}}, {"model": "api.subarea", "pk": 453, "fields": {"name": "مركة", "code": 453, "area": 7}}, {"model": "api.subarea", "pk": 454, "fields": {"name": "مسلية", "code": 454, "area": 7}}, {"model": "api.subarea", "pk": 455, "fields": {"name": "مشروع بيت قاد (الشمالي)", "code": 455, "area": 7}}, {"model": "api.subarea", "pk": 456, "fields": {"name": "ميثلون", "code": 456, "area": 7}}, {"model": "api.subarea", "pk": 457, "fields": {"name": "نزلة الشيخ زيد", "code": 457, "area": 7}}, {"model": "api.subarea", "pk": 458, "fields": {"name": "وادي الضبع", "code": 458, "area": 7}}, {"model": "api.subarea", "pk": 459, "fields": {"name": "وادي دعوق", "code": 459, "area": 7}}, {"model": "api.subarea", "pk": 460, "fields": {"name": "ي<PERSON><PERSON><PERSON>", "code": 460, "area": 7}}, {"model": "api.subarea", "pk": 461, "fields": {"name": "أبو شخيدم", "code": 461, "area": 8}}, {"model": "api.subarea", "pk": 462, "fields": {"name": "أبو قش", "code": 462, "area": 8}}, {"model": "api.subarea", "pk": 463, "fields": {"name": "اسكان الشرطة", "code": 463, "area": 8}}, {"model": "api.subarea", "pk": 464, "fields": {"name": "البيرة", "code": 464, "area": 8}}, {"model": "api.subarea", "pk": 465, "fields": {"name": "الجانية", "code": 465, "area": 8}}, {"model": "api.subarea", "pk": 466, "fields": {"name": "الجديرة", "code": 466, "area": 8}}, {"model": "api.subarea", "pk": 467, "fields": {"name": "الجيب", "code": 467, "area": 8}}, {"model": "api.subarea", "pk": 468, "fields": {"name": "الحي الدبلوماسي", "code": 468, "area": 8}}, {"model": "api.subarea", "pk": 469, "fields": {"name": "الطيبة", "code": 469, "area": 8}}, {"model": "api.subarea", "pk": 470, "fields": {"name": "القبيبة", "code": 470, "area": 8}}, {"model": "api.subarea", "pk": 471, "fields": {"name": "اللبن الغربي", "code": 471, "area": 8}}, {"model": "api.subarea", "pk": 472, "fields": {"name": "المدية", "code": 472, "area": 8}}, {"model": "api.subarea", "pk": 473, "fields": {"name": "المزرعة الشرقية", "code": 473, "area": 8}}, {"model": "api.subarea", "pk": 474, "fields": {"name": "المزرعة الغربية", "code": 474, "area": 8}}, {"model": "api.subarea", "pk": 475, "fields": {"name": "المغير", "code": 475, "area": 8}}, {"model": "api.subarea", "pk": 476, "fields": {"name": "النبي صالح", "code": 476, "area": 8}}, {"model": "api.subarea", "pk": 477, "fields": {"name": "أم صفا", "code": 477, "area": 8}}, {"model": "api.subarea", "pk": 478, "fields": {"name": "بدرس", "code": 478, "area": 8}}, {"model": "api.subarea", "pk": 479, "fields": {"name": "بدو", "code": 479, "area": 8}}, {"model": "api.subarea", "pk": 480, "fields": {"name": "برقة", "code": 480, "area": 8}}, {"model": "api.subarea", "pk": 481, "fields": {"name": "برهام", "code": 481, "area": 8}}, {"model": "api.subarea", "pk": 482, "fields": {"name": "بلعين", "code": 482, "area": 8}}, {"model": "api.subarea", "pk": 483, "fields": {"name": "بيت اجزا", "code": 483, "area": 8}}, {"model": "api.subarea", "pk": 484, "fields": {"name": "بيت اكسا", "code": 484, "area": 8}}, {"model": "api.subarea", "pk": 485, "fields": {"name": "بيت حنينا-ضفة", "code": 485, "area": 8}}, {"model": "api.subarea", "pk": 486, "fields": {"name": "بيت دقو", "code": 486, "area": 8}}, {"model": "api.subarea", "pk": 487, "fields": {"name": "بيت ريما", "code": 487, "area": 8}}, {"model": "api.subarea", "pk": 488, "fields": {"name": "بيت سوريك", "code": 488, "area": 8}}, {"model": "api.subarea", "pk": 489, "fields": {"name": "بيت سيرا", "code": 489, "area": 8}}, {"model": "api.subarea", "pk": 490, "fields": {"name": "بيت عنان", "code": 490, "area": 8}}, {"model": "api.subarea", "pk": 491, "fields": {"name": "بيت عور التحتا", "code": 491, "area": 8}}, {"model": "api.subarea", "pk": 492, "fields": {"name": "بيت عور الفوقا", "code": 492, "area": 8}}, {"model": "api.subarea", "pk": 493, "fields": {"name": "بيت لقيا", "code": 493, "area": 8}}, {"model": "api.subarea", "pk": 494, "fields": {"name": "بيتللو", "code": 494, "area": 8}}, {"model": "api.subarea", "pk": 495, "fields": {"name": "بيتين", "code": 495, "area": 8}}, {"model": "api.subarea", "pk": 496, "fields": {"name": "بيرزيت", "code": 496, "area": 8}}, {"model": "api.subarea", "pk": 497, "fields": {"name": "بير نبالا", "code": 497, "area": 8}}, {"model": "api.subarea", "pk": 498, "fields": {"name": "ترمسعيا", "code": 498, "area": 8}}, {"model": "api.subarea", "pk": 499, "fields": {"name": "جفنا", "code": 499, "area": 8}}, {"model": "api.subarea", "pk": 500, "fields": {"name": "جلجيليا", "code": 500, "area": 8}}, {"model": "api.subarea", "pk": 501, "fields": {"name": "جمالة", "code": 501, "area": 8}}, {"model": "api.subarea", "pk": 502, "fields": {"name": "جيبيا", "code": 502, "area": 8}}, {"model": "api.subarea", "pk": 503, "fields": {"name": "خربة أبو فلاح", "code": 503, "area": 8}}, {"model": "api.subarea", "pk": 504, "fields": {"name": "خربثا المصباح", "code": 504, "area": 8}}, {"model": "api.subarea", "pk": 505, "fields": {"name": "خربثا بني حارث", "code": 505, "area": 8}}, {"model": "api.subarea", "pk": 506, "fields": {"name": "دورا القرع", "code": 506, "area": 8}}, {"model": "api.subarea", "pk": 507, "fields": {"name": "دير إبزيع", "code": 507, "area": 8}}, {"model": "api.subarea", "pk": 508, "fields": {"name": "دير ابزيع", "code": 508, "area": 8}}, {"model": "api.subarea", "pk": 509, "fields": {"name": "دير أبو مشعل", "code": 509, "area": 8}}, {"model": "api.subarea", "pk": 510, "fields": {"name": "دير السودان", "code": 510, "area": 8}}, {"model": "api.subarea", "pk": 511, "fields": {"name": "دير جرير", "code": 511, "area": 8}}, {"model": "api.subarea", "pk": 512, "fields": {"name": "دير دبوان", "code": 512, "area": 8}}, {"model": "api.subarea", "pk": 513, "fields": {"name": "دير عمار", "code": 513, "area": 8}}, {"model": "api.subarea", "pk": 514, "fields": {"name": "دير غسانة", "code": 514, "area": 8}}, {"model": "api.subarea", "pk": 515, "fields": {"name": "دير قديس", "code": 515, "area": 8}}, {"model": "api.subarea", "pk": 516, "fields": {"name": "دير نظام", "code": 516, "area": 8}}, {"model": "api.subarea", "pk": 517, "fields": {"name": "راس كركر", "code": 517, "area": 8}}, {"model": "api.subarea", "pk": 518, "fields": {"name": "رافات", "code": 518, "area": 8}}, {"model": "api.subarea", "pk": 519, "fields": {"name": "رمون", "code": 519, "area": 8}}, {"model": "api.subarea", "pk": 520, "fields": {"name": "رنتيس", "code": 520, "area": 8}}, {"model": "api.subarea", "pk": 521, "fields": {"name": "روابي", "code": 521, "area": 8}}, {"model": "api.subarea", "pk": 522, "fields": {"name": "سردا", "code": 522, "area": 8}}, {"model": "api.subarea", "pk": 523, "fields": {"name": "سلواد", "code": 523, "area": 8}}, {"model": "api.subarea", "pk": 524, "fields": {"name": "سنجل", "code": 524, "area": 8}}, {"model": "api.subarea", "pk": 525, "fields": {"name": "شبتين", "code": 525, "area": 8}}, {"model": "api.subarea", "pk": 526, "fields": {"name": "شقبا", "code": 526, "area": 8}}, {"model": "api.subarea", "pk": 527, "fields": {"name": "صفا", "code": 527, "area": 8}}, {"model": "api.subarea", "pk": 528, "fields": {"name": "ضاحية الاتحاد", "code": 528, "area": 8}}, {"model": "api.subarea", "pk": 529, "fields": {"name": "ضاحية التربية والتعليم", "code": 529, "area": 8}}, {"model": "api.subarea", "pk": 530, "fields": {"name": "ضاحية الريحان", "code": 530, "area": 8}}, {"model": "api.subarea", "pk": 531, "fields": {"name": "ضاحية السنديان", "code": 531, "area": 8}}, {"model": "api.subarea", "pk": 532, "fields": {"name": "ضاحية الغدير", "code": 532, "area": 8}}, {"model": "api.subarea", "pk": 533, "fields": {"name": "ضاحية النجمة", "code": 533, "area": 8}}, {"model": "api.subarea", "pk": 534, "fields": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "code": 534, "area": 8}}, {"model": "api.subarea", "pk": 535, "fields": {"name": "عارورة", "code": 535, "area": 8}}, {"model": "api.subarea", "pk": 536, "fields": {"name": "عبوين", "code": 536, "area": 8}}, {"model": "api.subarea", "pk": 537, "fields": {"name": "عجول", "code": 537, "area": 8}}, {"model": "api.subarea", "pk": 538, "fields": {"name": "عطارة", "code": 538, "area": 8}}, {"model": "api.subarea", "pk": 539, "fields": {"name": "عين سينيا", "code": 539, "area": 8}}, {"model": "api.subarea", "pk": 540, "fields": {"name": "عين عريك", "code": 540, "area": 8}}, {"model": "api.subarea", "pk": 541, "fields": {"name": "عين قينيا", "code": 541, "area": 8}}, {"model": "api.subarea", "pk": 542, "fields": {"name": "عين يبرود", "code": 542, "area": 8}}, {"model": "api.subarea", "pk": 543, "fields": {"name": "قبيا", "code": 543, "area": 8}}, {"model": "api.subarea", "pk": 544, "fields": {"name": "قراوة بني زيد", "code": 544, "area": 8}}, {"model": "api.subarea", "pk": 545, "fields": {"name": "قرية الطيرة", "code": 545, "area": 8}}, {"model": "api.subarea", "pk": 546, "fields": {"name": "قطنة", "code": 546, "area": 8}}, {"model": "api.subarea", "pk": 547, "fields": {"name": "قلنديا البلد", "code": 547, "area": 8}}, {"model": "api.subarea", "pk": 548, "fields": {"name": "<PERSON><PERSON>ر عين", "code": 548, "area": 8}}, {"model": "api.subarea", "pk": 549, "fields": {"name": "ك<PERSON>ر مالك", "code": 549, "area": 8}}, {"model": "api.subarea", "pk": 550, "fields": {"name": "كفر نعمة", "code": 550, "area": 8}}, {"model": "api.subarea", "pk": 551, "fields": {"name": "كوبر", "code": 551, "area": 8}}, {"model": "api.subarea", "pk": 552, "fields": {"name": "م<PERSON>يم الجلزون", "code": 552, "area": 8}}, {"model": "api.subarea", "pk": 553, "fields": {"name": "مزارع النوباني", "code": 553, "area": 8}}, {"model": "api.subarea", "pk": 554, "fields": {"name": "نعلين", "code": 554, "area": 8}}, {"model": "api.subarea", "pk": 555, "fields": {"name": "يب<PERSON><PERSON>د", "code": 555, "area": 8}}, {"model": "api.subarea", "pk": 556, "fields": {"name": "مدينة رام الله", "code": 556, "area": 8}}, {"model": "api.subarea", "pk": 557, "fields": {"name": "الارسال", "code": 557, "area": 8}}, {"model": "api.subarea", "pk": 558, "fields": {"name": "الشرفة", "code": 558, "area": 8}}, {"model": "api.subarea", "pk": 559, "fields": {"name": "الصناعية البيرة", "code": 559, "area": 8}}, {"model": "api.subarea", "pk": 560, "fields": {"name": "الصناعية بيتونيا", "code": 560, "area": 8}}, {"model": "api.subarea", "pk": 561, "fields": {"name": "الطيرة", "code": 561, "area": 8}}, {"model": "api.subarea", "pk": 562, "fields": {"name": "الماصيون", "code": 562, "area": 8}}, {"model": "api.subarea", "pk": 563, "fields": {"name": "المصايف", "code": 563, "area": 8}}, {"model": "api.subarea", "pk": 564, "fields": {"name": "ام الشرايط", "code": 564, "area": 8}}, {"model": "api.subarea", "pk": 565, "fields": {"name": "بالوع البيرة", "code": 565, "area": 8}}, {"model": "api.subarea", "pk": 566, "fields": {"name": "بالوع بيتونيا", "code": 566, "area": 8}}, {"model": "api.subarea", "pk": 567, "fields": {"name": "بطن الهوى", "code": 567, "area": 8}}, {"model": "api.subarea", "pk": 568, "fields": {"name": "بيتونيا", "code": 568, "area": 8}}, {"model": "api.subarea", "pk": 569, "fields": {"name": "بيتونيا الخبطة", "code": 569, "area": 8}}, {"model": "api.subarea", "pk": 570, "fields": {"name": "بيتونيا راس يعقوب", "code": 570, "area": 8}}, {"model": "api.subarea", "pk": 571, "fields": {"name": "<PERSON><PERSON> الجنان", "code": 571, "area": 8}}, {"model": "api.subarea", "pk": 572, "fields": {"name": "دوار الساعة", "code": 572, "area": 8}}, {"model": "api.subarea", "pk": 573, "fields": {"name": "رام الله التحتا", "code": 573, "area": 8}}, {"model": "api.subarea", "pk": 574, "fields": {"name": "رام الله دوار المنارة", "code": 574, "area": 8}}, {"model": "api.subarea", "pk": 575, "fields": {"name": "رام الله شارع الحسبه", "code": 575, "area": 8}}, {"model": "api.subarea", "pk": 576, "fields": {"name": "رام الله شارع القدس", "code": 576, "area": 8}}, {"model": "api.subarea", "pk": 577, "fields": {"name": "رام الله شارع نابلس", "code": 577, "area": 8}}, {"model": "api.subarea", "pk": 578, "fields": {"name": "رام الله وسط البلد", "code": 578, "area": 8}}, {"model": "api.subarea", "pk": 579, "fields": {"name": "سطح مرحبا", "code": 579, "area": 8}}, {"model": "api.subarea", "pk": 580, "fields": {"name": "شارع البكري", "code": 580, "area": 8}}, {"model": "api.subarea", "pk": 581, "fields": {"name": "شارع المستشفى", "code": 581, "area": 8}}, {"model": "api.subarea", "pk": 582, "fields": {"name": "شارع ركب", "code": 582, "area": 8}}, {"model": "api.subarea", "pk": 583, "fields": {"name": "<PERSON>ين مصباح", "code": 583, "area": 8}}, {"model": "api.subarea", "pk": 584, "fields": {"name": "<PERSON>ي<PERSON> منجد", "code": 584, "area": 8}}, {"model": "api.subarea", "pk": 585, "fields": {"name": "مخيم الامعري", "code": 585, "area": 8}}, {"model": "api.subarea", "pk": 586, "fields": {"name": "م<PERSON>يم قدورة", "code": 586, "area": 8}}, {"model": "api.subarea", "pk": 587, "fields": {"name": "مفرق 17", "code": 587, "area": 8}}, {"model": "api.subarea", "pk": 588, "fields": {"name": "إسكاكا", "code": 588, "area": 9}}, {"model": "api.subarea", "pk": 589, "fields": {"name": "بديا", "code": 589, "area": 9}}, {"model": "api.subarea", "pk": 590, "fields": {"name": "بروقين", "code": 590, "area": 9}}, {"model": "api.subarea", "pk": 591, "fields": {"name": "حار<PERSON>", "code": 591, "area": 9}}, {"model": "api.subarea", "pk": 592, "fields": {"name": "خربة قيس", "code": 592, "area": 9}}, {"model": "api.subarea", "pk": 593, "fields": {"name": "دير استيا", "code": 593, "area": 9}}, {"model": "api.subarea", "pk": 594, "fields": {"name": "دير بلوط", "code": 594, "area": 9}}, {"model": "api.subarea", "pk": 595, "fields": {"name": "رافات", "code": 595, "area": 9}}, {"model": "api.subarea", "pk": 596, "fields": {"name": "سرطة", "code": 596, "area": 9}}, {"model": "api.subarea", "pk": 597, "fields": {"name": "سلفيت الزاوية", "code": 597, "area": 9}}, {"model": "api.subarea", "pk": 598, "fields": {"name": "سلفيت وسط البلد", "code": 598, "area": 9}}, {"model": "api.subarea", "pk": 599, "fields": {"name": "فرخة", "code": 599, "area": 9}}, {"model": "api.subarea", "pk": 600, "fields": {"name": "قراوة بني حسان", "code": 600, "area": 9}}, {"model": "api.subarea", "pk": 601, "fields": {"name": "قيرة", "code": 601, "area": 9}}, {"model": "api.subarea", "pk": 602, "fields": {"name": "ك<PERSON><PERSON> الديك", "code": 602, "area": 9}}, {"model": "api.subarea", "pk": 603, "fields": {"name": "ك<PERSON><PERSON> حارس", "code": 603, "area": 9}}, {"model": "api.subarea", "pk": 604, "fields": {"name": "مردا", "code": 604, "area": 9}}, {"model": "api.subarea", "pk": 605, "fields": {"name": "مسحة", "code": 605, "area": 9}}, {"model": "api.subarea", "pk": 606, "fields": {"name": "ياسوف", "code": 606, "area": 9}}, {"model": "api.subarea", "pk": 607, "fields": {"name": "ابو ديس", "code": 607, "area": 10}}, {"model": "api.subarea", "pk": 608, "fields": {"name": "ال<PERSON><PERSON> الاحمر", "code": 608, "area": 10}}, {"model": "api.subarea", "pk": 609, "fields": {"name": "الرام", "code": 609, "area": 10}}, {"model": "api.subarea", "pk": 610, "fields": {"name": "الزعيم", "code": 610, "area": 10}}, {"model": "api.subarea", "pk": 611, "fields": {"name": "السواحرة الشرقية", "code": 611, "area": 10}}, {"model": "api.subarea", "pk": 612, "fields": {"name": "السواحرة الشرقية-الشيخ سعد (ضفة)", "code": 612, "area": 10}}, {"model": "api.subarea", "pk": 613, "fields": {"name": "العيزرية", "code": 613, "area": 10}}, {"model": "api.subarea", "pk": 614, "fields": {"name": "جب<PERSON>(الرام)", "code": 614, "area": 10}}, {"model": "api.subarea", "pk": 615, "fields": {"name": "حزما", "code": 615, "area": 10}}, {"model": "api.subarea", "pk": 616, "fields": {"name": "عناتا", "code": 616, "area": 10}}, {"model": "api.subarea", "pk": 617, "fields": {"name": "<PERSON><PERSON><PERSON> عقب", "code": 617, "area": 10}}, {"model": "api.subarea", "pk": 618, "fields": {"name": "مخماس", "code": 618, "area": 10}}, {"model": "api.subarea", "pk": 619, "fields": {"name": "مخيم شعفاط", "code": 619, "area": 10}}, {"model": "api.subarea", "pk": 620, "fields": {"name": "مخيم شعفاط-را<PERSON> خميس", "code": 620, "area": 10}}, {"model": "api.subarea", "pk": 621, "fields": {"name": "مخيم شعفاط-را<PERSON> شحادة", "code": 621, "area": 10}}, {"model": "api.subarea", "pk": 622, "fields": {"name": "م<PERSON>يم قلنديا", "code": 622, "area": 10}}, {"model": "api.subarea", "pk": 623, "fields": {"name": "نزول الكسارات", "code": 623, "area": 10}}, {"model": "api.subarea", "pk": 624, "fields": {"name": "عينون طوباس", "code": 624, "area": 11}}, {"model": "api.subarea", "pk": 625, "fields": {"name": "الحاوز", "code": 625, "area": 11}}, {"model": "api.subarea", "pk": 626, "fields": {"name": "بردلة", "code": 626, "area": 11}}, {"model": "api.subarea", "pk": 627, "fields": {"name": "تياسير", "code": 627, "area": 11}}, {"model": "api.subarea", "pk": 628, "fields": {"name": "شارع مسجد الشهيد", "code": 628, "area": 11}}, {"model": "api.subarea", "pk": 629, "fields": {"name": "طمون", "code": 629, "area": 11}}, {"model": "api.subarea", "pk": 630, "fields": {"name": "طوباس وسط البلد", "code": 630, "area": 11}}, {"model": "api.subarea", "pk": 631, "fields": {"name": "عقابا", "code": 631, "area": 11}}, {"model": "api.subarea", "pk": 632, "fields": {"name": "قشدة", "code": 632, "area": 11}}, {"model": "api.subarea", "pk": 633, "fields": {"name": "كردلا", "code": 633, "area": 11}}, {"model": "api.subarea", "pk": 634, "fields": {"name": "مخيم الفارعة", "code": 634, "area": 11}}, {"model": "api.subarea", "pk": 635, "fields": {"name": "واد الفارعة", "code": 635, "area": 11}}, {"model": "api.subarea", "pk": 636, "fields": {"name": "الحارة الشرقية", "code": 636, "area": 12}}, {"model": "api.subarea", "pk": 637, "fields": {"name": "إكتابا", "code": 637, "area": 12}}, {"model": "api.subarea", "pk": 638, "fields": {"name": "الجاروشية", "code": 638, "area": 12}}, {"model": "api.subarea", "pk": 639, "fields": {"name": "الحسبه", "code": 639, "area": 12}}, {"model": "api.subarea", "pk": 640, "fields": {"name": "الحفاصي", "code": 640, "area": 12}}, {"model": "api.subarea", "pk": 641, "fields": {"name": "الراس", "code": 641, "area": 12}}, {"model": "api.subarea", "pk": 642, "fields": {"name": "المسقوفة", "code": 642, "area": 12}}, {"model": "api.subarea", "pk": 643, "fields": {"name": "النزلة الشرقية", "code": 643, "area": 12}}, {"model": "api.subarea", "pk": 644, "fields": {"name": "النزلة الغربية", "code": 644, "area": 12}}, {"model": "api.subarea", "pk": 645, "fields": {"name": "النزلة الوسطى", "code": 645, "area": 12}}, {"model": "api.subarea", "pk": 646, "fields": {"name": "باقة الشرقية", "code": 646, "area": 12}}, {"model": "api.subarea", "pk": 647, "fields": {"name": "بلعا", "code": 647, "area": 12}}, {"model": "api.subarea", "pk": 648, "fields": {"name": "بيت ليد", "code": 648, "area": 12}}, {"model": "api.subarea", "pk": 649, "fields": {"name": "جامعة القدس المفتوحة", "code": 649, "area": 12}}, {"model": "api.subarea", "pk": 650, "fields": {"name": "حي الصوانة", "code": 650, "area": 12}}, {"model": "api.subarea", "pk": 651, "fields": {"name": "خربة جبارة", "code": 651, "area": 12}}, {"model": "api.subarea", "pk": 652, "fields": {"name": "دير الغصون", "code": 652, "area": 12}}, {"model": "api.subarea", "pk": 653, "fields": {"name": "ذنابة", "code": 653, "area": 12}}, {"model": "api.subarea", "pk": 654, "fields": {"name": "رامين", "code": 654, "area": 12}}, {"model": "api.subarea", "pk": 655, "fields": {"name": "زيتا", "code": 655, "area": 12}}, {"model": "api.subarea", "pk": 656, "fields": {"name": "سفارين", "code": 656, "area": 12}}, {"model": "api.subarea", "pk": 657, "fields": {"name": "شارع السكة", "code": 657, "area": 12}}, {"model": "api.subarea", "pk": 658, "fields": {"name": "شوفة", "code": 658, "area": 12}}, {"model": "api.subarea", "pk": 659, "fields": {"name": "صيدا", "code": 659, "area": 12}}, {"model": "api.subarea", "pk": 660, "fields": {"name": "طولكرم", "code": 660, "area": 12}}, {"model": "api.subarea", "pk": 661, "fields": {"name": "طولكرم-الحارة الجنوبية", "code": 661, "area": 12}}, {"model": "api.subarea", "pk": 662, "fields": {"name": "طولكرم-حي ار<PERSON>اح", "code": 662, "area": 12}}, {"model": "api.subarea", "pk": 663, "fields": {"name": "طولكرم عزبة ناصر", "code": 663, "area": 12}}, {"model": "api.subarea", "pk": 664, "fields": {"name": "عتيل", "code": 664, "area": 12}}, {"model": "api.subarea", "pk": 665, "fields": {"name": "عزبة الخلال", "code": 665, "area": 12}}, {"model": "api.subarea", "pk": 666, "fields": {"name": "عزبة جراد", "code": 666, "area": 12}}, {"model": "api.subarea", "pk": 667, "fields": {"name": "عزبة شوفة", "code": 667, "area": 12}}, {"model": "api.subarea", "pk": 668, "fields": {"name": "عكابة", "code": 668, "area": 12}}, {"model": "api.subarea", "pk": 669, "fields": {"name": "علار", "code": 669, "area": 12}}, {"model": "api.subarea", "pk": 670, "fields": {"name": "عنبتا", "code": 670, "area": 12}}, {"model": "api.subarea", "pk": 671, "fields": {"name": "فرعون", "code": 671, "area": 12}}, {"model": "api.subarea", "pk": 672, "fields": {"name": "قفين", "code": 672, "area": 12}}, {"model": "api.subarea", "pk": 673, "fields": {"name": "كفا", "code": 673, "area": 12}}, {"model": "api.subarea", "pk": 674, "fields": {"name": "<PERSON><PERSON><PERSON> اللبد", "code": 674, "area": 12}}, {"model": "api.subarea", "pk": 675, "fields": {"name": "<PERSON><PERSON><PERSON> جمال", "code": 675, "area": 12}}, {"model": "api.subarea", "pk": 676, "fields": {"name": "كفر زيباد", "code": 676, "area": 12}}, {"model": "api.subarea", "pk": 677, "fields": {"name": "ك<PERSON><PERSON> صور", "code": 677, "area": 12}}, {"model": "api.subarea", "pk": 678, "fields": {"name": "<PERSON><PERSON><PERSON> عبوش", "code": 678, "area": 12}}, {"model": "api.subarea", "pk": 679, "fields": {"name": "كور", "code": 679, "area": 12}}, {"model": "api.subarea", "pk": 680, "fields": {"name": "م<PERSON><PERSON><PERSON> طولكرم", "code": 680, "area": 12}}, {"model": "api.subarea", "pk": 681, "fields": {"name": "مخيم نور شمس", "code": 681, "area": 12}}, {"model": "api.subarea", "pk": 682, "fields": {"name": "مدينة طولكرم", "code": 682, "area": 12}}, {"model": "api.subarea", "pk": 683, "fields": {"name": "السوق القديم", "code": 683, "area": 12}}, {"model": "api.subarea", "pk": 684, "fields": {"name": "حارة السلام", "code": 684, "area": 12}}, {"model": "api.subarea", "pk": 685, "fields": {"name": "حارة الفقهاء", "code": 685, "area": 12}}, {"model": "api.subarea", "pk": 686, "fields": {"name": "<PERSON><PERSON> الاقصى", "code": 686, "area": 12}}, {"model": "api.subarea", "pk": 687, "fields": {"name": "<PERSON>ي الجنوبي", "code": 687, "area": 12}}, {"model": "api.subarea", "pk": 688, "fields": {"name": "<PERSON>ي الشرقي", "code": 688, "area": 12}}, {"model": "api.subarea", "pk": 689, "fields": {"name": "<PERSON>ي الشمالي", "code": 689, "area": 12}}, {"model": "api.subarea", "pk": 690, "fields": {"name": "<PERSON><PERSON> الغربي", "code": 690, "area": 12}}, {"model": "api.subarea", "pk": 691, "fields": {"name": "دوار الساعة -طولكرم", "code": 691, "area": 12}}, {"model": "api.subarea", "pk": 692, "fields": {"name": "دوار جامعه الخضوري", "code": 692, "area": 12}}, {"model": "api.subarea", "pk": 693, "fields": {"name": "شارع المستشفى -طولكرم", "code": 693, "area": 12}}, {"model": "api.subarea", "pk": 694, "fields": {"name": "شارع شويكة", "code": 694, "area": 12}}, {"model": "api.subarea", "pk": 695, "fields": {"name": "شارع نابلس -طولكرم", "code": 695, "area": 12}}, {"model": "api.subarea", "pk": 696, "fields": {"name": "وسط البلد", "code": 696, "area": 12}}, {"model": "api.subarea", "pk": 697, "fields": {"name": "نزلة عيسى", "code": 697, "area": 12}}, {"model": "api.subarea", "pk": 698, "fields": {"name": "الضبعة", "code": 698, "area": 13}}, {"model": "api.subarea", "pk": 699, "fields": {"name": "الفندق", "code": 699, "area": 13}}, {"model": "api.subarea", "pk": 700, "fields": {"name": "المدور", "code": 700, "area": 13}}, {"model": "api.subarea", "pk": 701, "fields": {"name": "النبي إلياس", "code": 701, "area": 13}}, {"model": "api.subarea", "pk": 702, "fields": {"name": "إماتين", "code": 702, "area": 13}}, {"model": "api.subarea", "pk": 703, "fields": {"name": "باقة الحطب", "code": 703, "area": 13}}, {"model": "api.subarea", "pk": 704, "fields": {"name": "بيت أمين", "code": 704, "area": 13}}, {"model": "api.subarea", "pk": 705, "fields": {"name": "جنصافوط", "code": 705, "area": 13}}, {"model": "api.subarea", "pk": 706, "fields": {"name": "جيت", "code": 706, "area": 13}}, {"model": "api.subarea", "pk": 707, "fields": {"name": "جيوس", "code": 707, "area": 13}}, {"model": "api.subarea", "pk": 708, "fields": {"name": "ح<PERSON><PERSON>ة", "code": 708, "area": 13}}, {"model": "api.subarea", "pk": 709, "fields": {"name": "حجة", "code": 709, "area": 13}}, {"model": "api.subarea", "pk": 710, "fields": {"name": "خربة صير", "code": 710, "area": 13}}, {"model": "api.subarea", "pk": 711, "fields": {"name": "خلة نوفل", "code": 711, "area": 13}}, {"model": "api.subarea", "pk": 712, "fields": {"name": "راس الطيرة", "code": 712, "area": 13}}, {"model": "api.subarea", "pk": 713, "fields": {"name": "راس عطية", "code": 713, "area": 13}}, {"model": "api.subarea", "pk": 714, "fields": {"name": "سنيريا", "code": 714, "area": 13}}, {"model": "api.subarea", "pk": 715, "fields": {"name": "عرب أبو فردة", "code": 715, "area": 13}}, {"model": "api.subarea", "pk": 716, "fields": {"name": "عرب الرماضين الجنوبي", "code": 716, "area": 13}}, {"model": "api.subarea", "pk": 717, "fields": {"name": "عزبة الأشقر", "code": 717, "area": 13}}, {"model": "api.subarea", "pk": 718, "fields": {"name": "عزبة الطبيب", "code": 718, "area": 13}}, {"model": "api.subarea", "pk": 719, "fields": {"name": "عزبة جلعود", "code": 719, "area": 13}}, {"model": "api.subarea", "pk": 720, "fields": {"name": "عزبة سلمان", "code": 720, "area": 13}}, {"model": "api.subarea", "pk": 721, "fields": {"name": "عزون", "code": 721, "area": 13}}, {"model": "api.subarea", "pk": 722, "fields": {"name": "عزو<PERSON> عتمة", "code": 722, "area": 13}}, {"model": "api.subarea", "pk": 723, "fields": {"name": "عسلة", "code": 723, "area": 13}}, {"model": "api.subarea", "pk": 724, "fields": {"name": "فرعتا", "code": 724, "area": 13}}, {"model": "api.subarea", "pk": 725, "fields": {"name": "وسط البلد", "code": 725, "area": 13}}, {"model": "api.subarea", "pk": 726, "fields": {"name": "<PERSON><PERSON><PERSON>", "code": 726, "area": 13}}, {"model": "api.subarea", "pk": 727, "fields": {"name": "<PERSON><PERSON><PERSON> قدوم", "code": 727, "area": 13}}, {"model": "api.subarea", "pk": 728, "fields": {"name": "كفر لاقف", "code": 728, "area": 13}}, {"model": "api.subarea", "pk": 729, "fields": {"name": "مدينة قلقيلية", "code": 729, "area": 13}}, {"model": "api.subarea", "pk": 730, "fields": {"name": "حي كفر سابا", "code": 730, "area": 13}}, {"model": "api.subarea", "pk": 731, "fields": {"name": "وادي الرشا", "code": 731, "area": 13}}, {"model": "api.subarea", "pk": 732, "fields": {"name": "<PERSON>لة العامود", "code": 732, "area": 14}}, {"model": "api.subarea", "pk": 733, "fields": {"name": "إجنسنيا", "code": 733, "area": 14}}, {"model": "api.subarea", "pk": 734, "fields": {"name": "الباذان", "code": 734, "area": 14}}, {"model": "api.subarea", "pk": 735, "fields": {"name": "الساوية", "code": 735, "area": 14}}, {"model": "api.subarea", "pk": 736, "fields": {"name": "الطور", "code": 736, "area": 14}}, {"model": "api.subarea", "pk": 737, "fields": {"name": "العقربانية", "code": 737, "area": 14}}, {"model": "api.subarea", "pk": 738, "fields": {"name": "أللبن الشرقية", "code": 738, "area": 14}}, {"model": "api.subarea", "pk": 739, "fields": {"name": "الناقورة", "code": 739, "area": 14}}, {"model": "api.subarea", "pk": 740, "fields": {"name": "النصارية", "code": 740, "area": 14}}, {"model": "api.subarea", "pk": 741, "fields": {"name": "أودلا", "code": 741, "area": 14}}, {"model": "api.subarea", "pk": 742, "fields": {"name": "أوصرين", "code": 742, "area": 14}}, {"model": "api.subarea", "pk": 743, "fields": {"name": "برقة", "code": 743, "area": 14}}, {"model": "api.subarea", "pk": 744, "fields": {"name": "بزارية", "code": 744, "area": 14}}, {"model": "api.subarea", "pk": 745, "fields": {"name": "بورين", "code": 745, "area": 14}}, {"model": "api.subarea", "pk": 746, "fields": {"name": "بيتا", "code": 746, "area": 14}}, {"model": "api.subarea", "pk": 747, "fields": {"name": "بيت إمرين", "code": 747, "area": 14}}, {"model": "api.subarea", "pk": 748, "fields": {"name": "بيت إيبا", "code": 748, "area": 14}}, {"model": "api.subarea", "pk": 749, "fields": {"name": "بيت ايبا", "code": 749, "area": 14}}, {"model": "api.subarea", "pk": 750, "fields": {"name": "بي<PERSON> حسن", "code": 750, "area": 14}}, {"model": "api.subarea", "pk": 751, "fields": {"name": "بيت دجن", "code": 751, "area": 14}}, {"model": "api.subarea", "pk": 752, "fields": {"name": "بيت فوريك", "code": 752, "area": 14}}, {"model": "api.subarea", "pk": 753, "fields": {"name": "بيت وزن", "code": 753, "area": 14}}, {"model": "api.subarea", "pk": 754, "fields": {"name": "تلفيت", "code": 754, "area": 14}}, {"model": "api.subarea", "pk": 755, "fields": {"name": "جالود", "code": 755, "area": 14}}, {"model": "api.subarea", "pk": 756, "fields": {"name": "جماعين", "code": 756, "area": 14}}, {"model": "api.subarea", "pk": 757, "fields": {"name": "جوريش", "code": 757, "area": 14}}, {"model": "api.subarea", "pk": 758, "fields": {"name": "حوارة", "code": 758, "area": 14}}, {"model": "api.subarea", "pk": 759, "fields": {"name": "دوما", "code": 759, "area": 14}}, {"model": "api.subarea", "pk": 760, "fields": {"name": "دير الحطب", "code": 760, "area": 14}}, {"model": "api.subarea", "pk": 761, "fields": {"name": "دير شرف", "code": 761, "area": 14}}, {"model": "api.subarea", "pk": 762, "fields": {"name": "روجيب", "code": 762, "area": 14}}, {"model": "api.subarea", "pk": 763, "fields": {"name": "زواتا", "code": 763, "area": 14}}, {"model": "api.subarea", "pk": 764, "fields": {"name": "زيتا جماعين", "code": 764, "area": 14}}, {"model": "api.subarea", "pk": 765, "fields": {"name": "سالم", "code": 765, "area": 14}}, {"model": "api.subarea", "pk": 766, "fields": {"name": "سبسطية", "code": 766, "area": 14}}, {"model": "api.subarea", "pk": 767, "fields": {"name": "صرة", "code": 767, "area": 14}}, {"model": "api.subarea", "pk": 768, "fields": {"name": "طلوزة", "code": 768, "area": 14}}, {"model": "api.subarea", "pk": 769, "fields": {"name": "عراق بورين", "code": 769, "area": 14}}, {"model": "api.subarea", "pk": 770, "fields": {"name": "عزموط", "code": 770, "area": 14}}, {"model": "api.subarea", "pk": 771, "fields": {"name": "عصيرة الشمالية", "code": 771, "area": 14}}, {"model": "api.subarea", "pk": 772, "fields": {"name": "عصيرة القبلية", "code": 772, "area": 14}}, {"model": "api.subarea", "pk": 773, "fields": {"name": "عقربا", "code": 773, "area": 14}}, {"model": "api.subarea", "pk": 774, "fields": {"name": "عمورية", "code": 774, "area": 14}}, {"model": "api.subarea", "pk": 775, "fields": {"name": "عورتا", "code": 775, "area": 14}}, {"model": "api.subarea", "pk": 776, "fields": {"name": "عوريف", "code": 776, "area": 14}}, {"model": "api.subarea", "pk": 777, "fields": {"name": "عين البيضة (الاغوار)", "code": 777, "area": 14}}, {"model": "api.subarea", "pk": 778, "fields": {"name": "عينبوس", "code": 778, "area": 14}}, {"model": "api.subarea", "pk": 779, "fields": {"name": "<PERSON>ي<PERSON> شبلي", "code": 779, "area": 14}}, {"model": "api.subarea", "pk": 780, "fields": {"name": "قبلان", "code": 780, "area": 14}}, {"model": "api.subarea", "pk": 781, "fields": {"name": "قريوت", "code": 781, "area": 14}}, {"model": "api.subarea", "pk": 782, "fields": {"name": "قصرة", "code": 782, "area": 14}}, {"model": "api.subarea", "pk": 783, "fields": {"name": "قوصين", "code": 783, "area": 14}}, {"model": "api.subarea", "pk": 784, "fields": {"name": "<PERSON><PERSON><PERSON> قليل", "code": 784, "area": 14}}, {"model": "api.subarea", "pk": 785, "fields": {"name": "مادما", "code": 785, "area": 14}}, {"model": "api.subarea", "pk": 786, "fields": {"name": "مجدل بني فاضل", "code": 786, "area": 14}}, {"model": "api.subarea", "pk": 787, "fields": {"name": "مخيم عين بيت الماء", "code": 787, "area": 14}}, {"model": "api.subarea", "pk": 788, "fields": {"name": "مدينة نابلس", "code": 788, "area": 14}}, {"model": "api.subarea", "pk": 789, "fields": {"name": "إسكان روجيب", "code": 789, "area": 14}}, {"model": "api.subarea", "pk": 790, "fields": {"name": "الاكاديمية", "code": 790, "area": 14}}, {"model": "api.subarea", "pk": 791, "fields": {"name": "البلدة القديمة", "code": 791, "area": 14}}, {"model": "api.subarea", "pk": 792, "fields": {"name": "الجامعه القديمة", "code": 792, "area": 14}}, {"model": "api.subarea", "pk": 793, "fields": {"name": "الجبل الجنوبي", "code": 793, "area": 14}}, {"model": "api.subarea", "pk": 794, "fields": {"name": "الجبل الشمالي", "code": 794, "area": 14}}, {"model": "api.subarea", "pk": 795, "fields": {"name": "الجنيد", "code": 795, "area": 14}}, {"model": "api.subarea", "pk": 796, "fields": {"name": "الدوار", "code": 796, "area": 14}}, {"model": "api.subarea", "pk": 797, "fields": {"name": "السوق الشرقي", "code": 797, "area": 14}}, {"model": "api.subarea", "pk": 798, "fields": {"name": "الضاحية", "code": 798, "area": 14}}, {"model": "api.subarea", "pk": 799, "fields": {"name": "العامرية", "code": 799, "area": 14}}, {"model": "api.subarea", "pk": 800, "fields": {"name": "العين", "code": 800, "area": 14}}, {"model": "api.subarea", "pk": 801, "fields": {"name": "المخفية", "code": 801, "area": 14}}, {"model": "api.subarea", "pk": 802, "fields": {"name": "المساكن الشعبية", "code": 802, "area": 14}}, {"model": "api.subarea", "pk": 803, "fields": {"name": "المعاجين", "code": 803, "area": 14}}, {"model": "api.subarea", "pk": 804, "fields": {"name": "المنطقة الصناعية الشرقية", "code": 804, "area": 14}}, {"model": "api.subarea", "pk": 805, "fields": {"name": "بكير", "code": 805, "area": 14}}, {"model": "api.subarea", "pk": 806, "fields": {"name": "بلاطة", "code": 806, "area": 14}}, {"model": "api.subarea", "pk": 807, "fields": {"name": "راس العين", "code": 807, "area": 14}}, {"model": "api.subarea", "pk": 808, "fields": {"name": "رفيديا", "code": 808, "area": 14}}, {"model": "api.subarea", "pk": 809, "fields": {"name": "شارع 15", "code": 809, "area": 14}}, {"model": "api.subarea", "pk": 810, "fields": {"name": "شارع 16", "code": 810, "area": 14}}, {"model": "api.subarea", "pk": 811, "fields": {"name": "شارع احمد الشكعة", "code": 811, "area": 14}}, {"model": "api.subarea", "pk": 812, "fields": {"name": "شارع الاتحاد", "code": 812, "area": 14}}, {"model": "api.subarea", "pk": 813, "fields": {"name": "شارع التعاون", "code": 813, "area": 14}}, {"model": "api.subarea", "pk": 814, "fields": {"name": "شارع الحسبة", "code": 814, "area": 14}}, {"model": "api.subarea", "pk": 815, "fields": {"name": "شارع الرازي", "code": 815, "area": 14}}, {"model": "api.subarea", "pk": 816, "fields": {"name": "شارع السكة", "code": 816, "area": 14}}, {"model": "api.subarea", "pk": 817, "fields": {"name": "شارع القدس", "code": 817, "area": 14}}, {"model": "api.subarea", "pk": 818, "fields": {"name": "شارع المدارس", "code": 818, "area": 14}}, {"model": "api.subarea", "pk": 819, "fields": {"name": "شارع المنتزه", "code": 819, "area": 14}}, {"model": "api.subarea", "pk": 820, "fields": {"name": "شارع تل", "code": 820, "area": 14}}, {"model": "api.subarea", "pk": 821, "fields": {"name": "شارع جمال عبد الناصر", "code": 821, "area": 14}}, {"model": "api.subarea", "pk": 822, "fields": {"name": "شارع حطين", "code": 822, "area": 14}}, {"model": "api.subarea", "pk": 823, "fields": {"name": "شارع حيفا", "code": 823, "area": 14}}, {"model": "api.subarea", "pk": 824, "fields": {"name": "شارع سفيان", "code": 824, "area": 14}}, {"model": "api.subarea", "pk": 825, "fields": {"name": "شارع شويترة", "code": 825, "area": 14}}, {"model": "api.subarea", "pk": 826, "fields": {"name": "شارع صلاح الدين", "code": 826, "area": 14}}, {"model": "api.subarea", "pk": 827, "fields": {"name": "شارع طولكرم", "code": 827, "area": 14}}, {"model": "api.subarea", "pk": 828, "fields": {"name": "شارع عصيرة", "code": 828, "area": 14}}, {"model": "api.subarea", "pk": 829, "fields": {"name": "شارع عمان", "code": 829, "area": 14}}, {"model": "api.subarea", "pk": 830, "fields": {"name": "شارع عمر المختار", "code": 830, "area": 14}}, {"model": "api.subarea", "pk": 831, "fields": {"name": "شارع غرناطة", "code": 831, "area": 14}}, {"model": "api.subarea", "pk": 832, "fields": {"name": "شارع فلسطين", "code": 832, "area": 14}}, {"model": "api.subarea", "pk": 833, "fields": {"name": "شارع فيصل", "code": 833, "area": 14}}, {"model": "api.subarea", "pk": 834, "fields": {"name": "شارع يافا", "code": 834, "area": 14}}, {"model": "api.subarea", "pk": 835, "fields": {"name": "عسكر", "code": 835, "area": 14}}, {"model": "api.subarea", "pk": 836, "fields": {"name": "فطاير", "code": 836, "area": 14}}, {"model": "api.subarea", "pk": 837, "fields": {"name": "قرية تل", "code": 837, "area": 14}}, {"model": "api.subarea", "pk": 838, "fields": {"name": "م<PERSON>يم بلاطة", "code": 838, "area": 14}}, {"model": "api.subarea", "pk": 839, "fields": {"name": "م<PERSON><PERSON><PERSON> عسكر", "code": 839, "area": 14}}, {"model": "api.subarea", "pk": 840, "fields": {"name": "نابلس الجديدة", "code": 840, "area": 14}}, {"model": "api.subarea", "pk": 841, "fields": {"name": "شارع العدل", "code": 841, "area": 14}}, {"model": "api.subarea", "pk": 842, "fields": {"name": "وسط البلد", "code": 842, "area": 14}}, {"model": "api.subarea", "pk": 843, "fields": {"name": "نابلس", "code": 843, "area": 14}}, {"model": "api.subarea", "pk": 844, "fields": {"name": "شارع ابن رشد", "code": 844, "area": 14}}, {"model": "api.subarea", "pk": 845, "fields": {"name": "نصف جبيل", "code": 845, "area": 14}}, {"model": "api.subarea", "pk": 846, "fields": {"name": "ياصيد", "code": 846, "area": 14}}, {"model": "api.subarea", "pk": 847, "fields": {"name": "يتما", "code": 847, "area": 14}}, {"model": "api.subarea", "pk": 848, "fields": {"name": "الهدار", "code": 848, "area": 15}}, {"model": "api.subarea", "pk": 849, "fields": {"name": "وادي النسناس", "code": 849, "area": 15}}, {"model": "api.subarea", "pk": 850, "fields": {"name": "الحليصة", "code": 850, "area": 15}}, {"model": "api.subarea", "pk": 851, "fields": {"name": "الكبابير", "code": 851, "area": 15}}, {"model": "api.subarea", "pk": 852, "fields": {"name": "<PERSON><PERSON><PERSON> الكرمل", "code": 852, "area": 15}}, {"model": "api.subarea", "pk": 853, "fields": {"name": "الخالصة", "code": 853, "area": 15}}, {"model": "api.subarea", "pk": 854, "fields": {"name": "<PERSON>ي الجبارين", "code": 854, "area": 16}}, {"model": "api.subarea", "pk": 855, "fields": {"name": "حي المحاجنة", "code": 855, "area": 16}}, {"model": "api.subarea", "pk": 856, "fields": {"name": "<PERSON><PERSON> الباطن", "code": 856, "area": 16}}, {"model": "api.subarea", "pk": 857, "fields": {"name": "<PERSON><PERSON> الميدان", "code": 857, "area": 16}}, {"model": "api.subarea", "pk": 858, "fields": {"name": "الحي الشرقي", "code": 858, "area": 17}}, {"model": "api.subarea", "pk": 859, "fields": {"name": "الحي الغربي", "code": 859, "area": 17}}, {"model": "api.subarea", "pk": 860, "fields": {"name": "الحي الشمالي", "code": 860, "area": 17}}, {"model": "api.subarea", "pk": 861, "fields": {"name": "المنطقة المركزية", "code": 861, "area": 18}}, {"model": "api.subarea", "pk": 862, "fields": {"name": "منطقة الملعب", "code": 862, "area": 18}}, {"model": "api.subarea", "pk": 863, "fields": {"name": "المنطقة الزراعية", "code": 863, "area": 18}}, {"model": "api.subarea", "pk": 864, "fields": {"name": "البلدة القديمة", "code": 864, "area": 19}}, {"model": "api.subarea", "pk": 865, "fields": {"name": "وادي النسناس", "code": 865, "area": 19}}, {"model": "api.subarea", "pk": 866, "fields": {"name": "الحي الجنوبي", "code": 866, "area": 19}}, {"model": "api.subarea", "pk": 867, "fields": {"name": "الحي الغربي", "code": 867, "area": 19}}, {"model": "api.subarea", "pk": 868, "fields": {"name": "حي البركة", "code": 868, "area": 20}}, {"model": "api.subarea", "pk": 869, "fields": {"name": "حي المسعودية", "code": 869, "area": 20}}, {"model": "api.subarea", "pk": 870, "fields": {"name": "<PERSON><PERSON> الخلة", "code": 870, "area": 20}}, {"model": "api.subarea", "pk": 871, "fields": {"name": "حي عين عافية", "code": 871, "area": 21}}, {"model": "api.subarea", "pk": 872, "fields": {"name": "حي المنصورة", "code": 872, "area": 21}}, {"model": "api.subarea", "pk": 873, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 873, "area": 21}}, {"model": "api.subarea", "pk": 874, "fields": {"name": "<PERSON>ي الفوار", "code": 874, "area": 21}}, {"model": "api.subarea", "pk": 875, "fields": {"name": "البلدة القديمة", "code": 875, "area": 22}}, {"model": "api.subarea", "pk": 876, "fields": {"name": "حي الصفافرة", "code": 876, "area": 22}}, {"model": "api.subarea", "pk": 877, "fields": {"name": "<PERSON>ي القفزة", "code": 877, "area": 22}}, {"model": "api.subarea", "pk": 878, "fields": {"name": "جب<PERSON> طابور", "code": 878, "area": 22}}, {"model": "api.subarea", "pk": 879, "fields": {"name": "<PERSON><PERSON> المطران", "code": 879, "area": 22}}, {"model": "api.subarea", "pk": 880, "fields": {"name": "<PERSON>ي الزيتون", "code": 880, "area": 23}}, {"model": "api.subarea", "pk": 881, "fields": {"name": "<PERSON><PERSON> الدروز", "code": 881, "area": 23}}, {"model": "api.subarea", "pk": 882, "fields": {"name": "<PERSON>ي الخربة", "code": 882, "area": 23}}, {"model": "api.subarea", "pk": 883, "fields": {"name": "الحي الشمالي", "code": 883, "area": 24}}, {"model": "api.subarea", "pk": 884, "fields": {"name": "الحي الجنوبي", "code": 884, "area": 24}}, {"model": "api.subarea", "pk": 885, "fields": {"name": "الحي الغربي", "code": 885, "area": 25}}, {"model": "api.subarea", "pk": 886, "fields": {"name": "الحي الشرقي", "code": 886, "area": 25}}, {"model": "api.subarea", "pk": 887, "fields": {"name": "منطقة البركة", "code": 887, "area": 26}}, {"model": "api.subarea", "pk": 888, "fields": {"name": "الحي الجنوبي", "code": 888, "area": 26}}, {"model": "api.subarea", "pk": 889, "fields": {"name": "<PERSON>ي السوق", "code": 889, "area": 27}}, {"model": "api.subarea", "pk": 890, "fields": {"name": "<PERSON>ي عين الاسد", "code": 890, "area": 27}}, {"model": "api.subarea", "pk": 891, "fields": {"name": "الحي الجنوبي", "code": 891, "area": 28}}, {"model": "api.subarea", "pk": 892, "fields": {"name": "الحي الغربي", "code": 892, "area": 28}}, {"model": "api.subarea", "pk": 893, "fields": {"name": "<PERSON>ي المعاصر", "code": 893, "area": 29}}, {"model": "api.subarea", "pk": 894, "fields": {"name": "<PERSON>ي الزيتون", "code": 894, "area": 30}}, {"model": "api.subarea", "pk": 895, "fields": {"name": "<PERSON>ي السوق", "code": 895, "area": 30}}, {"model": "api.subarea", "pk": 896, "fields": {"name": "الحي الغربي", "code": 896, "area": 31}}, {"model": "api.subarea", "pk": 897, "fields": {"name": "الحي الشمالي", "code": 897, "area": 31}}, {"model": "api.subarea", "pk": 898, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 898, "area": 32}}, {"model": "api.subarea", "pk": 899, "fields": {"name": "الحي الجنوبي", "code": 899, "area": 33}}, {"model": "api.subarea", "pk": 900, "fields": {"name": "<PERSON><PERSON> البدو", "code": 900, "area": 34}}, {"model": "api.subarea", "pk": 901, "fields": {"name": "<PERSON><PERSON> الغربي", "code": 901, "area": 34}}, {"model": "api.subarea", "pk": 902, "fields": {"name": "<PERSON><PERSON> المحطة", "code": 902, "area": 34}}, {"model": "api.subarea", "pk": 903, "fields": {"name": "الحي الجنوبي", "code": 903, "area": 35}}, {"model": "api.subarea", "pk": 904, "fields": {"name": "الحي الغربي", "code": 904, "area": 35}}, {"model": "api.subarea", "pk": 905, "fields": {"name": "حي البركة", "code": 905, "area": 36}}, {"model": "api.subarea", "pk": 906, "fields": {"name": "<PERSON>ي السوق", "code": 906, "area": 36}}, {"model": "api.subarea", "pk": 907, "fields": {"name": "الحي الشمالي", "code": 907, "area": 37}}, {"model": "api.subarea", "pk": 908, "fields": {"name": "الحي الجنوبي", "code": 908, "area": 37}}, {"model": "api.subarea", "pk": 909, "fields": {"name": "<PERSON><PERSON> البدو", "code": 909, "area": 38}}, {"model": "api.subarea", "pk": 910, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 910, "area": 38}}, {"model": "api.subarea", "pk": 911, "fields": {"name": "حي المشيرفة", "code": 911, "area": 38}}, {"model": "api.subarea", "pk": 912, "fields": {"name": "<PERSON>ي السوق", "code": 912, "area": 39}}, {"model": "api.subarea", "pk": 913, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 913, "area": 39}}, {"model": "api.subarea", "pk": 914, "fields": {"name": "<PERSON><PERSON> المحطة", "code": 914, "area": 40}}, {"model": "api.subarea", "pk": 915, "fields": {"name": "<PERSON>ي السوق", "code": 915, "area": 40}}, {"model": "api.subarea", "pk": 916, "fields": {"name": "<PERSON><PERSON> العمال", "code": 916, "area": 40}}, {"model": "api.subarea", "pk": 917, "fields": {"name": "<PERSON><PERSON> النقب", "code": 917, "area": 40}}, {"model": "api.subarea", "pk": 918, "fields": {"name": "<PERSON>ي الزهور", "code": 918, "area": 41}}, {"model": "api.subarea", "pk": 919, "fields": {"name": "حي الشبيبة", "code": 919, "area": 41}}, {"model": "api.subarea", "pk": 920, "fields": {"name": "<PERSON>ي النور", "code": 920, "area": 41}}, {"model": "api.subarea", "pk": 921, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 921, "area": 42}}, {"model": "api.subarea", "pk": 922, "fields": {"name": "<PERSON>ي السوق", "code": 922, "area": 42}}, {"model": "api.subarea", "pk": 923, "fields": {"name": "الحي الغربي", "code": 923, "area": 43}}, {"model": "api.subarea", "pk": 924, "fields": {"name": "الحي الشرقي", "code": 924, "area": 43}}, {"model": "api.subarea", "pk": 925, "fields": {"name": "<PERSON>ي السوق", "code": 925, "area": 44}}, {"model": "api.subarea", "pk": 926, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 926, "area": 45}}, {"model": "api.subarea", "pk": 927, "fields": {"name": "<PERSON><PERSON> الب<PERSON>د", "code": 927, "area": 45}}, {"model": "api.subarea", "pk": 928, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 928, "area": 46}}, {"model": "api.subarea", "pk": 929, "fields": {"name": "<PERSON>ي السوق", "code": 929, "area": 46}}, {"model": "api.subarea", "pk": 930, "fields": {"name": "<PERSON>ي السوق", "code": 930, "area": 47}}, {"model": "api.subarea", "pk": 931, "fields": {"name": "<PERSON>ي الشرقي", "code": 931, "area": 47}}, {"model": "api.subarea", "pk": 932, "fields": {"name": "الحي الشرقي", "code": 932, "area": 48}}, {"model": "api.subarea", "pk": 933, "fields": {"name": "<PERSON><PERSON> العمال", "code": 933, "area": 49}}, {"model": "api.subarea", "pk": 934, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 934, "area": 49}}, {"model": "api.subarea", "pk": 935, "fields": {"name": "<PERSON>ي السوق", "code": 935, "area": 50}}, {"model": "api.subarea", "pk": 936, "fields": {"name": "حي يافا", "code": 936, "area": 51}}, {"model": "api.subarea", "pk": 937, "fields": {"name": "حي روتشيلد", "code": 937, "area": 51}}, {"model": "api.subarea", "pk": 938, "fields": {"name": "حي هرتسليا", "code": 938, "area": 51}}, {"model": "api.subarea", "pk": 939, "fields": {"name": "<PERSON><PERSON> العجمي", "code": 939, "area": 52}}, {"model": "api.subarea", "pk": 940, "fields": {"name": "حي المنشية", "code": 940, "area": 52}}, {"model": "api.subarea", "pk": 941, "fields": {"name": "حي هرتسليا", "code": 941, "area": 53}}, {"model": "api.subarea", "pk": 942, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 942, "area": 53}}, {"model": "api.subarea", "pk": 943, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 943, "area": 54}}, {"model": "api.subarea", "pk": 944, "fields": {"name": "حي كفار هيروك", "code": 944, "area": 54}}, {"model": "api.subarea", "pk": 945, "fields": {"name": "حي المزارع", "code": 945, "area": 55}}, {"model": "api.subarea", "pk": 946, "fields": {"name": "<PERSON>ي السوق", "code": 946, "area": 55}}, {"model": "api.subarea", "pk": 947, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 947, "area": 56}}, {"model": "api.subarea", "pk": 948, "fields": {"name": "حي تل هشومير", "code": 948, "area": 56}}, {"model": "api.subarea", "pk": 949, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 949, "area": 57}}, {"model": "api.subarea", "pk": 950, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 950, "area": 58}}, {"model": "api.subarea", "pk": 951, "fields": {"name": "<PERSON><PERSON> ال<PERSON><PERSON>ر", "code": 951, "area": 58}}, {"model": "api.subarea", "pk": 952, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 952, "area": 59}}, {"model": "api.subarea", "pk": 953, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 953, "area": 60}}, {"model": "api.subarea", "pk": 954, "fields": {"name": "<PERSON>ي السوق", "code": 954, "area": 60}}, {"model": "api.subarea", "pk": 955, "fields": {"name": "<PERSON>ي السوق", "code": 955, "area": 61}}, {"model": "api.subarea", "pk": 956, "fields": {"name": "<PERSON><PERSON> العمال", "code": 956, "area": 61}}, {"model": "api.subarea", "pk": 957, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 957, "area": 62}}, {"model": "api.subarea", "pk": 958, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 958, "area": 63}}, {"model": "api.subarea", "pk": 959, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 959, "area": 64}}, {"model": "api.subarea", "pk": 960, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 960, "area": 65}}, {"model": "api.subarea", "pk": 961, "fields": {"name": "<PERSON>ي السوق", "code": 961, "area": 66}}, {"model": "api.subarea", "pk": 962, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 962, "area": 53}}, {"model": "api.subarea", "pk": 963, "fields": {"name": "<PERSON><PERSON> ال<PERSON><PERSON>ر", "code": 963, "area": 67}}, {"model": "api.subarea", "pk": 964, "fields": {"name": "<PERSON><PERSON> العمال", "code": 964, "area": 68}}, {"model": "api.subarea", "pk": 965, "fields": {"name": "<PERSON><PERSON> ال<PERSON><PERSON>ر", "code": 965, "area": 68}}, {"model": "api.subarea", "pk": 966, "fields": {"name": "<PERSON><PERSON> العمال", "code": 966, "area": 69}}, {"model": "api.subarea", "pk": 967, "fields": {"name": "<PERSON>ي السوق", "code": 967, "area": 69}}, {"model": "api.subarea", "pk": 968, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 968, "area": 70}}, {"model": "api.subarea", "pk": 969, "fields": {"name": "<PERSON>ي السوق", "code": 969, "area": 71}}, {"model": "api.subarea", "pk": 970, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 970, "area": 72}}, {"model": "api.subarea", "pk": 971, "fields": {"name": "<PERSON>ي البحيرة", "code": 971, "area": 73}}, {"model": "api.subarea", "pk": 972, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 972, "area": 73}}, {"model": "api.subarea", "pk": 973, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 973, "area": 74}}, {"model": "api.subarea", "pk": 974, "fields": {"name": "<PERSON>ي السوق", "code": 974, "area": 74}}, {"model": "api.subarea", "pk": 975, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 975, "area": 75}}, {"model": "api.subarea", "pk": 976, "fields": {"name": "<PERSON><PERSON> ال<PERSON><PERSON>ر", "code": 976, "area": 76}}, {"model": "api.subarea", "pk": 977, "fields": {"name": "<PERSON><PERSON> المر<PERSON>ز", "code": 977, "area": 76}}, {"model": "api.subarea", "pk": 978, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 978, "area": 77}}, {"model": "api.subarea", "pk": 979, "fields": {"name": "<PERSON>ي السوق", "code": 979, "area": 23}}, {"model": "api.subarea", "pk": 980, "fields": {"name": "<PERSON><PERSON> الج<PERSON>ل", "code": 980, "area": 78}}]