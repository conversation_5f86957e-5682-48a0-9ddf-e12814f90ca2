[{"model": "api.statusmap", "pk": 1, "fields": {"status_code": "rejected", "delivery_company_status": "Rejected", "description": "Rejected"}}, {"model": "api.statusmap", "pk": 2, "fields": {"delivery_company_status": "Waiting", "status_code": "waiting", "description": "not yet with delivery company but sent to them"}}, {"model": "api.statusmap", "pk": 3, "fields": {"delivery_company_status": "Picked up", "status_code": "picked_up", "description": "the delivery picked up"}}, {"model": "api.statusmap", "pk": 4, "fields": {"delivery_company_status": "Picking up", "status_code": "picking_up", "description": "the delivery company coming to fetch"}}, {"model": "api.statusmap", "pk": 5, "fields": {"status_code": "ready_for_distribution", "delivery_company_status": "Ready for Distribution", "description": "When packages are ready to be out for delivery with driver"}}, {"model": "api.statusmap", "pk": 6, "fields": {"status_code": "in_progress", "delivery_company_status": "In Progress", "description": "the package in progress "}}, {"model": "api.statusmap", "pk": 7, "fields": {"status_code": "delivered", "delivery_company_status": "Delivered", "description": "success delivered"}}, {"model": "api.statusmap", "pk": 8, "fields": {"status_code": "stuck", "delivery_company_status": "Stuck", "description": "can not reach client"}}, {"model": "api.statusmap", "pk": 9, "fields": {"status_code": "reschedule", "delivery_company_status": "Reschedule", "description": "Rescheduled"}}, {"model": "api.statusmap", "pk": 10, "fields": {"status_code": "returned", "delivery_company_status": "Returned", "description": "Returned"}}, {"model": "api.statusmap", "pk": 11, "fields": {"status_code": "delivered_partial", "delivery_company_status": "Delivered Partial", "description": "When driver deliver part of shipment for charge and part returned with him"}}, {"model": "api.statusmap", "pk": 12, "fields": {"status_code": "replacement", "delivery_company_status": "Replacement", "description": "Replacement"}}, {"model": "api.statusmap", "pk": 13, "fields": {"status_code": "in_branch", "delivery_company_status": "In Branch", "description": "the package in the store of delivery company"}}, {"model": "api.statusmap", "pk": 14, "fields": {"status_code": "to_branch", "delivery_company_status": "To Branch", "description": "To Delivery Company Branch"}}, {"model": "api.statusmap", "pk": 15, "fields": {"status_code": "ready_for_delivery", "delivery_company_status": "Ready For Dispatch", "description": "When packages are ready to be out for delivery with driver"}}, {"model": "api.statusmap", "pk": 16, "fields": {"status_code": "ready_for_return", "delivery_company_status": "Ready for Return", "description": "When package is ready to be returned to branch or business"}}, {"model": "api.statusmap", "pk": 17, "fields": {"status_code": "returned_in_progress", "delivery_company_status": "Returned In Progress", "description": "When driver has the package to be returned to business"}}, {"model": "api.statusmap", "pk": 18, "fields": {"status_code": "money_received", "delivery_company_status": "Money Received", "description": "money with deliver company"}}, {"model": "api.statusmap", "pk": 19, "fields": {"status_code": "money_in", "delivery_company_status": "Money In", "description": "When accountant or the super manager in delivery company receive the money and process it in the collection"}}, {"model": "api.statusmap", "pk": 20, "fields": {"delivery_company_status": "Cancelled", "status_code": "canceled", "description": "Cancelled"}}, {"model": "api.statusmap", "pk": 21, "fields": {"status_code": "rejected_partial", "delivery_company_status": "Rejected Partial", "description": "When part of cargo return with driver"}}, {"model": "api.statusmap", "pk": 22, "fields": {"status_code": "completed", "delivery_company_status": "Completed", "description": "Completed delivery"}}, {"model": "api.statusmap", "pk": 23, "fields": {"delivery_company_status": "Deleted", "status_code": "deleted", "description": "Deleted"}}, {"model": "api.statusmap", "pk": 24, "fields": {"delivery_company_status": "Delivered <PERSON><PERSON>", "status_code": "delivered_stuck", "description": "When the driver delivers a package but there is a deference between the payment and cost"}}, {"model": "api.statusmap", "pk": 25, "fields": {"delivery_company_status": "Branch Returned", "status_code": "branch_returned", "description": "When driver deliver the returned package to the branch manager with a collection"}}, {"model": "api.statusmap", "pk": 26, "fields": {"status_code": "money_out", "delivery_company_status": "Money Out", "description": "When accountant or the super manager in delivery company send the money to business with collection"}}, {"model": "api.statusmap", "pk": 27, "fields": {"status_code": "completed_returned", "delivery_company_status": "Completed Returned", "description": "This is when returned cargo is back to sender"}}]