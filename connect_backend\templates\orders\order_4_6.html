<!DOCTYPE html>
{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_4_6.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}

    {% for doc in docs %}
    {% set products = doc.order_lines %}
    {% set chunk_size = 9 %}
    {% set product_count = products|length %}
    {% set chunks = (product_count / chunk_size)|round(0, 'ceil')|int or 1 %}

    {% for i in range(chunks) %}
    <div class="page-break">
        <div class="waybill-header d-flex justify-content-between align-items-center">
            <div class="business-logo">
                <img src="{{doc.company_logo}}" class="business-logo">
            </div>
            <div class="header-titles fw-bold text-end">
                <span class="text-center fw-bold">{{doc.company_name}}</span>
                <br>
                <span class="text-center fw-bold">{{meta.labels.PRINT_BY}}: {{meta.user_name}}</span>
                <br>
                {{meta.formatted_now}}
            </div>
        </div>
        <table class="bordered-table w-100">
            <tr class="table-header">
                <th class="text-center">
                    {{meta.labels.COD_SHORT}}
                </th>
                <th class="text-center">
                    {{meta.labels.RECIPIENT}}
                </th>
            </tr>
            <tr>
                <td rowspan="2" class="text-center fs-lg fw-bold">
                    {{doc.order.total_cod}}
                </td>
                <td class="text-center">
                    {{doc.order.customer_name}}
                </td>
            </tr>
                <td class="text-center">
                    {{doc.order.customer_mobile}}
                </td>
            </tr>
            <tr>
                <td colspan="2" class="text-center">
                    <span class="">
                        {{doc.order.area.name}},
                        {{doc.order.sub_area.name}},
                        {{doc.order.address}}
                    </span>
                </td>
            </tr>
            {% if doc.order.note%}
                <tr>
                    <td colspan="2" class="notes-container text-center">
                        <span class="">{{doc.order.note if doc.order.note is not none else ""}}</span>
                    </td>
                </tr>
            {% endif %}
        </table>

        <div class="products-container">
            {% if doc.order_lines and doc.order_lines|length > 0 %}
            <table class="o-table">
                <thead class="table-header">
                    <tr>
                        <th class="cell-truncate border-b-grey">
                            {{meta.labels.PRODUCT}}
                        </th>
                        <th class="border-b-grey">
                            {{meta.labels.QTY}}
                        </th>
                        <th class="border-b-grey">
                            {{meta.labels.CODE}}
                        </th>
                        <th class="border-b-grey">
                            {{meta.labels.LOCATIONS}}
                        </th>
                    </tr>
                </thead>
                {% for item in products[i*chunk_size : (i+1)*chunk_size] %}
                <tr>
                    <td class="cell-truncate">
                        {{item.product_variant.variant.name if item.print_product_sequence else item.product_variant.variant.name}}
                    </td>
                    <td class="">
                        {{item.quantity}}
                    </td>
                    <td class="">
                        {{item.product_variant.variant.sku}}
                    </td>
                    <td class="">
                        {{item.locations}}
                    </td>
                </tr>
                {% endfor %}
            </table>
            {% endif %}
        </div>
        <div class="barcode-container">
            <img class="barcode-img" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}"
                alt="">
            <div class="page-number-container">
                {% if chunks > 1 %}
                    <span class="text-small">{{i+1}} of {{chunks}}</span>
                {% endif %}
            </div>
        </div>
    </div>
    {% endfor %}
    {% endfor %}
</body>

</html>