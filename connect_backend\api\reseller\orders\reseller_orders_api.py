from django.utils.translation import gettext as _
from django.views.decorators.csrf import csrf_exempt
from api.orders.order_model import Order, Status, StatusMap
from api.serializers import OrderSerializer, StatusSerializer
from api.util_functions import create_log, create_log_info, get_records, handle_stock, parse_request_body, send_delivery_status_to_woocommerce
from api.orders.order_utils import auto_send_orders, handle_delivery_company_status, handle_order_type, update_order_statuses, get_order
from api.connection.connection_utils import process_sending_orders
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ...auth.auth_utils import *
from django.db.models import Q
from django.db import transaction
from api.serializers import UserPreferencesSerializer, ResellerUserPreferencesSerializer

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_orders(request):
    try:
        try:
            reseller = request.user
        except User.DoesNotExist:
            raise get_error_response('AUTH_400', {'username': request.user.username})
        
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        custom_filter = Q(warehouse__reseller=reseller)

        response_data = get_records(
            'Order',
            json_data,
            False,
            custom_filter=custom_filter,
            context={'additional_fields': ['company']}
            )
        return JsonResponse(response_data, status=200)

    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def update_order_status(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    user = request.user
    order_ids = json_data.get('order_ids', [])
    status_code = json_data.get('status')
    delivery_status_code = json_data.get('delivery_status')
    edited = json_data.get('edited', [])

    if status_code and delivery_status_code:
        raise get_error_response('ORDER_200', {})
    
    if delivery_status_code:
        try:
            delivery_status = StatusMap.objects.get(status_code=delivery_status_code)
        except StatusMap.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'StatusMap', 'field': 'code', 'value': delivery_status_code})
        for id in order_ids:
            try:
                order = Order.objects.get(id=id)
            except Order.DoesNotExist:
                continue
            if delivery_status_code == 'reschedule':
                reschedule_date = json_data.get('reschedule_date')
                order.reschedule_date = reschedule_date
            elif delivery_status_code == 'rejected' or delivery_status_code == 'returned':
                reject_reason = json_data.get('reject_reason')
                order.reject_reason = reject_reason
            elif delivery_status_code == 'stuck':
                stuck_comment = json_data.get('stuck_comment')
                order.stuck_comment = stuck_comment
            log = create_log('Order', order.company, 'update', user.name, order.id)
            log_info = create_log_info(log, 'delivery_company_status', order.delivery_company_status, delivery_status.delivery_company_status)
            order.delivery_company_status = delivery_status.delivery_company_status
            order.save()
            if order.channel.lower() == 'woocommerce' and order.order_reference:
                try:
                    send_order = send_delivery_status_to_woocommerce(order.order_reference, order.delivery_company_status, order.olivery_sequence, order.company)
                except:
                    pass
        response = {
        'success': True, 
        'message': _('Order status updated successfully'),
        'order_ids': order_ids
        }
        return JsonResponse(response, status=200)

    edited_ids = [item['id'] for item in edited]
    filtered_order_ids = [order_id for order_id in order_ids if order_id not in edited_ids]
    try:
        status = Status.objects.filter(code=status_code).first()
    except Status.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'STATUS_DOES_NOT_EXIST'}, status=404)
    
    edited_order_ids = []
    if edited:
        with transaction.atomic():
            for item in edited:
                order_id = item['id']
                item['created_by'] = user.name
                item['status'] = status
                edited_ids = handle_order_type(item,is_reseller=True)
                if len(edited_ids):
                    edited_order_ids.extend(edited_ids)
    
    updated_order_ids = []
    with transaction.atomic():
        for order_id in filtered_order_ids:
            order = Order.objects.get(id=order_id)
            values = {
                'id': order_id,
                'status': status,
                'company': order.company,
                'updated_by': user.name
            }
            if json_data.get('store_stuck_comment', ''):
                values['store_stuck_comment'] = json_data.get('store_stuck_comment')
            order_old_value = get_order(values['id'])
            # For reseller check if the new status is "new order" and the old value was "returned" so we need to reset certain fields 
            if values.get('status').code == 'new_order' and order_old_value.status.code == 'returned':
                order_old_value.olivery_sequence = None
                order_old_value.send_to_delivery_date = None
                order_old_value.delivery_date = None
                order_old_value.tried_to_delivered = False
                order_old_value.return_date = None
                order_old_value.delivery_status_change_date = None
                order_old_value.delivery_company_status = None

            order = update_order_statuses(values, order_old_value)
            updated_order_ids.append(order.id)
            if not order:
                return JsonResponse({'success': False, 'error': 'ORDER_NOT_FOUND'}, status=404)
            handle_stock(order)
            response = handle_delivery_company_status(order, order.company, status)

    updated_order_ids = list(set(updated_order_ids).union(edited_order_ids))
    success_messages = []
    fail_messages = []
    if status.code == 'ready_for_delivery':
        orders = Order.objects.filter(id__in=updated_order_ids)
        for order in orders:
            success_message, fail_message = auto_send_orders([order], order.company, user.name, status=status)
            success_messages.extend(success_message)
            fail_messages.extend(fail_message)
    response = {
        'success': True, 
        'message': _('Order status updated successfully'),
        'success_messages': success_messages,
        'fail_messages': fail_messages,
        'order_ids': updated_order_ids
        }
    return JsonResponse(response, status=200)

@api_view(['GET'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_order_statuses(request):
    records = Status.objects.filter(hidden=False).order_by('show_at_position')
    serializer = StatusSerializer(records, many=True)
    return JsonResponse({'success': True, 'records': serializer.data})

@csrf_exempt
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
def send_to_delivery_company(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        reseller = request.user
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
        
    success_messages = []
    fail_messages = []

    delivery_request_status = Status.objects.get(code='with_delivery_company')
    if json_data['orders_to_sent']:
        process_sending_orders(json_data['orders_to_sent'], reseller, delivery_request_status, success_messages, fail_messages)
      
    return JsonResponse({
        'success': True, 
        'success_messages': success_messages,
        'fail_messages': fail_messages
    }, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
def get_previous_states(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    reseller = request.user

    response_data = get_records('OrderDeliveryStatusHistory', json_data, None, company_filter=Q(order__warehouse__reseller=reseller))
    return JsonResponse(response_data, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
def set_user_preferences(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    reseller = request.user
    
    reseller_user_preferences, _ = ResellerUserPreferences.objects.get_or_create(user=reseller)
    reseller_user_preferences.set_order_tree_view(json_data.get('order_tree_view', []))
    reseller_user_preferences.save()
    serializer = ResellerUserPreferencesSerializer(reseller_user_preferences)
    return JsonResponse({'success': True, 'user_preferences': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
def get_user_preferences(request):

    reseller = request.user
    
    try:
        user_preferences = ResellerUserPreferences.objects.get(user=reseller)
    except ResellerUserPreferences.DoesNotExist:
        user_preferences = ResellerUserPreferences.objects.create(user=reseller)
    serializer = ResellerUserPreferencesSerializer(user_preferences)
    return JsonResponse({'success': True, 'user_preferences': serializer.data}, status=200)
