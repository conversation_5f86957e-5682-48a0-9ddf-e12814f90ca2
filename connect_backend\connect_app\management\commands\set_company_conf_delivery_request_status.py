from django.core.management.base import BaseCommand
from django.db import transaction
from api.users.user_model import CompanyConf
from api.orders.order_model import Status

class Command(BaseCommand):
    help = "Update company_conf default_delivery_request_status to 'with_delivery_company' for all Company"

    def handle(self, *args, **kwargs):
        self.stdout.write(self.style.NOTICE("Starting Company configuration update..."))

        with transaction.atomic():
            conf_to_update = CompanyConf.objects.all()
            updated_count = 0
            with_delivery_company_status = Status.objects.get(code='with_delivery_company')
            for conf in conf_to_update:
                conf.delivery_request_status = with_delivery_company_status
                conf.save(update_fields=["delivery_request_status"])
                updated_count += 1
            self.stdout.write(self.style.SUCCESS(f"Updated {updated_count} company configurations."))
        self.stdout.write(self.style.SUCCESS("Company Configuration update completed."))
