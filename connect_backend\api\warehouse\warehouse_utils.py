from ..dashboard.dashboard_utils import get_date_filter
from ..util_functions import *
from django.db import transaction
from ..orders.order_utils import *
import boto3
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils.translation import gettext as _


def create_warehouse_util(values):
    try:
        with transaction.atomic():
            warehouse = Warehouse.objects.create(**values)
    except Exception as e:
        print(f'An error occurred while creating warehouse: {e}')

def validate_warehouse(values):
    required_fields = ['name', 'address', 'area', 'country']
    validation_errors=[]
    foreign_key_objects = {}

    for field in required_fields:
        if not values.get(field):
            validation_errors.append(f'Missing required field: {field}')

    foreign_key_objects.update(warehouse_foreign_keys(values, validation_errors))

    return validation_errors, foreign_key_objects

def warehouse_foreign_keys(values, validation_errors):
    foreign_key_objects = {}

    if values.get('country'):
        foreign_key_objects['country'] = get_country(values, validation_errors)

    if values.get('area'):
        foreign_key_objects['area'] = get_area(values, validation_errors, foreign_key_objects['country'] if foreign_key_objects['country'] else None)

    return foreign_key_objects

def update_warehouse_stock_util(values, warehouse_id):
    try:
        with transaction.atomic():
            variant = WarehouseVariant.objects.get(id=values.pop('variant'))
            type = values.pop('type')
            physical_quantity = 0
            if type:
                if type == 'add':
                    physical_quantity = variant.physical_quantity + values.get('quantity',0)
                elif type == 'set':
                    physical_quantity = values.get('quantity',0)
                else:
                    return
                physical_change = physical_quantity - variant.physical_quantity
            
                if physical_change:
                    warehouse_tracking = WarehouseTracking.objects.create(
                        warehouse_variant=variant,
                        physical_change=physical_change,
                        virtual_change=physical_change,
                        previous_physical = variant.physical_quantity,
                        previous_virtual = variant.virtual_quantity,
                        user=values['updated_by'],
                    )
                variant.physical_quantity = physical_quantity
                variant.virtual_quantity = variant.virtual_quantity + physical_change
                variant.save() 
    except Exception as e:
        raise e

def create_aproval_record(variant, user, status, warehouse):
    try:
        with transaction.atomic():
            variant_instance = WarehouseVariant.objects.get(id=variant.get('variant'))
            type = variant.pop('type')
            new_physical_quantity = 0
            new_virtual_quantity = 0
            if type:
                if type == 'add':
                    new_physical_quantity = variant_instance.physical_quantity + variant.get('quantity',0)
                elif type == 'set':
                    new_physical_quantity = variant.get('quantity',0)
                else:
                    return 
                physical_change = new_physical_quantity - variant_instance.physical_quantity
                new_virtual_quantity = variant_instance.virtual_quantity +  physical_change
                new_reserved_quantity = new_physical_quantity - new_virtual_quantity
                difference_user_added = new_physical_quantity - variant_instance.physical_quantity
                stock_approval = StockApproval.objects.create(
                    warehouse_variant=variant_instance,
                    new_physical_quantity=new_physical_quantity,
                    new_virtual_quantity=new_virtual_quantity,
                    new_reserved_quantity=new_reserved_quantity,
                    user=user,
                    status=status,
                    difference_user_added=difference_user_added,
                    company=user.company,
                    warehouse=warehouse
                )

                return stock_approval
    except Exception as e:
        raise e


def update_approval_record_business(values,user):
    
    current_status = values.get('current_status')
    stock_approval_id = values.get('stock_approval_id')
    is_approved = values.get('is_approved')

    with transaction.atomic():
        if current_status == 'pending' and stock_approval_id:
            stock_approval_record = StockApproval.objects.select_for_update().get(id=stock_approval_id)
            if not is_approved:
                stock_approval_record.status = 'rejected'
                stock_approval_record.save(update_fields=['status'])
            elif is_approved:
                if not stock_approval_record.warehouse:
                    warehouse = get_related_warehouse(stock_approval_record,user)
                    if warehouse:
                        stock_approval_record.warehouse = warehouse
                        stock_approval_record.save(update_fields=['warehouse'])
                    else:
                        raise get_error_response('GENERAL_006', {'validation_errors': _('You are trying to update variants that are not connected to any warehouse.')})

                else:
                    warehouse = stock_approval_record.warehouse

                if warehouse :
                    if warehouse.reseller:
                        stock_approval_record.status = 'pending_warehouse_approval'
                        stock_approval_record.save(update_fields=['status'])
                    else:
                        update_approval_record(values,user, user.company, stock_approval_record)
                else:
                    raise get_error_response('GENERAL_006', {'validation_errors': _('You are trying to update variants that are not connected to any warehouse.')})


def update_approval_record_reseller(values, user):
    current_status = values.get('current_status')
    stock_approval_id = values.get('stock_approval_id')
    is_approved = values.get('is_approved')

    with transaction.atomic():
        if current_status == 'pending_warehouse_approval' and stock_approval_id:
            stock_approval_record = StockApproval.objects.select_for_update().get(id=stock_approval_id)
            if not is_approved:
                stock_approval_record.status = 'warehouse_rejected'
                stock_approval_record.save(update_fields=['status'])
            elif is_approved:
                update_approval_record(values, user, stock_approval_record.company, stock_approval_record)

def update_approval_record(values, user, company, stock_approval_record):
    try:
        stock_approval_id = values.get('stock_approval_id')
        variant_id = values.get('variant_id')

        if stock_approval_record.excel_file:
            s3_client = boto3.client(
                's3',
                aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                region_name=settings.AWS_S3_REGION_NAME,
            )
            bucket_name = settings.AWS_STORAGE_BUCKET_NAME
            parsed_url = urlparse(stock_approval_record.excel_file)
            s3_key = parsed_url.path.lstrip('/')
            response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
            excel_file = BytesIO(response["Body"].read())
            content_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

            uploaded_file = SimpleUploadedFile(
                name=bucket_name,
                content=excel_file.getvalue(),
                content_type=content_type
            )
            process_excel_variants(uploaded_file, user, company,stock_approval_record)
            stock_approval_records_needs_update = StockApproval.objects.filter(company=company,status='pending').exclude(id=stock_approval_id)
            handle_warehouse_approval(stock_approval_records_needs_update, None)
            
        else:
            new_values = {
                'variant': variant_id,
                'stock_approval_id': stock_approval_id,
                'updated_by': user.name,
            }
            check_warehouse_variants(new_values)
            variant_instance = WarehouseVariant.objects.select_for_update().get(id=variant_id)
            stock_approval_records_needs_update = StockApproval.objects.filter(
                warehouse_variant__id=variant_id,
                status='pending'
            ).exclude(id=stock_approval_id)
            handle_warehouse_approval(stock_approval_records_needs_update, variant_instance)
        stock_approval_record.status = 'approved'
        stock_approval_record.save(update_fields=['status'])
    except Exception as e:
        raise e

def handle_warehouse_approval(stock_approval_records_needs_update, variant_instance = None):
    update_list = []
    for stock in stock_approval_records_needs_update:
        variant = variant_instance or stock.warehouse_variant
        if variant :
            stock.new_physical_quantity = stock.difference_user_added + variant.physical_quantity
            stock.new_virtual_quantity = stock.difference_user_added + variant.virtual_quantity
            stock.new_reserved_quantity = stock.new_physical_quantity - stock.new_virtual_quantity
            update_list.append(stock)
    if len(update_list):
        StockApproval.objects.bulk_update(update_list, ['new_physical_quantity', 'new_virtual_quantity', 'new_reserved_quantity'])
    return

def get_company_conf(company):
    try:
        return CompanyConf.objects.get(company=company)
    except CompanyConf.DoesNotExist:
        raise get_error_response(
            'GENERAL_002', 
            {'model': 'CompanyConf', 'field': 'company', 'value':company.company_id}
        )

def get_total_physical_stock(user, warehouse):
    warehouse_totals = WarehouseVariant.objects.filter(warehouse=warehouse, variant__product__company=user.company).aggregate(Sum('physical_quantity')) 
    summary = {
        "count" : warehouse_totals['physical_quantity__sum'],
    }
    return summary 

def get_total_virtual_stock(user, warehouse):
    warehouse_totals = WarehouseVariant.objects.filter(warehouse=warehouse, variant__product__company=user.company).aggregate(Sum('virtual_quantity')) 
    summary = {
        "count" : warehouse_totals['virtual_quantity__sum'],
    }
    return summary 

def get_product_out_of_stock(user, warehouse):
    warehouse_variants_out_of_stock = WarehouseVariant.objects.filter(warehouse=warehouse, variant__product__company=user.company, physical_quantity__lte=0).count()

    filters = [
        {"operator": "and", "filters": [
            {"field":"variant__product__active","operator":"exact","value":True},
            {"field": "warehouse__id", "operator": "exact", "value":warehouse.id},
            {"field": "variant__product__company__company_id", "operator": "exact", "value":user.company.company_id},
            {"field": "physical_quantity", "operator": "lte", "value": 0}
        ]}
    ]
    summary = {
        "count" : warehouse_variants_out_of_stock,
        "filters":filters
    }
    return summary 

def get_products_almost_out_of_stock(user, warehouse):
    companyconf = get_company_conf(user.company)
    warehouse_variants_almost_out_of_stock = WarehouseVariant.objects.filter(warehouse=warehouse, variant__product__company=user.company, physical_quantity__lte=companyconf.critical_stock_number).count()
    filters = [
        {"operator": "and", "filters": [
            {"field":"variant__product__active","operator":"exact","value":True},
            {"field": "warehouse__id", "operator": "exact", "value":warehouse.id},
            {"field": "variant__product__company__company_id", "operator": "exact", "value":user.company.company_id},
            {"field": "physical_quantity", "operator": "lte", "value":companyconf.critical_stock_number},
            
        ]}
    ]
    summary = {
        "count" : warehouse_variants_almost_out_of_stock,
        "filters":filters
    }
    return summary 


def get_warehouse_logs_based_on_date(user, start_date,end_date, warehouse):
    date_filter = get_date_filter(start_date, end_date)
    warehouse_logs = WarehouseTracking.objects.filter(date_filter,warehouse_variant__warehouse=warehouse, warehouse_variant__variant__product__company=user.company).count()
    filters = []
    summary = {
        "count" : warehouse_logs,
        "filters":filters
    }
    return summary 

def process_excel_variants(file,user, company,stock_approval_record):
    dataFile = None
    if file.content_type == 'text/csv':
        dataFile = pd.read_csv(file, dtype=str)
    else:
        dataFile = pd.read_excel(file, header=0, dtype=str)
    
    if dataFile is not None and not dataFile.empty:
        variants_to_update = []
        logs = []
        affected_products = set()
        for _, row in dataFile.iterrows():
            if 'ID' in row and row['ID'] and not pd.isna(row['ID']):
                variant = WarehouseVariant.objects.filter(variant__id=int(row['ID']),warehouse=stock_approval_record.warehouse).first()
            elif 'SKU' in row and row['SKU'] and not pd.isna(row['SKU']):
                variant = WarehouseVariant.objects.filter(variant__sku=row['SKU'],warehouse=stock_approval_record.warehouse).first()
            else:
                continue
            if variant and variant.id and not pd.isna(row.Quantity) and int(row.Quantity):
                logs.append(WarehouseTracking(
                warehouse_variant=variant,
                physical_change=int(row.Quantity),
                virtual_change=int(row.Quantity),
                previous_physical = variant.physical_quantity,
                previous_virtual = variant.virtual_quantity,
                user=user.name + ' By Excel',
                ))
                variant.physical_quantity = variant.physical_quantity + int(row.Quantity)
                variant.virtual_quantity = variant.virtual_quantity + int(row.Quantity)
                variant.reserved_quantity = variant.physical_quantity - variant.virtual_quantity
                variant.updated_at = timezone.now()
                affected_products.add(variant.variant.product.id)
                variants_to_update.append(variant)
        if variants_to_update:
            WarehouseVariant.objects.bulk_update(variants_to_update, ["physical_quantity",'virtual_quantity','reserved_quantity','updated_at'])
        if logs:
            WarehouseTracking.objects.bulk_create(logs)
        if affected_products:
            total_stock = WarehouseVariant.objects.filter(variant__product_id__in=affected_products).values('variant__product_id').annotate(total_physical=Sum('physical_quantity'),total_virtual=Sum('virtual_quantity'))
            products_to_update = Product.objects.filter(id__in=affected_products)
            products_map = {}
            for stock in total_stock:
                products_map[stock['variant__product_id']]={
                    'total_physical': stock['total_physical'],
                    'total_virtual': stock['total_virtual']
                }
            for product in products_to_update:
                if product.id in products_map:
                    product.physical_stock = products_map[product.id]['total_physical']
                    product.virtual_stock = products_map[product.id]['total_virtual']
            Product.objects.bulk_update(products_to_update,['physical_stock','virtual_stock'])


    return 

def check_warehouse_variants(values):
    variant = WarehouseVariant.objects.get(id=values.pop('variant'))
    stock_approval_record = StockApproval.objects.get(id=values.pop('stock_approval_id'))
    physical_change = stock_approval_record.difference_user_added
        
    if physical_change:
        warehouse_tracking = WarehouseTracking.objects.create(
            warehouse_variant=variant,
            physical_change=physical_change,
            virtual_change=physical_change,
            previous_physical = variant.physical_quantity,
            previous_virtual = variant.virtual_quantity,
            user=values['updated_by'],
        )
        variant.physical_quantity = physical_change + variant.physical_quantity
        variant.virtual_quantity = physical_change + variant.virtual_quantity
        variant.save()

def update_warehouse_util(values, warehouse):
    warehouse.name = values.get('name', warehouse.name)
    warehouse.country = values.get('country', warehouse.country)
    warehouse.area = values.get('area', warehouse.area)
    warehouse.address = values.get('address', warehouse.address)

    warehouse.save()
    return warehouse

def create_location_util(values,user=None):
    try:
        with transaction.atomic():
            rack_id = values.pop('rack','')
            rack = None
            if rack_id:
                rack = Location.objects.filter(id=rack_id, warehouse__id=values.get('warehouse_id')).first()
            
            if values.get('type') == 'palette':
                location = Location.objects.create(**values)
                return location
            elif values.get('type') == 'rack':
                location = Location.objects.create(**values)
                return location
            elif values.get('type') == 'shelf':
                parent = None
                if rack:
                    parent = rack                    
                location = Location.objects.create(**values, parent=parent)
                return location
    except IntegrityError as e:
        if 'unique_location_name_per_warehouse' in str(e):
            raise get_error_response('GENERAL_008', {'field': 'name', 'value': values['name']})
        raise e
    except Exception as e:
        raise e
    
def proceed_location_excel(file,user,warehouse_id):
    header_mapping = {
        'name': 'name',
        'type': 'type',
        'parent': 'parent',
    }
    
    required_fields = ['name', 'type']
    df = read_and_validate_excel(file, header_mapping, required_fields)
    locations = process_locations_from_df(df, user, warehouse_id)
    return locations

def process_locations_from_df(df, user, warehouse_id):
    locations = []
    for index, row in df.iterrows():
        values = row.to_dict()    
        values['warehouse_id'] = warehouse_id    
        if not values.get('name') or pd.isna(values.get('name')):
            raise get_error_response('GENERAL_003', {'fields': 'name'})
        
        if not values.get('type') or pd.isna(values.get('type')):
            raise get_error_response('GENERAL_003', {'fields': 'type'})
        
        if values.get('parent') and not pd.isna(values.get('parent')):
            parent_location = Location.objects.filter(name=values['parent'], warehouse__id=values.get('warehouse_id')).first()
            if not parent_location:
                raise get_error_response('GENERAL_002', {'model': 'Location', 'field': 'parent', 'value': values.get('parent')})
            values['parent'] = parent_location
        else:
            values.pop('parent', None)
        try:
            q_filter = Q(
                name=values['name'],
                warehouse_id=values['warehouse_id'],
                type=values.get('type'),
                parent=values.get('parent', None)
            )
            existing_location = Location.objects.filter(q_filter).first()
            if existing_location:
                raise get_error_response('GENERAL_008', {'field': 'name', 'value': values['name']})
            location = Location.objects.create(**values)
            locations.append(location)
        except IntegrityError as e:
            if 'unique_location_name_per_warehouse' in str(e):
                raise get_error_response('GENERAL_008', {'field': 'name', 'value': values['name']})
            raise e
        except Exception as e:
            raise e
    
    return locations


def proceed_location_with_products_excel(file,user,warehouse_id):
    header_mapping = {
        'location_name': 'location_name',
        'product_name_or_sku': 'product_name_or_sku',
        'quantity': 'quantity',
    }
    
    required_fields = ['location_name','product_name_or_sku', 'quantity']
    df = read_and_validate_excel(file, header_mapping, required_fields)
    locations = process_locations_with_products_from_df(df, user, warehouse_id)
    return locations

def process_locations_with_products_from_df(df,user,warehouse_id):
    locations = []
    for index, row in df.iterrows():
        values = row.to_dict()    
        values['warehouse_id'] = warehouse_id    
        if not values.get('location_name') or pd.isna(values.get('location_name')):
            raise get_error_response('GENERAL_003', {'fields': 'location_name'})

        if not values.get('product_name_or_sku') or pd.isna(values.get('product_name_or_sku')):
            raise get_error_response('GENERAL_003', {'fields': 'product_name_or_sku'})
        
        if not values.get('quantity') or pd.isna(values.get('quantity')):
            raise get_error_response('GENERAL_003', {'fields': 'quantity'})
        
        try:
            q_filter = Q(
                name=values['location_name'],
                warehouse_id=values['warehouse_id'],
            )
            existing_location = Location.objects.filter(q_filter).first()
            if not existing_location:
                raise get_error_response('GENERAL_002', {'model': 'Location', 'field': 'name', 'value': values['location_name']})
            else:
                product = WarehouseVariant.objects.filter(
                    warehouse__id=warehouse_id
                ).filter(
                    Q(variant__sku=values['product_name_or_sku']) | Q(variant__name=values['product_name_or_sku'])
                ).first()
                if not product:
                    raise get_error_response('GENERAL_002', {'model': 'WarehouseVariant', 'field': 'product', 'value': values['product_name_or_sku']})
                
                location_product = LocationProduct.objects.update_or_create(
                    location=existing_location,
                    product=product,
                    defaults={'quantity': values['quantity']}
                )
            locations.append(location_product)
        except Exception as e:
            raise e
        
    return locations


def change_location_util(values):
    products = values.get('products', [])
    new_location_id = values.get('new_location_id', None)
    current_location_id = values.get('current_location_id', None)
    new_location_products = []
    if not new_location_id or not current_location_id or not products:
        raise get_error_response('GENERAL_003', {'fields': 'new_location_id, current_location_id,products'})
    with transaction.atomic():
        new_location = Location.objects.get(id=new_location_id)
        current_location = Location.objects.get(id=current_location_id)
        for product in products:
            quantity = int(product.get('quantity_to_move', 0))
            product_id = product.get('product_id', None)
            if not product_id:
                raise get_error_response('GENERAL_003', {'fields': 'product_id'})
            current_location_product = LocationProduct.objects.filter(location=current_location, product__id=product_id).first()
            if not current_location_product:
                raise get_error_response('GENERAL_002', {'model': 'LocationProduct', 'field': 'product', 'value': product_id})
            if current_location_product.quantity < quantity:
                raise get_error_response('GENERAL_003', {'fields': 'quantity_to_move'})
            current_location_product.quantity -= quantity
            if current_location_product.quantity == 0:
                current_location_product.delete()
            else:
                current_location_product.save()
            new_location_product = LocationProduct.objects.filter(location=new_location, product__id=product_id).first()
            if new_location_product:
                new_location_product.quantity += quantity
                new_location_product.save()
            else:
                new_location_product = LocationProduct.objects.create(location=new_location, product_id=product_id, quantity=quantity)

            new_location_products.append(new_location_product)
    
    return new_location_products

def get_related_warehouse(stock_approval_record,user):
    if stock_approval_record.warehouse_variant:
        return stock_approval_record.warehouse_variant.warehouse
    
    elif stock_approval_record.excel_file:
        parsed_url = urlparse(stock_approval_record.excel_file)
        s3_key = parsed_url.path.lstrip('/')
        bucket_name = settings.AWS_STORAGE_BUCKET_NAME
        s3_client = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_S3_REGION_NAME,
        )
        response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
        excel_file = BytesIO(response["Body"].read())
        df = pd.read_excel(excel_file, dtype=str)
        if df is not None and not df.empty:
            variant_filter = Q(warehouse__warehouse_connection__connection_status__iexact="connected",warehouse__warehouse_connection__company_id=user.company) | Q(warehouse__reseller__isnull=True,warehouse__company_id=user.company)
            for _,row in df.iterrows():
                if 'ID' in row and row['ID'] and not pd.isna(row['ID']):
                    variant = WarehouseVariant.objects.filter(variant__id=int(row['ID'])).filter(variant_filter).first()
                elif 'SKU' in row and row['SKU'] and not pd.isna(row['SKU']):
                    variant = WarehouseVariant.objects.filter(variant__sku=row['SKU']).filter(variant_filter).first()
                else:
                    continue
                if variant and variant.id:
                    return variant.warehouse

def check_excel_file(excel_url,user,warehouse_id):

    if not excel_url:
        raise get_error_response('GENERAL_003', {'fields': 'excel_url'})
    
    parsed_url = urlparse(excel_url)
    s3_key = parsed_url.path.lstrip('/')
    bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    s3_client = boto3.client(
        's3',
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name=settings.AWS_S3_REGION_NAME,
    )
    
    try:
        response = s3_client.get_object(Bucket=bucket_name, Key=s3_key)
        excel_file = BytesIO(response["Body"].read())
        df = pd.read_excel(excel_file, dtype=str)
        repeated_variants_message = []
        if df is not None and not df.empty:
            for _,row in df.iterrows():
                if 'SKU' in row and row['SKU'] and not pd.isna(row['SKU']):
                    variants = WarehouseVariant.objects.filter(variant__sku=row['SKU'],warehouse__id=warehouse_id)
                    if variants.count() > 1:
                        variants_name = ', '.join([variant.variant.name for variant in variants])
                        repeated_variants_message.append(f"SKU {row['SKU']} has multiple variants: {variants_name}")
        return repeated_variants_message
    except Exception as e:
        raise get_error_response('GENERAL_002', {'model': 'ExcelFile', 'field': 'url', 'value': excel_url})