from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from .lookup_model import *
from django.forms.models import model_to_dict
from ..serializers import *
from ..util_functions import *
from rest_framework.permissions import AllowAny
from ..permissions import *
from rest_framework.decorators import *
from ..error.error_model import *
from django.utils.translation import gettext as _

@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_countries(request): 
    records = Country.objects.all()
    serializer = CountrySerializer(records, many=True)
    return JsonResponse({'records': serializer.data})
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_areas(request):
    records = Area.objects.all()
    serializer = AreaSerializer(records, many=True)
    return JsonResponse({'success': True,'records': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_sub_areas(request):
    records = SubArea.objects.all()
    serializer = SubAreaSerializer(records, many=True)
    return JsonResponse({'success': True, 'records': serializer.data}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_country_by_code(request):
    code = request.GET.get('code')
    try:
        country = Country.objects.get(code=code)
    except Country.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Country', 'field': 'code', 'value': code})
    
    serializer = CountrySerializer(country)
    return JsonResponse({'success': True,'record': serializer.data}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_area_by_code(request):
    id = request.GET.get('id')
    try:
        area = Area.objects.get(id=id)
    except Area.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Area', 'field': 'id', 'value': id})

    serializer = AreaSerializer(area)
    return JsonResponse({'success': 'true','record': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_sub_area_by_code(request):
    id = request.GET.get('id')
    try:
        record = SubArea.objects.get(id=id)
    except SubArea.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'SubArea', 'field': 'id', 'value': id})
    serializer = SubAreaSerializer(record)
    return JsonResponse({'success': 'true','record': serializer}, status=200)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_area_by_country(request):
    code = request.GET.get('code')
    records = Area.objects.filter(country__code=code)
    serializer = AreaSerializer(records, many=True)
    return JsonResponse({'success': True, 'records': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_sub_area_by_area(request):
    id = request.GET.get('id')
    records = SubArea.objects.filter(area__id=id)
    serializer = SubAreaSerializer(records, many=True)
    return JsonResponse({'success': True, 'records': serializer.data}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_area_sub_area(request):
    country_code = request.GET.get('code', None)
    
    if country_code:
        countries = Country.objects.filter(code=country_code).prefetch_related('areas__subareas')
    else:
        countries = Country.objects.prefetch_related('areas__subareas').all()
    
    serializer = CountrySerializer(countries, many=True, context={'request': request, 'show_areas': True, 'show_subareas': True})
    return JsonResponse({'success': True, 'countries': serializer.data}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_sub_area_by_country(request):
    country_code = request.GET.get('code', None)
    sub_areas = SubArea.objects.filter(area__country__code=country_code)
    serializer = SubAreaSerializer(sub_areas, many=True)
    return JsonResponse({'success': True, 'sub_areas': serializer.data}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_areas_for_company(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    conf = get_company_conf(user.company)
    additional_countries = conf.additional_countries.all()
    company_country = user.company.country
    areas = Area.objects.filter(country__in=[company_country] + list(additional_countries)).order_by('country__id')
    serializer = AreaSerializer(areas, many=True)
    
    return JsonResponse({'success': True, 'records': serializer.data}, status=200)