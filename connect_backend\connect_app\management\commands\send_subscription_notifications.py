from django.core.management.base import BaseCommand
from api.billing.billing_model import Subscription
from api.users.user_model import User
from django.utils.timezone import now, timedelta
from api.notification.notification_utils import send_notification
from django.utils.translation import gettext as _

class Command(BaseCommand):
    help = "Sends notification to all Super Managers 3 days before the auto billing"

    def handle(self, *args, **kwargs):
        upcoming_billing_date = now().date() + timedelta(days=3)
        
        active_subscriptions = Subscription.objects.filter(
            end_date=upcoming_billing_date, 
            active=True,
            cancelled=False,
            stripe_subscription_id__isnull=False
        )
        for subscription in active_subscriptions:
            self.notify_super_managers(subscription)
        
        self.stdout.write(self.style.SUCCESS(f'Sent {active_subscriptions.count()} notifications to clients'))

    def notify_super_managers(self, subscription):
        title = _('Upcoming Subscription Renewal')
        message = _('Your subscription is set to auto-renew in 3 days. To ensure uninterrupted access, please make sure your payment details are up to date. If you wish to make any changes, visit your account settings before the renewal date.')
        notification_data = {
            'message': message,
            'title': title
        }

        notification_data['users'] = User.objects.filter(company=subscription.company, role__name='Super Manager')
        send_notification(notification_data)