# Django and related packages
Django>=3.2,<4.0
psycopg2-binary>=2.8
python-dotenv>=0.19.0
djangorestframework>=3.12,<3.13  # Specify the version range suitable for your Django version
requests>=2.25,<3.0  # You can adjust the version range as needed

# Add other necessary packages below

# Middleware and headers
django-cors-headers

# Image processing
Pillow

# JWT and encoding
djangorestframework-simplejwt
PyJWT
unidecode
jellyfish
python-Levenshtein

# Language detection and translation
langdetect
googletrans==4.0.0-rc1

# for weasyprint
Pango <= 1.44.0
pydyf <= 0.10.0
CFFI <= 1.15.1
html5lib <= 1.1
tinycss2 <= 1.3.0
cssselect2 <= 0.1
Pyphen <= 0.9.1
fontTools <= 4.0.0
cryptography<=3.4.7

# PDF generation
weasyprint <=60.2
Jinja2 <=3.1.4

# Barcode generation
python-barcode
qrcode

#stripe
stripe <= 10.6.0

#File Management
pandas <= 1.5.3
xlrd <= 2.0.1
openpyxl <= 3.0.10

#One Signal
onesignal-sdk==2.0.0

slack_sdk

# S3
boto3

# Translation
polib <= 1.2.0