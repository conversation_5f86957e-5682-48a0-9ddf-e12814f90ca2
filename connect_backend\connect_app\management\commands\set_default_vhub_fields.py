from django.core.management.base import BaseCommand
from api.users.user_model import Company
from api.orders.order_model import VhubFieldMap, StatusMap
from django.db import transaction

class Command(BaseCommand):
    help = 'Ensure default VhubFieldMap records exist for each company'

    def handle(self, *args, **options):
        # Define the default fields
        vhub_fields = {
            'total_cod': {
                'vhub_field': 'copy_total_cost',
                'is_foreign_key': False,
            },
            'note': {
                'vhub_field': 'note',
                'is_foreign_key': False,
            },
            'area': {
                'vhub_field': 'customer_area',
                'is_foreign_key': True,
            },
            'sub_area': {
                'vhub_field': 'customer_sub_area',
                'is_foreign_key': True,
            },
            'status': {
                'vhub_field': 'state',
                'is_foreign_key': True,
            },
            'paid': {
                'vhub_field': 'paid',
                'is_foreign_key': False,
            },
            'customer_mobile': {
                'vhub_field': 'customer_mobile',
                'is_foreign_key': False,
            },
            'address': {
                'vhub_field': 'customer_address',
                'is_foreign_key': False,
            }
        }

        delivery_statuses = ['in_branch', 'picked_up', 'picking_up', 'waiting']
        
        # Loop through each company
        for company in Company.objects.all():
            self.stdout.write(f"Processing company: {company.company_name}")

            # Check for missing VhubFieldMap records and create them if necessary
            with transaction.atomic():
                for delivery_status in delivery_statuses:
                    status_instance = StatusMap.objects.get(status_code=delivery_status)
                    
                    for key, value in vhub_fields.items():
                        vhub_field_map, created = VhubFieldMap.objects.get_or_create(
                            company=company,
                            field=key,
                            status=status_instance,
                            defaults={
                                'vhub_field': value['vhub_field'],
                                'is_foreign_key': value['is_foreign_key']
                            }
                        )
                        if created:
                            self.stdout.write(f"Created {key} for status '{delivery_status}' in company '{company.company_name}'")
                        else:
                            self.stdout.write(f"{key} for status '{delivery_status}' in company '{company.company_name}' already exists")

        self.stdout.write("Default VhubFieldMap setup complete for all companies.")
