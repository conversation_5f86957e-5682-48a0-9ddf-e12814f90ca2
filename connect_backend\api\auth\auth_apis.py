from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from datetime import timedelta
from django.utils import timezone
from ..util_functions import *
from ..auth.auth_model import *
from ..users.user_model import User
from ..users.user_utils import *
from .auth_utils import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ..permissions import *
from ..error.error_model import *
from django.utils.translation import gettext as _

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def generate_and_send_otp(request):
    json_body = parse_request_body(request)
    if not json_body:
        raise get_error_response('GENERAL_001', {})

    mobile_number = json_body.get('mobile_number')
    country_code = json_body.get('country_code')

    if not validate_mobile_number(mobile_number):
        raise get_error_response('AUTH_403', {'mobile_number': mobile_number})
    
    if User.objects.filter(mobile_number=mobile_number).exists():
        raise get_error_response('AUTH_404', {'mobile_number': mobile_number})

    otp_code = generate_otp_code()
    values = {
        'mobile_number': mobile_number,
        'code': otp_code,
        'created_at': timezone.now(),
        'expire_date': timezone.now() + timedelta(minutes=3),
        'error_trials': 0
    }

    try:
        otp = get_or_create_otp(mobile_number, values)
        update_otp_trials(otp)
        send_otp_message(country_code, mobile_number, otp_code)
        return JsonResponse({'success': True, 'message': _('OTP sent successfully')}, status=200)
    except Exception as e:
        raise get_error_response('AUTH_405', {'error': str(e)})

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def create_super_user(request):
    json_body = parse_request_body(request)
    if not json_body:
        raise get_error_response('GENERAL_001', {})

    values = json_body
    values['role'] = 1
    token = values.pop('token')
    reseller_token = values.pop('reseller_token', None)

    if User.objects.filter(mobile_number=values['mobile_number']).exists():
        raise get_error_response('AUTH_404', {'mobile_number': values['mobile_number']})

    validate_token(token)
    
    required_fields = ['mobile_number', 'name', 'password', 'address', 'area', 'country', 'country_code', 'role', 'country']
    validation_errors, user_foreign_key_objects = validate_user(values, required_fields)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})


    company_values = get_company_values(values)
    company_required_fields = ['company_mobile', 'company_name', 'company_area']
    validation_errors, company_foreign_key_objects = validate_company(company_values, company_required_fields)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
    company_values = build_company_values(company_values, company_foreign_key_objects)

    company, company_conf = create_company(company_values, values['country_code'])
    setup_new_company(company)
    values =  build_user_values(values, user_foreign_key_objects, company)
    values['is_active'] = False
    values['waiting_confirmation'] = True
    values['created_by'] = values['name']
    values['lang'] = request.LANGUAGE_CODE
    user = create_user(values)
    if not user or not company:
        raise get_error_response('AUTH_408', {})
    if reseller_token:
        try:
            handle_reseller(reseller_token, company, company_conf)
        except Exception as e:
            print(f'error handling reseller: {str(e)}')
    company.owner = user
    company.save()
    return JsonResponse({'success': True,'message': _('User Created Successfully')}, status = 201)

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def verify_otp(request):
    json_body = parse_request_body(request)
    if not json_body:
        raise get_error_response('GENERAL_001', {})
    
    mobile_number = json_body.get('mobile_number')
    otp_code = json_body.get('otp')

    if not mobile_number or not otp_code:
        raise get_error_response('GENERAL_003', {'fields': 'mobile_number, otp'})

    try:
        code = OtpCodes.objects.get(mobile_number=mobile_number, code=otp_code, expire_date__gt=timezone.now(), number_of_trials__lte=3, error_trials__lte=3)
    except OtpCodes.DoesNotExist:
        try:
            code = OtpCodes.objects.get(mobile_number=mobile_number)
        except OtpCodes.MultipleObjectsReturned:
            raise get_error_response('AUTH_409', {})
        return handle_invalid_otp(mobile_number, code)
    token = create_or_update_token(mobile_number)
    code.delete()
    return JsonResponse({'success': True, 'message': _('OTP is valid'), 'token': token.token}, status=200)

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_token(request):
    json_body = parse_request_body(request)
    if not json_body:
        raise get_error_response('GENERAL_001', {})

    mobile_number = json_body.get('mobile_number')
    password = json_body.get('password')
    country_code = json_body.get('country_code')

    if mobile_number is None or password is None:
        raise get_error_response('GENERAL_003', {'fields': 'mobile_number, password'})


    try:
        if country_code in ["+970", "+972"]:
            my_user = User.objects.get(mobile_number=mobile_number, country_code__in=["+970", "+972"])
        elif country_code:
            my_user = User.objects.get(mobile_number=mobile_number, country_code=country_code)
        else: #to be removed, this "else" added to support backward compatibility
            my_user = User.objects.get(mobile_number=mobile_number)
    except (User.DoesNotExist, User.MultipleObjectsReturned) as e:
        raise get_error_response('AUTH_412', {})
    
    if not my_user.user:
        raise get_error_response('AUTH_411', {})

    if my_user.is_deleted:
        raise get_error_response('AUTH_414', {})
    username = extract_username(my_user)
    user = validate_credentials(username, password)
    user_data = UserSerializer(my_user).data
    token = generate_JWT(user, connect_user_id=my_user.id)
    return JsonResponse({'success': True, 'token': token, 'user': user_data}, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def extend_token_validity(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    token = generate_JWT(user.user, connect_user_id=user.id)

    return JsonResponse({'token': token})

@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def reset_password_otp(request):
    json_body = parse_request_body(request)
    if not json_body:
        raise get_error_response('GENERAL_001', {})
    
    mobile_number = json_body.get('mobile_number')
    country_code = json_body.get('country_code')
    if not validate_mobile_number(mobile_number):
        raise get_error_response('AUTH_403', {'mobile_number': mobile_number})

    try:
        if country_code in ["+970", "+972"]:
            user = User.objects.get(mobile_number=mobile_number, country_code__in=["+970", "+972"])
        elif country_code:
            user = User.objects.get(mobile_number=mobile_number, country_code=country_code)
        else:
            user = User.objects.get(mobile_number=mobile_number)
    except (User.DoesNotExist, User.MultipleObjectsReturned) as e:
        raise get_error_response('AUTH_412', {})
    
    if user.is_deleted:
        raise get_error_response('AUTH_414', {})
    
    if not user.is_active:
        raise get_error_response('AUTH_415', {})

    otp_code = generate_otp_code()
    otp_values = {
        'mobile_number': mobile_number,
        'code': otp_code,
        'created_at': timezone.now(),
        'expire_date': timezone.now() + timedelta(minutes=3),
        'error_trials': 0
    }
    try:
        otp = get_or_create_otp(mobile_number, otp_values)
        update_otp_trials(otp)
        send_otp_message(user.country_code, mobile_number, otp_code)
    except Exception as e:
        raise get_error_response('AUTH_405', {'error': str(e)})
    return JsonResponse({'success': True, 'message': _('OTP sent successfully')})
    
@api_view(['POST'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def reset_password(request):
    json_body = parse_request_body(request)
    if not json_body:
        raise get_error_response('GENERAL_001', {})

    mobile_number = json_body.get('mobile_number')
    password = json_body.get('password')
    token = json_body.get('token')
    country_code = json_body.get('country_code')

    if not mobile_number or not password:
        raise get_error_response('GENERAL_003', {'fields': 'mobile_number, password'})

    if not User.objects.filter(mobile_number=mobile_number).exists():
        raise get_error_response('AUTH_414', {})

    validate_token(token)
    try:
        if country_code in ["+970", "+972"]:
            user = User.objects.get(mobile_number=mobile_number, country_code__in=["+970", "+972"])
        elif country_code:
            user = User.objects.get(mobile_number=mobile_number, country_code=country_code)
        else:
            user = User.objects.get(mobile_number=mobile_number)
    except (User.DoesNotExist, User.MultipleObjectsReturned) as e:
        raise get_error_response('AUTH_412', {})

    try:
        username = extract_username(user)
        dj_user, created = DjUser.objects.get_or_create(username = username)
        dj_user.set_password(password)
        dj_user.is_active = True
        dj_user.groups.add(user.role.group)
        dj_user.save()
        user.user = dj_user
        user.is_active = True
        user.waiting_confirmation = False
        user.save()
    except User.DoesNotExist:
        raise get_error_response('AUTH_414', {})
    
    return JsonResponse({'success': True, 'message': _('Password updated successfully')}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_permissions(request):
    user_groups = request.user.groups.all()
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    if not user_groups.exists():
        if user.role:
            request.user.groups.add(user.role.group)
            user_groups = request.user.groups.all()

    permissions = Permission.objects.filter(group__in=user_groups).distinct().order_by('id')
    permissions_data = PermissionSerializer(permissions, many=True).data

    return JsonResponse({
        "success": True,
        "permissions": permissions_data
    }, status=200)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_otp(request):
    mobile_number = request.GET.get('mobile_number')
    if not mobile_number:
        raise get_error_response('GENERAL_003', {'fields': 'mobile_number'})
    try:
        otp = OtpCodes.objects.get(mobile_number = mobile_number)
    except OtpCodes.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'OTP codes', 'field': 'mobile_number', 'value': mobile_number})
    return JsonResponse({
        "success": True,
        "otp": otp.code
    }, status=200)