from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from ...util_functions import *
from ...users.user_utils import *
from ...auth.auth_utils import *
from .user_model import *
from .user_utils import *
from .backends import *
from ...orders.order_utils import get_product as get_product_by_id
from ...lookups.lookup_utils import *
from .user_utils import *
from ..mystore_utils import *

@csrf_exempt
def generate_and_send_otp(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Method not allowed'}, status=405)
    
    json_body = parse_request_body(request)
    if not json_body:
        return JsonResponse({'success': False, 'message': 'Invalid JSON format in request body'}, status=400)
    
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    mobile_number = json_body.get('mobile_number')
    country_code = json_body.get('country_code')

    if not validate_mobile_number(mobile_number):
        return JsonResponse({'success': False, 'message': 'MISSING_MOBILE_NUMBER_OR_OTP'}, status=400)
    
    if StoreUser.objects.filter(mobile_number = mobile_number, store_id = mystore.id).exists():
        return JsonResponse({'success':False,'error': 'USED_MOBILE_NUMBER'}, status=400)
    
    # return random otp code
    otp_code = generate_otp_code()

    values = {
        'mobile_number': mobile_number,
        'code': otp_code,
        'created_at': timezone.now(),
        'expire_date': timezone.now() + timedelta(minutes=3),
        'error_trials': 0
    }

    try:
        # create otp based on specific mobile number
        otp = get_or_create_otp(mobile_number, values)
        update_otp_trials(otp)
        send_otp_message(country_code, mobile_number, otp_code)
        return JsonResponse({'success': True, 'message': 'OTP sent successfully'}, status=200)    
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
def verify_otp(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Method not allowed'}, status=405)
    
    json_body = parse_request_body(request)
    if not json_body:
        return JsonResponse({'success': False, 'message': 'Invalid JSON format in request body'}, status=400)
    
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    mobile_number = json_body.get('mobile_number')
    otp_code = json_body.get('otp') 

    if not mobile_number or not otp_code:
        return JsonResponse({'success': False, 'error': 'MISSING_MOBILE_NUMBER_OR_OTP'}, status=400)
    try:
        code = OtpCodes.objects.get(mobile_number=mobile_number, code=otp_code, expire_date__gt=timezone.now(), number_of_trials__lte=3)
        if not code:
            return JsonResponse({'success': False, 'error': 'INVALID_OTP'}, status=404)
        token = create_or_update_token(mobile_number)
        code.delete()
        return JsonResponse({'success': True, 'message': 'OTP is valid', 'token': token.token}, status=200)
    except OtpCodes.DoesNotExist:
        try:
            code = OtpCodes.objects.get(mobile_number=mobile_number)
            return handle_invalid_otp(mobile_number, code)
        except OtpCodes.MultipleObjectsReturned:
            return JsonResponse({'success': False, 'error': 'OTP_LIMIT_EXCEEDED'}, status=400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
def create_store_user(request):
    if request.method != 'POST':
        return JsonResponse({'success':False,'message': 'Method not allowed'}, status=405)
    
    json_body = parse_request_body(request)
    if not json_body:
        return JsonResponse({'success': False, 'message': 'Invalid JSON format in request body'}, status=400)
    
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)


    values = json_body
    token = values.pop('token')

    if StoreUser.objects.filter(mobile_number=values['mobile_number']).exists():
        return JsonResponse({'success':False,'error': 'USED_MOBILE_NUMBER'}, status=400)
    
    try:
        validate_token(token)
    except ValueError as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)
    
    required_fields = [
        'mobile_number', 
        'name',
        'password', 
        'address', 
        'area', 
        'country', 
        'country_code'
        ]
    validation_errors, user_foreign_key_objects = validate_user(values, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'message': validation_errors}, status=400)

    try:
        values  = build_store_user_values(values,user_foreign_key_objects,mystore.company,mystore)
        user = create_user_store_util(values)
        if not user:
            return JsonResponse({'success': False,'message':'Failed to create user '}, status=500)
        return JsonResponse({'success': True,'message':'User Created Successfully'}, status = 201)
    except Exception as e:
        return JsonResponse({'success':False,'message':e.args}, status=400)

@csrf_exempt
def get_token(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_body = parse_request_body(request)
    if not json_body:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    mobile_number = json_body.get('mobile_number')
    password = json_body.get('password')

    if mobile_number is None or password is None:
        return JsonResponse({'success': False, 'error': 'Please provide both mobile number and password'}, status=400)
    
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    try:
        user = StoreUser.objects.get(mobile_number=mobile_number, company_id = mystore.company.company_id , store_id = mystore.id)
    except (StoreUser.DoesNotExist, StoreUser.MultipleObjectsReturned) as e:
        return JsonResponse({'success': False, 'error': 'User with provided mobile number does not exist'}, status=400)

    try:
        username = extract_username(user)
        user = store_validate_credentials(username,mystore)
        user = authenticate(mobile_number = mobile_number,password=password, store_id = mystore.id)
        if not user:
            raise ValueError('Invalid credentials')
        store_user_data = StoreUserSerializer(user).data
        token = generate_JWT(user)
        return JsonResponse({'success': True, 'token': token, 'user': store_user_data}, status=200)    
    except ValueError as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)

@csrf_exempt
def extend_token_validity(request):
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Token '):
        return JsonResponse({'success': False, 'error': 'Missing or invalid token'}, status=401)
    
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    token = generate_JWT(user)

    return JsonResponse({'token': token})

@csrf_exempt
def reset_password_otp(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Method not allowed'}, status=405)

    json_body = parse_request_body(request)
    if not json_body:
        return JsonResponse({'success': False, 'message': 'Invalid JSON format in request body'}, status=400)
    
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    try:
        mobile_number = json_body.get('mobile_number')

        if not validate_mobile_number(mobile_number):
            return JsonResponse({'success': False, 'error': 'MISSING_MOBILE_NUMBER'}, status=400)
        
        user = StoreUser.objects.get(mobile_number = mobile_number, store_id = mystore.id)
        if not user:
            return JsonResponse({'success': False, 'error': 'USED_MOBILE_NUMBER'}, status=400)
        
        # return random otp code
        otp_code = generate_otp_code()

        values = {
            'mobile_number': mobile_number,
            'code': otp_code,
            'created_at': timezone.now(),
            'expire_date': timezone.now() + timedelta(minutes=3),
            'error_trials': 0
        }
        otp = get_or_create_otp(mobile_number, values)
        update_otp_trials(otp)
        try:
            send_otp_message(user.country_code, mobile_number, otp_code)
        except ConnectError as e:
            response = e.to_dict()
            return JsonResponse({'success': False, 'error': response['error']['message']}, status=500)
        return JsonResponse({'success': True, 'message': 'OTP sent successfully'})
    except ValueError as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)

@csrf_exempt
def reset_password(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    json_body = parse_request_body(request)
    if not json_body:
        return JsonResponse({'success': False, 'message': 'Invalid JSON format in request body'}, status=400)
    
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    token = json_body.get('token')
    try:
        validate_token(token)
    except ValueError as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)
    
    mobile_number = json_body.get('mobile_number')
    try:
        user = StoreUser.objects.get(mobile_number=mobile_number,store_id = mystore.id)
    except:
        return JsonResponse({'success':False, 'error': 'User Does not Exist'})
    
    password = json_body.get('password')

    try:
        user.set_password(password)
        user.save()
        return JsonResponse({'success': True, 'message': 'Password updated successfully'}, status=200)
    except User.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'User not found'}, status=400)

@csrf_exempt
def get_store_user_info(request):

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
        
    fields_to_include = [
        'id',
        'mobile_number',
        'name',
        'email',
        'second_phone',
        'address',
        'area',
        'country',
        'country_code',
        'is_active',
        'language'
    ]

    objects_to_handle = ['country', 'area']

    model_map = {
        'country': Country,
        'area': Area,
    }

    related_fields = {}

    queryset = StoreUser.objects.filter(store=mystore, mobile_number = user.mobile_number)

    user = search_filter_group(queryset, json_data, fields_to_include, related_fields)
    user = object_to_json(user, objects_to_handle, model_map)
    
    response_data = {
        'success': True,
        'users': user

    }
    return JsonResponse(response_data, status=200)

@csrf_exempt
def update_store_user(request):

    if request.method != 'PUT':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    required_fields = [
        'id',
        'mobile_number',
        'name',
        'country_code',
        'email',
        'second_phone',
        'address',
        'area',
        'country',
        'language'
        ]
    
    values = json_data
    values['store'] = mystore
    values['created_by'] = user.name
    values['updated_by'] = user.name

    validation_errors, foreign_key_objects = validate_store_user(json_data, values, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)
    
    try:
        values = {**json_data, **foreign_key_objects}
        user = update_store_user_utils(values)
        
    except StoreUser.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'User not found'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    return JsonResponse({'success': True, 'message': 'User updated successfully'}, status=200)

@csrf_exempt
def add_user_favorite(request):

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    required_fields = ['product_id']

    try:
        values = {
                'user': user,
                'store': mystore,
                'product_id':json_data.get('product_id')
            }
        
        validation_errors = validate_fields(values,required_fields)
        if validation_errors:
            return JsonResponse({'success': False, 'error': validation_errors}, status=400)
    
        product = get_product_by_id(values['product_id'])
        if product:
            values['product'] = product
        user_favorite_object = StoreUserFavorites.objects.get_or_create(**values) 
    except Exception as e:
        return JsonResponse({'success': False, 'error':str(e) }, status=500)
    return JsonResponse({'success': True, 'message': 'Product added successfully'}, status=201)

@csrf_exempt
def get_user_favorites(request):

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    

    try:
        user_favorite = StoreUserFavorites.objects.filter(user=user, store=mystore).order_by('-created_at')
        print(user_favorite)
    except:
        return JsonResponse({'success': False, 'error': 'User Favorites not found'}, status=500)
    serializer = StoreUserFavoritesSerializer(user_favorite,many=True)
    return JsonResponse({'success': True, 'data': serializer.data})

@csrf_exempt
def delete_user_favorite(request):

    if request.method != 'DELETE':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    required_fields = ['id']

    try:
        values = {
                'id':json_data.get('id'),
            }
        
        validation_errors = validate_fields(values,required_fields)
        if validation_errors:
            return JsonResponse({'success': False, 'error': validation_errors}, status=400)
    
        user_favorite_object = StoreUserFavorites.objects.get(id=values['id']) 
    except:
        return JsonResponse({'success': False, 'error': 'Favorite Order not found' }, status=404)
    
    user_favorite_object.delete()

    return JsonResponse({'success': True, 'message': 'Product deleted successfully'}, status=201)

@csrf_exempt
def add_store_billing_address(request):

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    required_fields = [
    "first_name",
    "surname",
    "address_line1",
    "address_line2",
    "area",
    "sub_area",
    "country",
    "mobile_number",
    "second_mobile",
    "is_default",
    ]

    validation_errors, foreign_key_objects = validate_lookup(json_data, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)
    try:
        values = build_billing_address_values(json_data, foreign_key_objects, user, mystore)
        billing_address = StoreBillingAddress.objects.create(**values)
    except Exception as e:
        return JsonResponse({'success': False, 'error':str(e) }, status=500)
    
    return JsonResponse({'success': True, 'message': 'Billing Address Created Successfully'}, status=201)

@csrf_exempt
def get_store_billing_address(request):

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    try:
        billing_address = StoreBillingAddress.objects.filter(user=user, store=mystore).order_by('-is_default')
    
    except:
        return JsonResponse({'success': False, 'error': 'User Favorites not found'}, status=500)
    
    serializer = StoreBillingAddressSerialaizer(billing_address, many=True).data
    response_data = {
        'success': True,
        'billing_address':serializer,
        'data_count':len(serializer)
    }
    return JsonResponse(response_data, status=200)

@csrf_exempt
def update_store_billing_address(request):

    if request.method != 'PUT':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    required_fields = [
    "id",
    "first_name",
    "surname",
    "address_line1",
    "address_line2",
    "area",
    "sub_area",
    "country",
    "mobile_number",
    "second_mobile",
    "is_default",
    ]

    validation_errors, foreign_key_objects = validate_lookup(json_data, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)
    try:
        values = build_billing_address_values(json_data, foreign_key_objects, user, mystore)
        billing_address = update_billing_address_utils(values)
    except Exception as e:
        return JsonResponse({'success': False, 'error':str(e) }, status=500)
    
    return JsonResponse({'success': True, 'message': 'Billing Address Update Successfully'}, status=201)

@csrf_exempt
def delete_store_billing_address(request):

    if request.method != 'DELETE':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    # Parse the JSON request body
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    # get the store name from the origin 
    mystore = validate_mystore(request)
    if mystore == None:
        return JsonResponse({'success': False, 'error': "E-commerse store not found"}, status=400)

    user = get_store_user_from_token(request,mystore)
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    required_fields = [
    "id",
    ]

    validation_errors, foreign_key_objects = validate_lookup(json_data, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)
    try:
        values = build_billing_address_values(json_data, foreign_key_objects, user, mystore)
        billing_address = StoreBillingAddress.objects.get(id=values['id'], user = values['user'], store = values['store'])
        billing_address.delete()
    except Exception as e:
        return JsonResponse({'success': False, 'error':str(e) }, status=500)
    
    return JsonResponse({'success': True, 'message': 'Billing Address Delete Successfully'}, status=201)










