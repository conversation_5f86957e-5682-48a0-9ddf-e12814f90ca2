[{"model": "api.country", "pk": 1, "fields": {"name": "Palestine", "code": "PS", "mobile_intro": "+970|+972", "country_flag": "🇵🇸", "currency": "ILS", "mobile_placeholder": "05xxxxxxxx", "mobile_number_length": 10}}, {"model": "api.country", "pk": 2, "fields": {"name": "Jordan", "code": "JO", "mobile_intro": "+962", "country_flag": "🇯🇴", "currency": "JOD", "mobile_placeholder": "07xxxxxxxx", "mobile_number_length": 10}}, {"model": "api.country", "pk": 3, "fields": {"name": "United Arab Emirates", "code": "UAE", "mobile_intro": "+971", "country_flag": "🇦🇪", "currency": "AED", "mobile_placeholder": "05xxxxxxxx", "mobile_number_length": 10}}, {"model": "api.country", "pk": 4, "fields": {"name": "Oman", "code": "OM", "mobile_intro": "+968", "country_flag": "🇴🇲", "currency": "OMR", "mobile_placeholder": "09xxxxxxx", "mobile_number_length": 9}}, {"model": "api.country", "pk": 5, "fields": {"name": "Kuwait", "code": "KW", "mobile_intro": "+965", "country_flag": "🇰🇼", "currency": "KWD", "mobile_placeholder": "0xxxxxxxx", "mobile_number_length": 9}}, {"model": "api.country", "pk": 6, "fields": {"name": "Saudi Arabia", "code": "KSA", "mobile_intro": "+966", "country_flag": "🇸🇦", "currency": "SAR", "mobile_placeholder": "05xxxxxxxx", "mobile_number_length": 10}}, {"model": "api.country", "pk": 7, "fields": {"name": "Iraq", "code": "IQ", "mobile_intro": "+964", "country_flag": "🇮🇶", "currency": "IQD", "mobile_placeholder": "07xxxxxxxxx", "mobile_number_length": 11}}, {"model": "api.country", "pk": 8, "fields": {"name": "Libya", "code": "LY", "mobile_intro": "+218", "country_flag": "🇱🇾", "currency": "LYD", "mobile_placeholder": "09xxxxxxxx", "mobile_number_length": 10}}]