import requests
import os

FRONT_BASEURL = os.getenv('FRONT_BASEURL')

def send_discord_otp(message_text):
    webhook_url = 'https://discord.com/api/webhooks/1389510698777706496/-Or1AzNj6-mnecFayIm7vMI1pmTLnUYIXALwAxamqztdtUxDGjVv0N8CE1QV63rjEt7r'
    data = {
        "content": message_text,
        "username": "DjangoBot"
    }

    response = requests.post(webhook_url, json=data)
    if response.status_code != 204:
        print(f"Failed to send message to discrod: {response.status_code}, {response.text}")
