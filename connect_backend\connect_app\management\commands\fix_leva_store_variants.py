from django.core.management.base import BaseCommand
from api.warehouse.warehouse_model import WarehouseVariant
from api.products.product_model import OrderLine

class Command(BaseCommand):
    help = "Clears the Django cache"

    def add_arguments(self, parser):
        parser.add_argument(
            '--company_id',
            type=int,
            help='Company ID for which the operation should be performed',
        )

    def handle(self, *args, **kwargs):
        company_id = kwargs.get('company_id')
        
        if not company_id:
            self.stdout.write(self.style.ERROR('Error: --company_id is required'))
            return

        for variant in WarehouseVariant.objects.filter(variant__product__company__company_id=company_id):
            variant.save()
            self.stdout.write(f"Variant {variant.variant.id} saved successfully.")

        for order_line in OrderLine.objects.filter(order__company__company_id=company_id, order__status__code='with_delivery_company'):
            order_line.physical_deducted = order_line.quantity
            order_line.virtual_deducted = order_line.quantity
            order_line.save()

        self.stdout.write(self.style.SUCCESS(f"Operation completed for company_id {company_id}."))
