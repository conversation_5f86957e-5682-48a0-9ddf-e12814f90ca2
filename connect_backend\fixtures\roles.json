[{"model": "auth.group", "pk": 1, "fields": {"name": "Super Manager"}}, {"model": "api.role", "pk": 1, "fields": {"name": "Super Manager", "role_id": 1, "group": 1, "code": "super_manager"}}, {"model": "auth.group", "pk": 2, "fields": {"name": "Manager"}}, {"model": "api.role", "pk": 2, "fields": {"name": "Manager", "role_id": 2, "group": 2, "code": "manager"}}, {"model": "auth.group", "pk": 3, "fields": {"name": "Accountant"}}, {"model": "api.role", "pk": 3, "fields": {"name": "Accountant", "role_id": 3, "group": 3, "code": "accountant"}}, {"model": "auth.group", "pk": 4, "fields": {"name": "Call center"}}, {"model": "api.role", "pk": 4, "fields": {"name": "Call center", "role_id": 4, "group": 4, "code": "call_center"}}, {"model": "auth.group", "pk": 5, "fields": {"name": "Driver"}}, {"model": "api.role", "pk": 5, "fields": {"name": "Driver", "role_id": 5, "group": 5, "code": "driver"}}, {"model": "auth.group", "pk": 6, "fields": {"name": "Sales Manager"}}, {"model": "api.role", "pk": 6, "fields": {"name": "Sales Manager", "role_id": 6, "group": 6, "code": "sales_manager"}}, {"model": "auth.group", "pk": 7, "fields": {"name": "Sales"}}, {"model": "api.role", "pk": 7, "fields": {"name": "Sales", "role_id": 7, "group": 7, "code": "sales"}}]