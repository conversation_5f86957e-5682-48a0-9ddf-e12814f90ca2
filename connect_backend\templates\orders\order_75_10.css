@page {
    size: 100mm 75mm;
    margin: 0;
}

body,
html {
    font-size: 7.5pt;
}

.o-table .cell-truncate {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 9rem;
    width: 9rem;
}

.waybill-container {
    padding: 1rem;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.o-table {
    border-spacing: 0 0.2rem;
}

.o-table td {
    padding: .2rem 1rem;
    width: 33%;
}

.date-time {
    font-size: 1.13rem;
}

.card {
    border: solid 0.033rem;
    border-radius: .5rem;
    padding: .5rem;
    margin-top: .5rem;
}

.card-header {
    font-size: 1.25rem;
    font-weight: bold;
    border-bottom: .15rem solid;
    padding-bottom: .3rem;
    margin-bottom: .3rem;
}

.left-cards-container {
    display: inline-block;
    width: 43%;
    /* padding: .3rem .5rem .8rem 0rem; */
    padding: .3rem 0rem .8rem .5rem;
}

.right-cards-container {
    display: inline-block;
    width: 55%;
    padding: .3rem 0rem .8rem 0rem;
    vertical-align: top;
}

.notes-content {
    height: 2.2rem;
    margin-bottom: .6rem;
    overflow: hidden;
}

.products-container {
    height: 10rem;
    max-height: 10rem;
    position: relative;
    font-size: .9rem;
    overflow: hidden
}

.header-barcode-container {
    text-align: center;
    width: 19rem;
    height: 5.8rem;
}

.business-logo {
    max-height: 5rem;
    max-width: 7rem;
}

.text-small{
    font-size: 0.8rem;
}