from django.core.management.base import BaseCommand
from api.products.product_model import Product, ProductVariant, ProductsImage
from api.users.user_model import User, Company
from api.category.category_model import Category
from api.mystore.mystore_model import MyStore, BannerDetail
from api.orders.order_model import BranchPage
from django.conf import settings
import boto3
import os
import uuid

class Command(BaseCommand):
    help = 'Upload images to S3 and update the links in models'

    def style_message(self, message, color_code):
        """
        Returns a styled message using ANSI color codes.
        """
        return f"\033[{color_code}m{message}\033[0m"

    def upload_images_and_update_links(self, model, old_field, new_field):
        """
        Uploads images to S3 and updates the model's URL field.
        """
        s3_client = boto3.client(
            's3',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            region_name=settings.AWS_S3_REGION_NAME,
        )
        region = settings.AWS_S3_REGION_NAME
        bucket_name = settings.AWS_STORAGE_BUCKET_NAME
        uploaded_count = 0
        skipped_count = 0

        for obj in model.objects.filter(**{f"{old_field}__isnull": False, f"{new_field}__isnull": True}):
            # Check if the field has a file
            image_field = getattr(obj, old_field)
            if not image_field or not hasattr(image_field, 'path') or not os.path.exists(image_field.path):
                skipped_count += 1
                continue

            unique_file_name = f"{uuid.uuid4()}_{image_field.name}"
            s3_key = f'public-folder/{unique_file_name}'
            try:
                s3_client.upload_file(image_field.path, bucket_name, s3_key)
                s3_url = f'https://{bucket_name}.s3.{region}.amazonaws.com/{s3_key}'
                setattr(obj, new_field, s3_url)
                obj.save()

                self.stdout.write(
                    self.style_message(
                        f"Uploaded {image_field.name} to {s3_url}",
                        "32"  # Green
                    )
                )
                uploaded_count += 1
            except Exception as e:
                self.stdout.write(
                    self.style_message(
                        f"Failed to generate presigned URL for {image_field.name}: {e}",
                        "31"  # Red
                    )
                )
                skipped_count += 1

        return uploaded_count, skipped_count
    
    def handle(self, *args, **kwargs):
        model_fields = [
            (ProductVariant, 'variant_image', 'variant_image_url'),
            (ProductsImage, 'image', 'product_image_url'),
            (MyStore, 'mystore_image', 'mystore_image_url'),
            (BannerDetail, 'banner_image', 'banner_image_url'),
            (BranchPage, 'branch_page_image', 'branch_page_image_url'),
            (User, 'user_image', 'user_image_url'),
            (Company, 'company_image', 'company_image_url'),
            (Category, 'category_image', 'category_image_url'),
        ]
        total_uploaded = 0
        total_skipped = 0

        for model, old_field, new_field in model_fields:
            self.stdout.write(self.style_message(f"\nProcessing {model.__name__}...", "34"))  # Blue
            uploaded, skipped = self.upload_images_and_update_links(model, old_field, new_field)
            total_uploaded += uploaded
            total_skipped += skipped

            self.stdout.write(self.style_message(f"Summary for {model.__name__}:", "36"))  # Cyan
            self.stdout.write(self.style_message(f"  Uploaded: {uploaded}", "32"))  # Green
            self.stdout.write(self.style_message(f"  Skipped: {skipped}", "33"))  # Yellow

        self.stdout.write(self.style_message("\nOverall Summary:", "35"))  # Magenta
        self.stdout.write(self.style_message(f"  Total Uploaded: {total_uploaded}", "32"))  # Green
        self.stdout.write(self.style_message(f"  Total Skipped: {total_skipped}", "33"))  # Yellow
