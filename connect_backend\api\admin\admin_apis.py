from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.db import transaction
from ..billing.billing_utils import *
from ..auth.auth_model import *
from ..products.product_model import *
from ..lookups.lookup_utils import *
from .admin_model import *
from ..permissions import *
from rest_framework.decorators import *
from ..auth.auth_utils import *
from rest_framework.permissions import AllowAny
from .admin_utils import *
from ..notification.notification_utils import *
from ..error.error_model import ConnectError
from ..collection.collection_utils import *
from django.core.exceptions import FieldDoesNotExist, ObjectDoesNotExist
from ..dashboard.dashboard_model import *
from ..error.error_model import *
from ..reseller.models import *
from django.contrib.auth.models import Permission
from django.utils.translation import gettext as _
from django.utils import translation
import boto3
import uuid
from .admin_tickets.admin_ticket_model import ReleaseDocument

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def activate_account(request):
    json_body = parse_request_body(request)
    if not json_body:
        raise get_error_response('GENERAL_001', {})
    
    mobile_number = json_body.get('mobile_number')

    try:
        user = User.objects.get(mobile_number=mobile_number)
    except User.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'User', 'field':'mobile_number', 'value': mobile_number})

    user.is_active = True
    user.waiting_confirmation = False
    user.is_deleted = False
    user.updated_by = request.user.username
    user.save()

    user.company.free_trial_end_date = timezone.now() + datetime.timedelta(days=3)
    user.company.save()

    company_plan = CompanyPlan.objects.create(company=user.company, package=None)
    subscription = Subscription.objects.create(start_date=timezone.now(), end_date=timezone.now() + datetime.timedelta(days=3), order_limit=500, active=True, recurring_interval='month', company=user.company, company_plan=company_plan)
    subscription.activate_freemium()
    subscription.save()
    send_activate_message(user)
    try:
        reseller_company = ResellerCompany.objects.get(company=user.company)
        reseller_company.status = _('Activated')
        reseller_company.status_code = 'activated'
        reseller_company.save()
    except ResellerCompany.DoesNotExist:
        pass

    return JsonResponse({'success': True,'message': f'User with mobile number {mobile_number} has been activated'}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def delete_user(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    mobile_number = json_data.get('mobile_number')

    otp_codes = OtpCodes.objects.filter(mobile_number=mobile_number).delete()
    OtpToken.objects.filter(mobile_number=mobile_number).delete()
    try:
        user = User.objects.get(mobile_number=mobile_number)
    except User.DoesNotExist:
        if otp_codes:
            return JsonResponse({'success': True,'message': 'otp codes and tokens deleted'}, status = 200)
        return JsonResponse({'success': False ,'message': f'No user found with mobile number {mobile_number}'}, status = 404)
    
    with transaction.atomic():
        company = user.company
        user.user.delete()
        user.delete()
        company.delete()

    return JsonResponse({'success': True,'message': 'User deleted successfully'}, status=200)

@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([NoAuthentication])
def admin_token(request):
    json_body = parse_request_body(request)
    if not json_body:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    email = json_body.get('email')
    password = json_body.get('password')

    if email is None or password is None:
        return JsonResponse({'success': False, 'error': 'Please provide both email and password'}, status=400)

    try:
        user = authenticate(username=email, password=password)
        if not user:
            return JsonResponse({'success': False, 'error': 'Invalid email or password'}, status=400)
        if not user.is_superuser:
            return JsonResponse({'success': False, 'error': 'Invalid email or password'}, status=400)
        token = generate_JWT(user)
        return JsonResponse({'success': True, 'token': token}, status=200)
    except ValueError as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([NoAuthentication])
def admin_extend_token(request):
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Token '):
        return JsonResponse({'success': False, 'error': 'Missing or invalid token'}, status=401)
    
    user = request.user
    if user is None:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)

    token = generate_JWT(user)

    return JsonResponse({'token': token})

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_waiting_users(request):

    try: 
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        response_data = get_records('User',json_data,None,company_filter=Q((Q(is_active=False) & Q(waiting_confirmation=True) & Q(company__free_trial_end_date__isnull=True)) | Q(is_deleted=True)) & Q(user__isnull=False),context={'use_follow_up_serializer': True})
        
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def create_package_in_stripe(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid data format'}, status=400)
    
    user = request.user
    
    if Package.objects.filter(name=json_data['name']).exists():
        return JsonResponse({'success': False, 'error': 'Package with the same name already exists'}, status=400)
    
    values = json_data
    values['created_by'] = values['updated_by'] = user.username
    
    package = create_package(values)
    if not package:
        return JsonResponse({'success': False, 'error': 'Failed to create package'}, status=500)

    result = create_stripe_product_and_prices(package)
    if result['success']:
        return JsonResponse({'success': True, 'stripe_product_id': result['stripe_product_id']}, status=201)
    else:
        return JsonResponse({'success': False, 'error': result['error']}, status=400)

@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_package_in_stripe(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid data format'}, status=400)
    
    package_id = json_data.get('id')
    if not package_id:
        return JsonResponse({'success': False, 'error': 'Package ID is required'}, status=400)

    try:
        package = Package.objects.get(id=package_id)
    except Package.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Package not found'}, status=404)

    user = request.user

    values = json_data
    values['updated_by'] = user.username
    package = update_package_util(values)
    
    if package:
        try:
            if package.name == 'Free Package':
                return JsonResponse({'success': True, 'message': 'Updated package successfully'}, status=200)
            product = update_stripe_product_and_prices(package)
            return JsonResponse({'success': True, 'stripe_product_id': product['stripe_product_id']}, status=200)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)
    else:
        return JsonResponse({'success': False, 'error': 'Failed to update package'}, status=500)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_packages(request):
    packages = Package.objects.all()
    serializer = PackageSerializer(packages, many=True)
    response_data = {
        'success': True,
        'packages': serializer.data
    }
    return JsonResponse(response_data, status=200)
    
@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
@authentication_classes([NoAuthentication])
def send_unsynced_orders(request):
    json_body = parse_request_body(request)
    
    if not json_body:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    if 'params' in json_body and json_body['params']:
        if 'values' in json_body['params']['values']:
            values = json_body['params']['values']
            for key, value in values.items():
                try:
                    company = Company.objects.get(company_id=value['company'])
                except Company.DoesNotExist:
                    return JsonResponse({'success':False, 'result': {'code': 400, 'error': f'Company with id {value["company"]} not found'}}, status=404)
                try:
                    order = Order.objects.get(company=company, order_sequence=value['order_sequence'])
                except Order.DoesNotExist:
                    return JsonResponse({'success':False, 'result': {'code': 400, 'error': f'Order with id {value["order_sequence"]} not found'}}, status=404)
                
                try:
                    failed_order = FailedOrder.objects.create(company=company, order_sequence=value['order_sequence'],order_id=order.id, job_id = key, job_status = value['state'], error_message=value['connect_message'])
                except Exception as e:
                    return JsonResponse({'success':False, 'result': {'code': 400, 'error': f'Error creating record for admin in Connect'}}, status=400)
                
    return JsonResponse({'success':True, 'result': {'code': 200, 'message': 'orders successfully sent'}}, status_code=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def add_area(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid data format'}, status=400)
    
    values = json_data
    values['created_by'] = values['updated_by'] = request.user.username

    required_fields = ['country', 'name']
    area_name_english = values.pop('area_name_english', '')
    area_name_hebrew = values.pop('area_name_hebrew', '')

    validation_errors, foreign_key_objects = validate_lookup(json_data, required_fields)

    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)
    
    values = build_lookup_values(values, foreign_key_objects)
    area = create_area(values)
    if not area:
        return JsonResponse({'success': False, 'error': 'Failed to create area'}, status=500)
    
    try:
        if not FuzzyArea.objects.filter(area_name_arabic=area.name).exists():
            fuzzy_area = FuzzyArea.objects.create(area_name_arabic=area.name, area_name_english=area_name_english, area_name_hebrew=area_name_hebrew)
            fuzzy_sub_area = FuzzySubArea.objects.create(sub_area_name_arabic=area.name, sub_area_name_english=area_name_english, sub_area_name_hebrew=area_name_hebrew)
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'Error creating record for FuzzyArea: {str(e)}'}, status=400)
    
    serializer = AreaSerializer(area)
    if area:
        return JsonResponse({'success': True, 'message': 'Area Added Successfully', 'area': serializer.data}, status=201)
    else:
        return JsonResponse({'error': 'Failed to create area'}, status=500)
    
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_area(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001')
        area_id = json_data.get('id')
        if not area_id:
            raise get_error_response('GENERAL_003', {'fields': 'id'})
        try:
            area = Area.objects.get(id=area_id)
        except Area.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'Area', 'field': 'id', 'value': area_id})
        values = json_data
        required_fields = ['country', 'name', 'code']
        area_name_english = values.pop('area_name_english', '')
        area_name_hebrew = values.pop('area_name_hebrew', '')
        default_subarea = values.pop('default_subarea', '')
        validation_errors, foreign_key_objects = validate_lookup(values, required_fields)
        if validation_errors:
            raise get_error_response('GENERAL_006', {'errors': ', '.join(validation_errors)})
        values = build_lookup_values(values, foreign_key_objects)
        values['updated_by'] = request.user.username
        with transaction.atomic():
            area = update_lookup_util(values, area)
            try:
                sub_areas = SubArea.objects.filter(area=area, is_default_for_area=True)
                for sub_area in sub_areas:
                    sub_area.is_default_for_area = False
                    sub_area.save()
                sub_area = SubArea.objects.get(id=default_subarea)
                sub_area.is_default_for_area = True
                sub_area.save()
            except SubArea.DoesNotExist:
                raise get_error_response('GENERAL_002', {'model': 'SubArea', 'field': 'id', 'value': default_subarea})
            try:
                FuzzyArea.objects.update_or_create(
                    area_name_arabic=area.name,
                    defaults={'area_name_english': area_name_english, 'area_name_hebrew': area_name_hebrew}
                )
            except Exception as e:
                return JsonResponse({'success': False, 'error': f'Error creating record for FuzzyArea: {str(e)}'}, status=400)
        serializer = AreaSerializer(area)
        return JsonResponse({'success': True, 'message': 'Area Updated Successfully', 'area': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['DELETE'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def delete_area(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid data format'}, status=400)
    
    area_id = json_data.get('id')
    if not area_id:
        return JsonResponse({'success': False, 'error': 'Area ID is required'}, status=400)

    try:
        area = Area.objects.get(id=area_id)
    except Area.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Area not found'}, status=404)
    
    area.delete()
    return JsonResponse({'success': True, 'message': 'Area deleted successfully'}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def add_sub_area(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'error': 'Invalid data format'}, status=400)
    
    values = json_data
    values['created_by'] = values['updated_by'] = request.user.username

    required_fields = ['area', 'name']
    sub_area_name_english = values.pop('sub_area_name_english', '')
    sub_area_name_hebrew = values.pop('sub_area_name_hebrew', '')

    validation_errors, foreign_key_objects = validate_lookup(json_data, required_fields)

    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)
    
    values = build_lookup_values(values, foreign_key_objects)
    
    existing_subarea = SubArea.objects.filter(name=values['name'],area=values['area']).exists()
    if existing_subarea:
        return JsonResponse({'success': False, 'error': _('Duplicate subarea: a subarea with the same name and area already exists.')}, status=400)

    sub_area = create_sub_area(values)
    if not sub_area:
        return JsonResponse({'success': False, 'error': 'Failed to create sub area'}, status=500)
    
    try:
        if not FuzzySubArea.objects.filter(sub_area_name_arabic=sub_area.name).exists():
            fuzzy_sub_area = FuzzySubArea.objects.update_or_create(sub_area_name_arabic=sub_area.name,defaults={'sub_area_name_english': sub_area_name_english,'sub_area_name_hebrew': sub_area_name_hebrew})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'Error creating record for FuzzyArea: {str(e)}'}, status=400)
    
    serializer = SubAreaSerializer(sub_area)
    if sub_area:
        return JsonResponse({'success': True, 'message': 'Sub Area Added Successfully', 'sub_area': serializer.data}, status=201)
    else:
        return JsonResponse({'success': False, 'error': 'Failed to create sub area'}, status=500)
    
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_sub_area(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid data format'}, status=400)
    
    sub_area_id = json_data.get('id')
    if not sub_area_id:
        return JsonResponse({'success': False, 'error': 'SubArea ID is required'}, status=400)

    try:
        sub_area = SubArea.objects.get(id=sub_area_id)
    except SubArea.DoesNotExist:
        return JsonResponse({'error': 'Sub Area not found'}, status=404)
    
    values = json_data
    required_fields = ['area', 'name', 'code']
    sub_area_name_english = values.pop('sub_area_name_english', '')
    sub_area_name_hebrew = values.pop('sub_area_name_hebrew', '')
    
    validation_errors, foreign_key_objects = validate_lookup(values, required_fields)
    if validation_errors:
        return JsonResponse({'success': False, 'error': validation_errors}, status=400)
    
    values = build_lookup_values(values, foreign_key_objects)
    values['updated_by'] = request.user.username
    sub_area = update_lookup_util(values, sub_area)
    if not sub_area:
        return JsonResponse({'success': False,'error': 'Failed to update area'}, status=400)

    try:
        fuzzy_sub_area = FuzzySubArea.objects.update_or_create(sub_area_name_arabic=sub_area.name,defaults={'sub_area_name_english': sub_area_name_english,'sub_area_name_hebrew': sub_area_name_hebrew})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'Error creating record for FuzzyArea: {str(e)}'}, status=400)

    serializer = SubAreaSerializer(sub_area)
    return JsonResponse({'success': True, 'message': 'Sub Area Updated Successfully', 'sub_area': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['DELETE'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def delete_sub_area(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid data format'}, status=400)
    
    sub_area_id = json_data.get('id')
    if not sub_area_id:
        return JsonResponse({'success': False, 'error': 'Sub Area ID is required'}, status=400)

    try:
        sub_area = SubArea.objects.get(id=sub_area_id)
    except SubArea.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Sub Area not found'}, status=404)
    
    sub_area.delete()
    return JsonResponse({'success': True, 'message': 'Sub Area deleted successfully'}, status=200)

@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_status_map(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'error': 'Invalid data format'}, status=400)
    updated_by = request.user.username
    status_maps = json_data.get('status_maps', [])
    for status_map in status_maps:
        if 'id' in status_map and status_map['id']:
            try:
                update_status_map_util(status_map, updated_by)
            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)}, status=500)
        else:
            try:
                create_status_map(status_map, updated_by)
            except Exception as e:
                return JsonResponse({'success': False, 'error': str(e)}, status=500)
    return JsonResponse({'success': True, 'message': 'Status Map Updated Successfully'}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def sync_delivery_companies(request):
    url = 'https://master.olivery.app/master/companies/get_companies/delivery'
    json_body = {
        "jsonrpc": "2.0"
    }
    response = requests.get(url, json=json_body)
    data = response.json()

    if data.get('result', {}).get('status') == 'success':
        companies = data['result']['data']
        try:
            with transaction.atomic():
                for company in companies:
                    image_url = ''
                    required_fields = ['name', 'website_db', 'website_url', 'logo', 'country']
                    missing_fields = [field for field in required_fields if field not in company or company.get(field) is None]
                    if missing_fields:
                        raise ValidationError(f"Company {company.get('name', 'Unknown')} is missing required fields: {', '.join(missing_fields)}")
                    img_data = None

                    if 'country' in company:
                        try:
                            if 'Palestine' in company['country']:
                                company['country'] = 'Palestine'
                            country = Country.objects.get(name=company['country'])
                        except Country.DoesNotExist:
                            raise ValidationError(f"Country '{company['country']}' not found")
                    else:
                        raise ValidationError(f"Country not found for company {company['name']}")
                    
                    delivery_company, _ = DeliveryCompany.objects.update_or_create(
                        name=company['name'],
                        defaults={
                            'company_image': img_data if img_data else None,
                            'delivery_company_url': company['website_url'],
                            'delivery_company_db': company['website_db'],
                            'country': country
                        }
                    )
                    if 'logo' in company and not delivery_company.image_url:
                        try:
                            img_data = ContentFile(base64.b64decode(company['logo']), name=f'{uuid.uuid4()}.png')
                        except Exception as e:
                            raise ValidationError(f"Failed to decode logo for company {company['name']}: {e}, Please check that the company has a valid logo")

                        s3_client = boto3.client(
                            's3',
                            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
                            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
                            region_name=settings.AWS_S3_REGION_NAME,
                        )
                        region = settings.AWS_S3_REGION_NAME
                        bucket_name = settings.AWS_STORAGE_BUCKET_NAME
                        existing_image_url = getattr(delivery_company, 'image_url', None)
                        s3_key = None

                        if existing_image_url:
                            parsed_url = urlparse(existing_image_url)
                            s3_key = parsed_url.path.lstrip('/')
                        else:
                            unique_file_name = f"{uuid.uuid4()}.png"
                            s3_key = f'public-folder/{unique_file_name}'
                        img_data.seek(0)
                        s3_client.upload_fileobj(img_data, bucket_name, s3_key)
                        s3_url = f'https://{bucket_name}.s3.{region}.amazonaws.com/{s3_key}'
                        delivery_company.image_url = s3_url
                        delivery_company.save()

        except ValidationError as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)
        return JsonResponse({'success': True,'message': 'Delivery Companies Synced Successfully'}, status=200)
    else:
        return JsonResponse({'success': False, 'error': 'Failed to fetch delivery companies'}, status=500)
        
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_delivery_companies(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'error': 'Invalid data format'}, status=400)
    
    fields_to_include = [
        'id',
        'name',
        'company_image',
        'delivery_company_url',
        'country',
        'is_subarea_required',
        'is_active',
        'fully_synced_areas',
        'fully_synced_subareas',
        'fully_synced_statuses',
        'is_olivery_company'
    ]

    objects_to_handle = ['country']

    model_map = {
        'country': Country
    }

    related_fields = {}

    delivery_companies = DeliveryCompany.objects.all()
    filtered_delivery_companies = search_filter_group(delivery_companies, json_data, fields_to_include, related_fields)
    delivery_companies = object_to_json(filtered_delivery_companies, objects_to_handle, model_map)

    for delivery_company in delivery_companies[0].get('data',[]):
        id = delivery_company.get('id', None)
        if Reseller.objects.filter(delivery_company_id=id).exists():
            delivery_company['has_reseller'] = True
        else:
            delivery_company['has_reseller'] = False

    return JsonResponse({'success': True, 'delivery_companies': delivery_companies}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def impersonate_user(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success':False, 'error': 'Invalid data format'}, status=400)
    
    user_id = json_data.get('user_id')

    if not user_id:
        return JsonResponse({'success':False, 'error': 'User ID is required'}, status=400)
    
    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        return JsonResponse({'success':False, 'error': 'User not found'}, status=404)
    
    if not user.user:
        return JsonResponse({'success':False, 'error': 'User is not activated user and should be sent the invitation link to set password'}, status=400)
    
    try:
        token = generate_JWT(user.user,connect_user_id=user_id, impersonated_by=request.user.username)
        return JsonResponse({'success':True, 'token': token}, status=200)
    except Exception as e:
        return JsonResponse({'success':False, 'error': f'Failed to generate JWT: {str(e)}'}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_users(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('User', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def test_send_notification(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid data format'}, status=400)
    
    player_ids = json_data.get('player_ids', [])
    lang = json_data.get('lang', 'ar')
    translation.activate(lang) 

    values = {
        'title_en': 'TEST',
        'title_ar': _('New Order'),
        'message_en': 'status of order ' + 'PLACEHOLDER HERE' + ' has been changed to ' + 'PLACEHOLDER HERE',
        'message_ar': 'حالة الطلبية ' + _('New Order') + ' تم تغييرها الى ' + 'PLACEHOLDER HERE',
        'data': {"filters": [{"operator": "and", "filters": [{"field": "order_sequence", "operator": "exact", "value": 'TESTING SEQUENCE'}]}], 'title': 'TESTING SEQUENCE'}
    }
    sound = NotificationSound.objects.get(name=json_data['name'])

    response = send_notification(user_ids=player_ids, values=values, url=None, sound=sound, lang=lang)
    return JsonResponse({'success': True, 'message': 'Notification sent successfully'}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def set_notification_sounds(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid data format'}, status=400)
    
    notifications = json_data.get('notification', [])

    for notification in notifications:
        sound_name = notification.get('sound')
        status = notification.get('delivery_status')
        try:
            sound = NotificationSound.objects.get(name=sound_name)
        except NotificationSound.DoesNotExist:
            sound = None
        try:
            notifcation = Notification.objects.get_or_create(status=status)
            notifcation.sound = sound
            notification.save()
        except Exception as e:
            return JsonResponse({'success': False, 'error': f'Failed to set {sound_name} as default: {str(e)}'}, status=500)
    
    return JsonResponse({'success': True, 'message': 'Notification Sound set as default successfully'}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_sounds(request):
    sounds = NotificationSound.objects.all()
    serializer = NotificationSoundSerializer(sounds, many=True)
    return JsonResponse({'success': True, 'sounds': serializer.data}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_notifications(request):
    notifications = Notification.objects.all()
    serializer = NotificationSerializer(notifications, many=True)
    return JsonResponse({'success': True, 'notifications': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_collections(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    fields_to_include = [
        'id',
        'company',
        'amount',
        'collection_status',
        'collection_type',
        'currency',
        'created_at',
        'collection_bank',
        'iban'
    ]

    model_map = {
        'collection_status': CollectionStatus,
        'collection_type': CollectionType,
        'collection_bank': CollectionBank,
        'company': Company
    }

    objects_to_handle = ['collection_status', 'collection_type', 'collection_bank', 'company']

    related_field = {}

    if 'is_archive' in json_data and json_data['is_archive']:
        query = Collection.objects.filter(is_archive=True)
    else:
        query = Collection.objects.filter(is_archive=False)
        
    collections = search_filter_group(query, json_data, fields_to_include, related_field)
    collections_dict = object_to_json(collections, objects_to_handle, model_map)
    response_data = {
        'success': True,
        'collections': collections_dict
    }

    return JsonResponse(response_data, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_collection_status(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    collection_id = json_data.get('id')
    status = json_data.get('status')

    try:
        new_status = CollectionStatus.objects.get(id=status)
    except CollectionStatus.DoesNotExist:
        try:
            new_status = CollectionStatus.objects.get(code=status)
        except CollectionStatus.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Invalid status code or id'}, status=400)

    try:
        collection = Collection.objects.get(id=collection_id)
        collection.collection_status = new_status
        collection.save()
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'Failed to update collection status: {str(e)}'}, status=500)
    
    if status == 'confirmed':
        orders = Order.objects.filter(collection=collection)
        for order in orders:
            order.status = Status.objects.get(code='money_collected')
            order.save()

    if status == 'completed':
        collection.is_archive = True
        collection.save()

    return JsonResponse({'success': True, 'message': f'Collection status updated successfully'}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_delivery_company_areas(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    delivery_company = json_data.get('delivery_company')

    try:
        delivery_company = DeliveryCompany.objects.get(id=delivery_company)
    except DeliveryCompany.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Invalid delivery company id'}, status=400)
    
    params = {
        "jsonrpc": "2.0",
        "params": {

        }
    }
    try:
        if delivery_company.is_olivery_company:
            response = send_api_to_delivery_company(delivery_company, '/connection/get_areas', params)
            if response:
                code = response['code']
                if code == 200:
                    result = response['result']
                    areas = result.get('areas', [])
                    return JsonResponse({'success': True, 'areas': areas}, status=200)
                else:
                    return JsonResponse({'success': False, 'error': result['message']}, status=400)
        else:
            request_template = RequestTemplate.objects.get(delivery_company=delivery_company, name='get_areas')
            params = build_request_params(request_template, None)
            response = send_dynamic_api_request(request_template, params)
            return JsonResponse({'success': True, 'areas': response.get('data')}, status=200)
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'Failed to fetch delivery company areas: {str(e)}'}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_delivery_company_sub_areas(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    delivery_company = json_data.get('delivery_company')

    try:
        delivery_company = DeliveryCompany.objects.get(id=delivery_company)
    except DeliveryCompany.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Invalid delivery company id'}, status=400)
    
    params = {
        "jsonrpc": "2.0",
        "params": {

        }
    }
    try:
        if delivery_company.is_olivery_company:
            response = send_api_to_delivery_company(delivery_company, '/connection/get_subareas', params)
            if response:
                code = response['code']
                if code == 200:
                    result = response['result']
                    sub_areas = result.get('sub_areas', [])
                    return JsonResponse({'success': True, 'sub_areas': sub_areas}, status=200)
                else:
                    return JsonResponse({'success': False, 'error': result['message']}, status=400)
        else:
            request_template = RequestTemplate.objects.get(delivery_company=delivery_company, name='get_sub_areas')
            params = build_request_params(request_template, None)
            response = send_dynamic_api_request(request_template, params)
            return JsonResponse({'success': True, 'sub_areas': response.get('data')}, status=200)
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'Failed to fetch delivery company sub_areas: {str(e)}'}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def sync_delivery_areas(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    delivery_company = get_delivery_company(json_data.get('delivery_company'))
    if not delivery_company:
        return JsonResponse({'success': False, 'error': 'Invalid delivery company id'}, status=400)

    area_map = json_data.get('area_map', [])
    
    response_data = update_area_maps(area_map, delivery_company)

    area_count = Area.objects.filter(country=delivery_company.country).count()
    area_map_count = AreaMap.objects.filter(delivery_company=delivery_company, imported_area__isnull=False).count()
    if area_count == area_map_count:
        delivery_company.fully_synced_areas = True
        delivery_company.save()

    if response_data['success']:
        return JsonResponse({'success': True,'message': 'Delivery company areas synced successfully'}, status=200)
    return JsonResponse({'success': True, 'error': response_data['error']}, status=400)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def sync_delivery_sub_areas(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    delivery_company = get_delivery_company(json_data.get('delivery_company'))
    if not delivery_company:
        return JsonResponse({'success': False, 'error': 'Invalid delivery company id'}, status=400)

    area_map = json_data.get('sub_area_map', [])
    
    response_data = update_subarea_maps(area_map, delivery_company)

    sub_area_count = SubArea.objects.filter(area__country__code=delivery_company.country).count()
    sub_area_map_count = SubAreaMap.objects.filter(delivery_company=delivery_company, imported_sub_area__isnull=False).count()
    if sub_area_count == sub_area_map_count:
        delivery_company.fully_synced_subareas = True
        delivery_company.save()
    if response_data['success']:
        return JsonResponse({'success': True,'message': 'Delivery company sub areas synced successfully'}, status=200)
    return JsonResponse({'success': True, 'error': response_data['error']}, status=400)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_area_map(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    delivery_company = get_delivery_company(json_data.get('delivery_company'))
    try:
        area_maps = AreaMap.objects.filter(delivery_company=delivery_company)
    except AreaMap.DoesNotExist:
        return JsonResponse({'success': True, 'area_map': []}, status=200)
    serializer = AreaMapSerializer(area_maps, many=True)
    return JsonResponse({'success': True, 'area_map': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_sub_area_map(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    delivery_company = get_delivery_company(json_data.get('delivery_company'))
    try:
        sub_area_maps = SubAreaMap.objects.filter(delivery_company=delivery_company)
    except SubAreaMap.DoesNotExist:
        return JsonResponse({'success': True, 'sub_area_map': []}, status=200)
    serializer = SubAreaMapSerializer(sub_area_maps, many=True)
    return JsonResponse({'success': True, 'sub_area_map': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def auto_map_areas(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
        
        delivery_company = get_delivery_company(json_data.get('delivery_company'))
        if not delivery_company:
            return JsonResponse({'success': False, 'error': 'Invalid delivery company id'}, status=400)


        def fetch_and_update_areas(endpoint, key, update_func, params):
            try:
                response = send_api_to_delivery_company(delivery_company, endpoint, params)
                if response and response.get('code') == 200:
                    result = response.get('result', {})
                    area_list = result.get(key, [])
                    update_func(area_list, delivery_company)
            except Exception as e:
                return JsonResponse({'success': False, 'error': f'Failed to fetch delivery company areas from {endpoint}: {str(e)}'}, status=500)

        if delivery_company.is_olivery_company:
            params = {"jsonrpc": "2.0", "params": {}}
            fetch_and_update_areas('/connection/get_areas', 'areas', update_or_create_area_map, params)
            fetch_and_update_areas('/connection/get_subareas', 'sub_areas', update_or_create_sub_area_map, params)
        else:
            request_template = RequestTemplate.objects.get(delivery_company=delivery_company, name='get_areas')
            params = build_request_params(request_template, delivery_company.country)
            response = send_dynamic_api_request(request_template, params)
            if response.get('success'):
                areas = response.get('data')
                update_or_create_area_map(areas, delivery_company)
            if delivery_company.is_subarea_required:
                request_template = RequestTemplate.objects.get(delivery_company=delivery_company, name='get_sub_areas')
                params = build_request_params(request_template, delivery_company.country)
                response = send_dynamic_api_request(request_template, params)
                if response.get('success'):
                    sub_areas = response.get('data')
                    update_or_create_sub_area_map(sub_areas, delivery_company)

        return JsonResponse({'success': True, 'message': 'Auto-mapped delivery company areas successfully'}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if e['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_failed_orders(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    fields_to_include = ['company', 'order', 'order_sequence', 'error_message', 'job_status', 'job_id']

    related_fields = {}

    objects_to_handle = ['company', 'order']
    model_map = {
        'company': Company,
        'order': Order
    }

    queryset = FailedOrder.objects.all()
    failed_orders = search_filter_group(queryset, json_data, fields_to_include, related_fields)
    failed_orders = object_to_json(failed_orders, objects_to_handle, model_map)
    return JsonResponse({'success': True, 'failed_orders': failed_orders}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_sub_areas(request):
    sub_areas = SubArea.objects.all()
    serializer = SubAreaSerializer(sub_areas, many=True)
    return JsonResponse({'success': True, 'sub_areas': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def set_system_configuration(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
        
        if(json_data.get("default_release_document", "")):
            release_doc = ReleaseDocument.objects.get(id=json_data["default_release_document"])
            json_data["default_release_document"] = release_doc
        system_configuration, created = SystemConfiguration.objects.update_or_create(id=1, defaults=json_data)
        serializer = SystemConfigurationSerializer(system_configuration)
        return JsonResponse({'success': True, 'system_configuration': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if e['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def test_fuzzy(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise ValidationError(get_error_response('GENERAL_001', {}))
        
        validation_errors = []
        json_data['area_mismatch'] = ''
        country = get_country(json_data, validation_errors)
        area = get_area(json_data, validation_errors, country)
        sub_area = get_sub_area(json_data, validation_errors, area)
        response_data = {
            'country': CountrySerializer(country).data if country else None,
            'area': AreaSerializer(area).data if area else None,
            'sub_area': SubAreaSerializer(sub_area).data if sub_area else None,
            'area_mismatch': json_data['area_mismatch']
        }
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def test_fuzzy_rate(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise ValidationError(get_error_response('GENERAL_001', {}))
        
        query = json_data.get('query', None)
        model = json_data.get('model', None)

        if model == 'area':
            area_names = list(Area.objects.all().values_list('name', flat=True))
            area_names.append('رام الله')
            area_names.append('البيرة')
            best_match, score = fuzzy_search(query, 'Area', filter=Q(area_name_arabic__in = area_names))
            if best_match:
                area = Area.objects.get(name=best_match)
                serializer = AreaSerializer(area)
                return JsonResponse({'success': True, 'area': serializer.data, 'score': score}, status=200)
            else:
                return JsonResponse({'success': True, 'area': None, 'score': 0}, status=200)
        
        if model == 'sub_area':
            sub_area_names = list(SubArea.objects.all().values_list('name', flat=True))
            best_match, score = fuzzy_search(query, 'SubArea', filter=Q(sub_area_name_arabic__in = sub_area_names))
            if best_match:
                sub_area = SubArea.objects.get(name=best_match)
                serializer = SubAreaSerializer(sub_area)
                return JsonResponse({'success': True, 'sub_area': serializer.data,'score': score}, status=200)
            else:
                return JsonResponse({'success': True, 'sub_area': None, 'score': 0}, status=200)
            
        if model == 'country':
            country_names = list(Country.objects.all().values_list('name', flat=True))
            best_match, score = fuzzy_search(query, 'Country', filter=Q(country_name_arabic__in = country_names))
            if best_match:
                country = Country.objects.get(name=best_match)
                serializer = CountrySerializer(country)
                return JsonResponse({'success': True, 'country': serializer.data,'score': score}, status=200)
            else:
                return JsonResponse({'success': True, 'country': None, 'score': 0}, status=200)
            
        return JsonResponse({'success': False, 'message': 'not a valid model'}, status=400)
    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_companies(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        on_boarding_companies = get_on_boarding_filters(json_data)
        if on_boarding_companies != None:
            custom_filter = Q(company_id__in=on_boarding_companies)
        else:
            custom_filter = Q()
        response_data = get_records('Company', json_data, False,context={'use_company_follow_up_serializer': True}, custom_filter=custom_filter)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_subscription(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        id = json_data.pop('id')
        try:
            Subscription.objects.update_or_create(id=id, defaults=json_data)
        except Exception as e:
            raise get_error_response('GENERAL_002', {'model': 'Subscription', 'field': 'id', 'value': id})

        return JsonResponse({'success': True, 'message': 'Subscription updated successfully'}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_subscription(request):
    try:
        company_id = request.GET.get('company_id')
        if not id:
            raise get_error_response('GENERAL_001', {})
        
        try:
            company = Company.objects.get(company_id=company_id)
        except Company.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'Company', 'field': 'company_id', 'value': company_id})
        
        subscription = Subscription.objects.filter(company=company).last()
        serializer = SubscriptionSerializer(subscription)
        return JsonResponse({'success': True, 'subscription': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_permissions(request):
    try:
        role = request.GET.get('role', None)
        if role:
            role = Role.objects.get(role_id=role)
            group = role.group
            permissions = group.permissions.filter(content_type__app_label='api').order_by('id')
        else:
            permissions = Permission.objects.filter(content_type__app_label='api').order_by('id')
        permissions_data = PermissionSerializer(permissions, many=True).data

        return JsonResponse({
            "success": True,
            "permissions": permissions_data
        }, status=200)

    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def set_role_permissions(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
        
        role = json_data.get('role')
        try:
            role = Role.objects.get(role_id=role)
            group = role.group
        except:
            raise get_error_response('GENERAL_002', {'model': 'Role', 'field': 'name', 'value': role})
        
        try:
            with transaction.atomic(): 
                group.permissions.clear()
                for permission_data in json_data.get('permissions', []):
                    permission = Permission.objects.get(id=permission_data)
                    group.permissions.add(permission)
        except Permission.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'Permission', 'field': 'codename', 'value': permission_data})
        except Exception as e:
            raise get_error_response('AUTH_401', {'role': group.name, 'error': str(e)})
        
        return JsonResponse({
            "success": True,
            "permissions": _('Permissions set successfully')
        }, status=200)

    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_roles(request):
    try:
        roles = Role.objects.all()
        serializer = RoleSerializer(roles, many=True)
        return JsonResponse({'success': True, 'roles': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def set_role(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
        
        role_id = json_data.get('id')
        if not role_id:
            role = Role.objects.create(name=json_data['name'], active=json_data['active'],code=json_data['code'])
            group = Group.objects.create(name=json_data['name'])
            role.group = group
            role.save()
        else:
            try:
                role = Role.objects.get(role_id=role_id)
                role.active = json_data['active']
                role.code = json_data['code']
                role.save()
            except ObjectDoesNotExist:
                return JsonResponse({'success': False, 'error': 'Role not found'}, status=404)

        serializer = RoleSerializer(role)
        return JsonResponse({'success': True, 'role': serializer.data}, status=200)

    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def add_attribute(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
        
        name = json_data.get('name')
        if not name:
            raise get_error_response('GENERAL_003', {'fields': 'name'})
        
        if Attribute.objects.filter(name=name).exists():
            raise get_error_response('GENERAL_008', {'field': 'name', 'value': name})
        
        attribute = Attribute.objects.create(name=name)
        serializer = AttributeSerializer(attribute)
        return JsonResponse({'success': True, 'attribute': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_attribute(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
        
        attribute = json_data.get('attribute')
        id = json_data.get('id')
        if not attribute or not id:
            raise get_error_response('GENERAL_003', {'fields': 'attribute, id'})
        
        if Attribute.objects.filter(name=attribute).exists():
            raise get_error_response('GENERAL_008', {'field': 'attribute', 'value': attribute})
        
        attribute = Attribute.objects.update(id=id,defaults={'name':attribute})
        serializer = AttributeSerializer(attribute)
        return JsonResponse({'success': True, 'attribute': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_integration_logs(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('IntegrationLogs', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_integration_logs(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('IntegrationLogs', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def add_notification_banner(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        notification_banner = create_notification_banner(json_data)
        serializer = NotificationBannerSerializer(notification_banner)
        return JsonResponse({'success': True, 'notification_banner': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_notification_banners(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('NotificationBanner', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_notification_banner(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        notification_banner = update_notification_banner_util(json_data)
        serializer = NotificationBannerSerializer(notification_banner)
        return JsonResponse({'success': True, 'notification_banner': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def send_notification_to_users(request):
    notification_data = {
        'sound': None,
        'title': '',
        'message': '',
        'data': {},
        'source':'admin',
        'users': []
    }
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        user_ids = json_data.get('user_ids', [])
        values = json_data.get('values', {})
        sound = json_data.get('sound', None)
        if sound:
            try:
                sound = NotificationSound.objects.get(name=sound)
                notification_data['sound'] = sound
            except NotificationSound.DoesNotExist:
                raise get_error_response('GENERAL_008', {'field':'sound', 'value': sound})
        users = []
        if user_ids and len(user_ids) > 0 and user_ids[0] == '*':
            users = User.objects.filter(player_id__isnull=False)
        else:
            users = User.objects.filter(id__in=user_ids)
        if users : 
            count = len(users)
            grouped_users = defaultdict(list)
            for user in users:
                grouped_users[user.lang].append(user)

            for lang, users in grouped_users.items():
                if lang == 'ar':
                    notification_data['title'] = values['title_ar']
                    notification_data['message'] = values['message_ar']
                else:
                    notification_data['title'] = values['title_en']
                    notification_data['message'] = values['message_en']
                notification_data['users'].extend(list(users))
                response = send_notification(notification_data)
        
            return JsonResponse({'success': True, 'message': _(f'Sent notifications to {count} users')}, status=200)
        else:
            return JsonResponse({'success': False, 'message': _(f'Users not found')}, status=401)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_company_wallet(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        company_id = json_data.get('company_id')
        jod_balance = json_data.get('jod_balance')
        ils_balance = json_data.get('ils_balance')
        if not company_id:
            raise get_error_response('GENERAL_003', {'fields': 'company_id'})
        
        try:
            company = Company.objects.get(company_id=company_id)
        except Company.DoesNotExist:
            raise get_error_response('GENERAL_008', {'field': 'company_id', 'value': company_id})
        
        company_wallet = CompanyWallet.objects.get(company=company)
        company_wallet.jod_balance = jod_balance
        company_wallet.ils_balance = ils_balance
        company_wallet.save()
        
        serializer = CompanySerializer(company)
        return JsonResponse({'success': True, 'company': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_company_wallet(request):
    try:
        company_id = request.GET.get('company_id')
        if not company_id:
            raise get_error_response('GENERAL_003', {'fields': 'company_id'})
        
        try:
            company = Company.objects.get(company_id=company_id)
        except Company.DoesNotExist:
            raise get_error_response('GENERAL_008', {'field': 'company_id', 'value': company_id})
        
        company_wallet, _ = CompanyWallet.objects.get_or_create(company=company)
        serializer = CompanyWalletSerializer(company_wallet)
        return JsonResponse({'success': True, 'company_wallet': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def map_statuses(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        status_mapping = json_data.get('status_mapping', [])
        delivery_company = json_data.get('delivery_company')

        try:
            delivery_company = DeliveryCompany.objects.get(id=delivery_company)
        except DeliveryCompany.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'DeliveryCompany', 'field': 'id', 'value': delivery_company})
        
        try:
            with transaction.atomic():
                DeliveryStatusMap.objects.filter(delivery_company=delivery_company).delete()

                for status_map in status_mapping:
                    if status_map.get('status', None):
                        status = StatusMap.objects.get(status_code=status_map.get('status'))
                    else:
                        status = None
                    DeliveryStatusMap.objects.create(delivery_company=delivery_company, status=status, imported_status_code=status_map.get('imported_status_code'), imported_status_name=status_map.get('imported_status_name'))
        except Exception as e:
                raise get_error_response('GENERAL_007', {'error': str(e)})
        
        if DeliveryStatusMap.objects.filter(delivery_company=delivery_company, status=None).exists():
            delivery_company.fully_synced_statuses = False
        else:
            delivery_company.fully_synced_statuses = True
        delivery_company.save()

        statuses = DeliveryStatusMap.objects.filter(delivery_company=delivery_company)
        serializer = DeliveryStatusMapSerializer(statuses, many=True)
        return JsonResponse({'success': True, 'delivery_status_map': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_delivery_company_statuses(request):
    try:
        delivery_company = request.GET.get('delivery_company')

        try:
            delivery_company = DeliveryCompany.objects.get(id=delivery_company)
        except DeliveryCompany.DoesNotExist:
                raise get_error_response('GENERAL_002', {'model': 'DeliveryCompany', 'field': 'id', 'value': delivery_company})
        
        if delivery_company.is_olivery_company:
            params = {
                "jsonrpc": "2.0",
                "params": {

                }
            }
            response = send_api_to_delivery_company(delivery_company, '/connection/get_statuses', params)
            if response:
                code = response['code']
                if code == 200:
                    result = response['result']
                    statuses = result.get('response', [])
                    return JsonResponse({'success': True, 'statuses': statuses}, status=200)
                else:
                    return JsonResponse({'success': False, 'error': result['message']}, status=400)
        else:
            request_template = RequestTemplate.objects.get(delivery_company=delivery_company, name='get_statuses')
            params = build_request_params(request_template, None)
            response = send_dynamic_api_request(request_template, params)
            return JsonResponse({'success': True, 'statuses': response.get('data')}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_delivery_status_map(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('DeliveryStatusMap', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def auto_map_statuses(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        delivery_company = json_data.get('delivery_company')

        try:
            delivery_company = DeliveryCompany.objects.get(id=delivery_company)
        except DeliveryCompany.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'DeliveryCompany', 'field': 'id', 'value': delivery_company})

        auto_map_statuses_util(delivery_company)

        if DeliveryStatusMap.objects.filter(delivery_company=delivery_company, status=None).exists():
            delivery_company.fully_synced_statuses = False
        else:
            delivery_company.fully_synced_statuses = True
        delivery_company.save()

        return JsonResponse({'success': True,'message': _('Auto mapping statuses completed successfully')}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_variant_names(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        variants = json_data.get('variants', [])

        for variant in variants:
            id = variant.get('id')
            variant_name = variant.get('variant_name')

            try:
                variant = ProductVariant.objects.get(id=id)
            except ProductVariant.DoesNotExist:
                raise get_error_response('GENERAL_002', {'model': 'Variant', 'field': 'id', 'value': id})

            variant.name = variant_name
            variant.save()

        return JsonResponse({'success': True,'message': _('Variant names updated successfully')}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def add_limit_upgrade(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        limit_upgrade = LimitUpgrade.objects.create(**json_data)
        serializer = LimitUpgradeSerializer(limit_upgrade)
        return JsonResponse({'success': True, 'limit_upgrade': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_limit_upgrade(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        limit_upgrade, _ = LimitUpgrade.objects.update_or_create(id = json_data.pop('id'), defaults=json_data)
        serializer = LimitUpgradeSerializer(limit_upgrade)
        return JsonResponse({'success': True, 'limit_upgrade': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_limit_upgrades(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('LimitUpgrade', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_company(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        company_id = json_data.get('company_id')
        if not company_id:
            return JsonResponse({'success':False, 'error': 'Company ID is required'}, status=400)
        try:
            company = Company.objects.get(company_id=company_id)
        except Company.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'Company', 'field': 'company_id', 'value': company_id})
        
        if json_data.get('owner', None):
            json_data['owner'] = User.objects.get(id=json_data.get('owner'))
    
        with transaction.atomic():
            for field, value in json_data.items():
                if hasattr(company, field):
                    setattr(company, field, value)  
            company.save()

        return JsonResponse({'success': True, 'message': _('Company updated successfully')}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_users_otp(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('OtpCodes', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_error_logs(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    response_data = get_records('ErrorLogs', json_data, False)
    return JsonResponse(response_data, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_connect_errors(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    response_data = get_records('ConnectErrorData', json_data, False)
    return JsonResponse(response_data, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def sync_connect_errors(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    for error_code, error_details in ERROR_MESSAGES.items():
        ConnectErrorData.objects.update_or_create(
            title=error_code,
            defaults={
                'message': error_details['message'],
                'what_to_do': error_details['what_to_do'],
                'status': error_details['status'],
            },
        )
    return JsonResponse({'success': True, 'message': 'Errors synced successfully'}, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_areas(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('Area', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_request_templates(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('RequestTemplate', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_response_templates(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('ResponseTemplate', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def build_dynamic_request(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        template_id = json_data.get('id')
        if not template_id:
            return JsonResponse({"error": "Template ID is required"}, status=400)

        try:
            template = RequestTemplate.objects.get(id=template_id)
        except RequestTemplate.DoesNotExist:
            return JsonResponse({"error": "Template not found"}, status=404)

        model_name = template.model_name
        model = apps.get_model(app_label='api', model_name=model_name)
        instance_id = json_data.get('instance_id')
        if not instance_id:
            return JsonResponse({"error": "Instance ID is required"}, status=400)

        try:
            instance = model.objects.get(id=instance_id)
        except model.DoesNotExist:
            return JsonResponse({"error": "Instance not found"}, status=400)

        response = build_request_params(template, instance)

        return JsonResponse({
            "message": "Request successfully built",
            "request_body": response,
        }, status=200)

    except ConnectError as e:
        response = e.to_dict()
        error_status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=error_status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def create_request_template(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        values = json_data
        
        if values.get('delivery_company', None):
            values['delivery_company'] = DeliveryCompany.objects.get(id=values.get('delivery_company'))
        elif values.get('integration_template', None):
            values['integration_template'] = IntegrationTemplate.objects.get(id=values.get('integration_template'))
        else:
            raise get_error_response('GENERAL_003', {'fields': 'delivery_company or integration_template'})
        prerequisites_data = values.pop('prerequisites', [])
        request_template = RequestTemplate.objects.create(**values)

        for prereq in prerequisites_data:
            prerequisite_id = prereq.get('prerequisite_id')
            position = prereq.get('position', 0)
            input = prereq.get('input', {})

            prerequisite = RequestTemplate.objects.get(id=prerequisite_id)

            RequestTemplatePrerequisite.objects.create(
                request_template=request_template,
                prerequisite=prerequisite,
                position=position,
                input=input
            )

        serializer = RequestTemplateSerializer(request_template)
        return JsonResponse({'success': True, 'request_template': serializer.data}, status=201)

    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_request_template(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        values = json_data
        prerequisites_data = values.pop('prerequisites', [])
        if values.get('delivery_company', None):
            values['delivery_company'] = DeliveryCompany.objects.get(id=values.get('delivery_company'))
        elif values.get('integration_template', None):
            values['integration_template'] = IntegrationTemplate.objects.get(id=values.get('integration_template'))
        else:
            raise get_error_response('GENERAL_003', {'fields': 'delivery_company or integration_template'})
        RequestTemplate.objects.filter(id=json_data.get('id')).update(**values)
        request_template = RequestTemplate.objects.get(id=json_data.get('id'))

        existing_prereqs = RequestTemplatePrerequisite.objects.filter(request_template=request_template)
        existing_prereqs_dict = {p.prerequisite_id: p for p in existing_prereqs}

        new_prereq_ids = set()

        for prereq_data in prerequisites_data:
            prerequisite_id = prereq_data.get('prerequisite_id')
            position = prereq_data.get('position', 0)
            input = prereq_data.get('input', {})

            new_prereq_ids.add(prerequisite_id)

            if int(prerequisite_id) in existing_prereqs_dict.keys():
                prereq_instance = existing_prereqs_dict[int(prerequisite_id)]
                changed = False
                if prereq_instance.position != position:
                    prereq_instance.position = position
                    changed = True
                if prereq_instance.input != input:
                    prereq_instance.input = input
                    changed = True
                if changed:
                    prereq_instance.save()
            else:
                RequestTemplatePrerequisite.objects.create(
                    request_template=request_template,
                    prerequisite_id=prerequisite_id,
                    position=position,
                    input=input
                )

        for prereq_id, prereq_obj in existing_prereqs_dict.items():
            if prereq_id not in new_prereq_ids:
                prereq_obj.delete()
        serializer = RequestTemplateSerializer(request_template)
        return JsonResponse({'success': True, 'request_template': serializer.data}, status=201)

    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def create_response_template(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        values = json_data
        values['request_template'] = RequestTemplate.objects.get(id=values.get('request_template'))
        response_template = ResponseTemplate.objects.create(**values)
        serializer = ResponseTemplateSerializer(response_template)
        return JsonResponse({'success': True, 'response_template': serializer.data}, status=201)

    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_response_template(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        values = json_data
        values['request_template'] = RequestTemplate.objects.get(id=values.get('request_template'))
        ResponseTemplate.objects.filter(id=json_data.get('id')).update(**values)
        response_template = ResponseTemplate.objects.get(id=json_data.get('id'))
        serializer = ResponseTemplateSerializer(response_template)
        return JsonResponse({'success': True, 'response_template': serializer.data}, status=201)

    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def build_dynamic_response(request):
    json_data = request.data
    template_id = json_data.get("template_id")
    response_body = json_data.get("response_body")
    http_status = json_data.get("http_status", 200)

    if not template_id:
        return JsonResponse({"error": "Template ID is required"}, status=400)

    if response_body is None:
        return JsonResponse({"error": "Response body is required"}, status=400)

    try:
        template = RequestTemplate.objects.get(id=template_id)
    except RequestTemplate.DoesNotExist:
        return JsonResponse({"error": "Template not found"}, status=404)

    try:
        response_template = template.response_template
    except RequestTemplate.response_template.RelatedObjectDoesNotExist:
        return JsonResponse({"error": "No response template found for this request template"}, status=404)

    # Build response schema from ResponseTemplate model
    response_schema = {
        "success_type": response_template.success_type,
        "success_values": response_template.success_values,
        "failure_values": response_template.failure_values,
        "success_path": response_template.success_path,
        "data_path": response_template.data_path,
        "data_mapping": response_template.data_mapping,
        "error_path": response_template.error_path,
        "process_function": response_template.process_function,
    }

    # Use the serializer to parse response
    serializer = DynamicResponseSerializer(response_schema=response_schema)
    processed_response = serializer.parse_response(MockResponse(response_body, http_status))

    return JsonResponse(processed_response, status=http_status)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_delivery_company_records(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('DeliveryCompany', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def add_delivery_company(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        values = json_data
        country = Country.objects.get(code=values.get('country'))
        values['country'] = country
        if values.get('integration_template', None):
            values['integration_template'] = IntegrationTemplate.objects.get(id=values.get('integration_template'))
        else:
            values['integration_template'] = None
        with transaction.atomic():
            delivery_company = DeliveryCompany.objects.create(**values)
            if values.get('integration_template', None):
                apply_integration_template_to_company(integration_template=values.get('integration_template'), delivery_company=delivery_company, meta_values=delivery_company.template_meta_data)
        serializer = DeliveryCompanySerializer(delivery_company)
        return JsonResponse(serializer.data, status=201)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_delivery_company(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        values = json_data
        country = Country.objects.get(code=values.get('country'))
        values['country'] = country
        if values.get('integration_template', None):
            values['integration_template'] = IntegrationTemplate.objects.get(id=values.get('integration_template'))
        else:
            values['integration_template'] = None
        delivery_company = DeliveryCompany.objects.filter(id=json_data.get('id')).first()
        apply_template = False
        if values.get('integration_template', None) and delivery_company.integration_template != values.get('integration_template'):
            apply_template = True
        with transaction.atomic():
            DeliveryCompany.objects.filter(id=json_data.get('id')).update(**values, updated_at=timezone.now())
            delivery_company = DeliveryCompany.objects.filter(id=json_data.get('id')).first()
            if apply_template:
                apply_integration_template_to_company(integration_template=values.get('integration_template'), delivery_company=delivery_company, meta_values=delivery_company.template_meta_data)
        serializer = DeliveryCompanySerializer(DeliveryCompany.objects.get(id=json_data.get('id')))
        return JsonResponse(serializer.data, status=201)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_optimus_areas(request):
    from ..optimus_areas import sub_areas
    return JsonResponse({'success': True, 'areas': sub_areas}, status=200)

@csrf_exempt
@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_logestechs_statuses(request):
    statuses = [
        {
            'status_code': 'DELIVERED_TO_RECIPIENT',
            'delivery_company_status': 'Delivered to Recipient'
        },
        {
            'status_code': 'PENDING_CUSTOMER_CARE_APPROVAL',
            'delivery_company_status': 'Pending Customer Care Approval'
        },
        {
            'status_code': 'CANCELLED',
            'delivery_company_status': 'Cancelled'
        },
        {
            'status_code': 'APPROVED_BY_CUSTOMER_CARE_AND_WAITING_FOR_DISPATCHER',
            'delivery_company_status': 'Approved by Customer Care and waiting for dispatcher'
        },
        {
            'status_code': 'SCANNED_BY_DRIVER_AND_IN_CAR',
            'delivery_company_status': 'Scanned by Driver and in car'
        },
        {
            'status_code': 'RETURNED_BY_RECIPIENT',
            'delivery_company_status': 'Returned by Recipient'
        },
        {
            'status_code': 'POSTPONED_DELIVERY',
            'delivery_company_status': 'Postponed delivery'
        },
        {
            'status_code': 'FAILED',
            'delivery_company_status': 'Failed'
        },
        {
            'status_code': 'PARTIALLY_DELIVERED',
            'delivery_company_status': 'Partially Delivered'
        },
        {
            'status_code': 'SWAPPED',
            'delivery_company_status': 'Swapped'
        },
        {
            'status_code': 'COMPLETED',
            'delivery_company_status': 'Completed'
        }
    ]
    return JsonResponse({'success': True, 'statuses': statuses}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_billing_logs(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('BillingLog', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_resellers(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('Reseller', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def activate_reseller(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        try:
            reseller = Reseller.objects.get(id=json_data['id'])
        except Reseller.DoesNotExist:
            raise get_error_response('GENERAL_002', {'mode': 'Reseller', 'field':'id', 'value': json_data['id']})
        reseller.is_active = True
        reseller.save()

        return JsonResponse({'success': True, 'message': _('Reseller activated successfully')}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def deactivate_reseller(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        try:
            reseller = Reseller.objects.get(id=json_data['id'])
        except Reseller.DoesNotExist:
            raise get_error_response('GENERAL_002', {'mode': 'Reseller', 'field':'id', 'value': json_data['id']})
        reseller.is_active = False
        reseller.save()

        return JsonResponse({'success': True, 'message': _('Reseller deactivated successfully')}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def impersonate_reseller(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        try:
            reseller = Reseller.objects.get(id=json_data['id'])
        except Reseller.DoesNotExist:
            raise get_error_response('GENERAL_002', {'mode': 'Reseller', 'field':'id', 'value': json_data['id']})
        
        token = generate_JWT(reseller)

        return JsonResponse({'success': True, 'token': token}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def add_reseller_pricelist_item(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        values  = json_data
        values['country'] = Country.objects.get(code=values['country'])
        item = ResellerPricelistTemplate.objects.create(**values)
        serializer = ResellerPricelistTemplateSerializer(item)
        return JsonResponse({'success': True, 'pricelist_item': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_reseller_pricelist_item(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        values  = json_data
        values['country'] = Country.objects.get(code=values['country'])
        item, _ = ResellerPricelistTemplate.objects.update_or_create(id=values.pop('id'),defaults=values)
        serializer = ResellerPricelistTemplateSerializer(item)
        return JsonResponse({'success': True, 'pricelist_item': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['DELETE'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def delete_reseller_pricelist_item(request):
    try:
        id = request.DELETE.get('id')
        if id is None:
            raise get_error_response('GENERAL_001', {})
        
        ResellerPricelistItem.objects.get(id=id).delete()
        return JsonResponse({'success': True, 'message': _('Pricelist Item Template Deleted Successfully')}, status=200)

    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_reseller_update(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('ResellerUpdate', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def approve_reseller_update(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        update = ResellerUpdate.objects.get(reseller_id=json_data['id'], status='pending')
        reseller = update.reseller
        update_fields = update.fields.all()
        pricelist_items_updates = update.pricelist_items_updates.all()
        branch_updates = update.branch_updates.all()

        with transaction.atomic():
            apply_field_updates(update_fields, reseller)
            update_pricelist_items(pricelist_items_updates, reseller)
            update_branches(branch_updates, reseller)
            update.status = 'accepted'
            reseller.update_pending = False
            reseller.save()
            update.save()

        return JsonResponse({'success': True, 'message': 'Reseller update approved and changes applied.'}, status=200)

    except ResellerUpdate.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Reseller update not found or already processed.'}, status=404)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_reseller_limit(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        reseller = Reseller.objects.get(id=json_data['id'])
        reseller.limit = json_data['limit']
        reseller.save()

        serializer = ResellerSerializer(reseller)

        return JsonResponse({'success': True, 'reseller': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_company_reseller(request):
    try:
        company_id = request.GET.get('company_id')
        reseller_company = ResellerCompany.objects.get(company_id=company_id)
        print("reseller_company", reseller_company)
        serializer = ResellerCompanySerializer(reseller_company)

        return JsonResponse({'success': True, 'reseller_company': serializer.data}, status=200)
    
    except ResellerCompany.DoesNotExist:
        return JsonResponse({'success': True, 'reseller_company': None}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def set_business_delivery_companies(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        company_id = json_data.get('company_id')
        delivery_companies = json_data.get('delivery_companies', [])
        company = Company.objects.get(company_id=company_id)

        CompanyDeliveryCompany.objects.filter(company=company).delete()
        for delivery_company in delivery_companies:
            CompanyDeliveryCompany.objects.get_or_create(company=company, delivery_company=DeliveryCompany.objects.get(id=delivery_company))


        return JsonResponse({'success': True, 'message': _('Company Updated Successfully')}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_company_delivery_company(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('CompanyDeliveryCompany', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_reseller_wallet(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        wallet = ResellerWallet.objects.get(id=json_data['id'])
        wallet.credit = json_data.get('credit')
        wallet.debit = json_data.get('debit')
        wallet.save()
        serializer = ResellerWalletSerializer(wallet)

        return JsonResponse({'success': True, 'wallet': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_debit_pricing(request):
    try:
        pricing, _ = ResellerDebitPricing.objects.get_or_create(id=1)
        serializer = ResellerDebitPricingSerializer(pricing)
        return JsonResponse({'success': True, 'pricing': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_debit_pricing(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        pricing, _ = ResellerDebitPricing.objects.update_or_create(id=1, defaults=json_data)
        pricing.save()
        serializer = ResellerDebitPricingSerializer(pricing)
        return JsonResponse({'success': True, 'pricing': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def approve_reseller(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        reseller = Reseller.objects.get(id=json_data['id'])
        reseller.is_approved = True
        reseller.waiting_confirmation = False

        if reseller.is_delivery_company:
            delivery_company_id = json_data.get('delivery_company')
            delivery_company = DeliveryCompany.objects.get(id=delivery_company_id)
            if Reseller.objects.filter(delivery_company=delivery_company).exists():
                raise get_error_response('RESELLER_1202')
            reseller.delivery_company = delivery_company
        reseller.save()
        serializer = ResellerSerializer(reseller)
        return JsonResponse({'success': True, 'reseller': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def get_reseller_companies(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('ResellerCompany', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def update_reseller_company(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        id = json_data.get('id')
        reseller = json_data.get('reseller', None)
        if reseller:
            reseller = Reseller.objects.get(id=reseller)
        reseller_company = ResellerCompany.objects.get(id=id)
        if reseller is None:
            reseller_company.delete()
        reseller_company.reseller = reseller
        reseller_company.save()
        serializer = ResellerCompanySerializer(reseller_company)
        return JsonResponse({'success': True, 'reseller_company': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def add_reseller_company(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        reseller = json_data.get('reseller')
        company_id = json_data.get('company_id')
        
        reseller = Reseller.objects.get(id=reseller)
        company = Company.objects.get(company_id=company_id)

        reseller_company = ResellerCompany.objects.create(company=company, reseller=reseller, name=company.company_name, mobile_number=company.company_mobile, status='Activated', status_code='activated')

        serializer = ResellerCompanySerializer(reseller_company)
        return JsonResponse({'success': True, 'reseller_company': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def generate_reseller_token(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        company_id = json_data.get('delivery_company_id')
        delivery_company = DeliveryCompany.objects.get(id=company_id)
        reseller = Reseller.objects.filter(delivery_company=delivery_company).last()
        if reseller:
            reseller_token, _ = ResellerToken.objects.get_or_create(reseller=reseller, expires_at__isnull=True)
            return JsonResponse({'success': True, 'token': reseller_token.token}, status=200)

        return JsonResponse({'success': True, 'message': _('No Reseller found')}, status=400)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
@csrf_exempt
def set_to_aggregator(request):
    try:
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})
        reseller = Reseller.objects.get(id=json_data['id'])
        reseller.is_aggregator = True
        reseller.save()
        serializer = ResellerSerializer(reseller)
        return JsonResponse({'success': True, 'reseller': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def add_follow_up_comment(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        message = json_data.get('message')
        company=json_data.get('company')
        company = Company.objects.get(company_id=company)
        user = request.user
        comment = FollowUpComment.objects.create(message=message, created_by=user.username, company=company)
        serializer = FollowUpCommentSerializer(comment)
        return JsonResponse(serializer.data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_follow_up_comment(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        response_data = get_records('FollowUpComment', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def add_integration_template(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        values = json_data
        integration_template = IntegrationTemplate.objects.create(**values)
        serializer = IntegrationTemplateSerializer(integration_template)
        return JsonResponse({'success': True, 'integration_template': serializer.data}, status=201)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_integration_templates(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('IntegrationTemplate', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_integration_template(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        values = json_data
        integration_template = IntegrationTemplate.objects.get(id=values.get('id'))
        for key, value in values.items():
            setattr(integration_template, key, value)
        integration_template.save()
        serializer = IntegrationTemplateSerializer(integration_template)
        return JsonResponse({'success': True, 'integration_template': serializer.data}, status=201)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])    
def reapply_template(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        delivery_company = DeliveryCompany.objects.get(id=json_data.get('delivery_company'))
        integration_template = delivery_company.integration_template
        if integration_template:
            with transaction.atomic():
                apply_integration_template_to_company(integration_template=integration_template, delivery_company=delivery_company, meta_values=delivery_company.template_meta_data)
        serializer = DeliveryCompanySerializer(delivery_company)
        return JsonResponse({'success': True, 'delivery_company': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)