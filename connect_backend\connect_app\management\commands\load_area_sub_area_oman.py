import json
from django.core.management import BaseCommand, call_command
from api.lookups.lookup_model import Area, FuzzyArea, FuzzySubArea, SubArea, AreaGroup, Country
class Command(BaseCommand):
    help = 'Update areas, subareas, and area groups from fixtures and update references'

    def handle(self, *args, **kwargs):
        
        fixture = 'fixtures/oman_area_and_sub_areas.json'

        with open(fixture) as f:
                data = json.load(f)
                for item in data:
                    try:
                        area = Area.objects.get(name=item['arabic_area_name'])
                    except Area.DoesNotExist:
                        country = Country.objects.get(id=4)
                        area = Area.objects.create(
                            name=item['arabic_area_name'],
                            country = country,
                            created_by = 'System',
                            updated_by = 'System',
                            )
                        fuzzy_area = FuzzyArea.objects.create(area_name_arabic=item['arabic_area_name'], area_name_english=item['area'], area_name_hebrew='')
                
                for item in data:
                    try:
                        sub_area = SubArea.objects.get(name=item['arabic_sub_area_name'])
                    except SubArea.DoesNotExist:
                        try:
                            area = Area.objects.get(name=item['arabic_area_name'])
                            sub_area = SubArea.objects.create(
                                name=item['arabic_sub_area_name'],
                                area = area,
                                created_by = 'System',
                                updated_by = 'System',
                            )
                            fuzzy_sub_area = FuzzySubArea.objects.update_or_create(sub_area_name_arabic=item['arabic_sub_area_name'],defaults={'sub_area_name_english': item['subarea'],'sub_area_name_hebrew': ''})

                        except Area.DoesNotExist:
                            continue
                        
