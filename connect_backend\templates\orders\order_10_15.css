@page {
    size: 100mm 150mm;
    margin: 0;
}

.o-table .cell-truncate {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 11rem;
    width: 11rem;
}

.waybill-container {
    padding: 3rem;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: solid 0.033rem;
    padding: .2rem .6rem;
    height: 7.15rem;
}

.header-info .info-item {
    padding-bottom: .3rem;
}

.barcode-image {
    display: inline-block;
    height: 5.8rem;
    width: 18rem;
}

.o-table {
    border-spacing: 0.15rem 0.35rem;
}

.o-table td,
th {
    padding: .4rem .6rem;
    width: 25%;
    text-align: center;
}

.card {
    border: solid 0.033rem;
    border-radius: .5rem;
    padding: .9rem;
    margin-top: .5rem;
}

.card-header {
    font-size: 1.25rem;
    font-weight: bold;
    border-bottom: .15rem solid;
    padding-bottom: .5rem;
    margin-bottom: .5rem;
}

.cards-container {
    display: inline-block;
    width: 50%;
    vertical-align: top;
    padding: 0rem 0rem 0rem .45rem;
}

/* :dir(rtl)  */
.cards-container {
    padding: 0rem .45rem 0rem 0rem;
}

.cards-container:first-child {
    padding-right: .45rem;
    padding-left: 0rem;
}

:dir(rtl) 
.cards-container:first-child {
    padding-right: 0rem;
    padding-left: .45rem;
}

.notes-content {
    height: 6rem;
    overflow: hidden;
}

.products-container {
    min-height: 9rem;
    max-height: 14rem;
    position: relative;
    overflow: hidden;
}

.card-header-container {
    display: flex;
}

.card-header-container .card-header {
    flex-grow: 1;
}

.business-logo {
    max-height: 5rem;
}

.recipient-card {
    max-height: 20rem;
}
