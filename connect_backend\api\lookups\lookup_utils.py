from ..util_functions import *
from .lookup_model import *
from django.db import transaction
from ..logs.logs_model import *

def validate_lookup(values, required_fields):
    validation_errors = []
    foreign_key_objects = {}

    for field in required_fields:
        if not values.get(field):
            validation_errors.append(f'Missing field: {field}')

    foreign_key_objects.update(validate_foreign_keys(values, required_fields, validation_errors))

    return validation_errors, foreign_key_objects

def build_lookup_values(lookup, foreign_key_objects):
    values = {**lookup, **foreign_key_objects}
    return values

def create_area(values):
    try:
        with transaction.atomic():
            area = Area.objects.create(**values)
            log = create_log('Area', None, 'create', values['created_by'], area.id)
            fields = get_tracked_fields(None, 'Area')
            log_tracked_fields(log, values, fields)
            sub_area = SubArea.objects.create(name=area.name, code=area.code, area=area, is_default_for_area=True)
            delete_log_if_no_changes(log)
    except Exception as e:
        raise ValidationError(f"Error creating area: {str(e)}")
    return area

def update_lookup_util(values, lookup):
    log = create_log(lookup._meta.model_name, None, 'update', values['updated_by'], lookup.id)
    try:
        with transaction.atomic():
            lookup = update_lookup_fields(lookup, values, log)
    except Exception as e:
        delete_log_if_no_changes(log)
        raise ValidationError('Lookup was not updated due to' + str(e))
    delete_log_if_no_changes(log)
    lookup.save()  
    return lookup

def update_lookup_fields(lookup, values, log):
    fields = get_tracked_fields(None, lookup._meta.model_name)

    for key, value in values.items():
        old_value = getattr(lookup, key, None)
        if old_value != value:
            log_and_update_field(lookup, key, value, old_value, log, bool(key in fields))

    return lookup

def create_sub_area(values):
    try:
        with transaction.atomic():
            sub_area = SubArea.objects.create(**values)
            log = create_log('SubArea', None, 'create', values['created_by'], sub_area.id)
            fields = get_tracked_fields(None, 'SubArea')
            log_tracked_fields(log, values, fields)
            delete_log_if_no_changes(log)
    except Exception as e:
        raise ValidationError(f"Error creating sub_area: {str(e)}")
    return sub_area
