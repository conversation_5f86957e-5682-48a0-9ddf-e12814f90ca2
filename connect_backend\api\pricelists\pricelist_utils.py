from .pricelist_model import *
from ..util_functions import *
from django.db import transaction
from django.core.exceptions import ValidationError

def create_pricelist(values, pricelist_items):
    try:
        with transaction.atomic():
            pricelist = Pricelist.objects.create(**values)
            log = create_log('Pricelist', values['company'], 'create', values['created_by'], pricelist.id)
            fields = get_tracked_fields(values['company'], 'Pricelist')
            log_tracked_fields(log, values, fields)
            delete_log_if_no_changes(log)
            create_pricelist_items(pricelist, pricelist_items, values['updated_by'])
    except Exception as e:
        raise ValidationError(f"Error creating pricelist: {str(e)}")
    return pricelist

def update_pricelist_util(values, pricelist_items):
    try:
        pricelist = Pricelist.objects.filter(id=values['id'], company=values['company']).first()
        if not pricelist:
            raise ValidationError("Pricelist not found")

        log = create_log('Pricelist', values['company'], 'update', values['updated_by'], pricelist.id)
        fields = get_tracked_fields(pricelist.company, 'Pricelist')
        
        try:
            with transaction.atomic():
                for key, value in values.items():
                    old_value = getattr(pricelist, key, None)
                    if old_value != value:
                        log_and_update_field(pricelist, key, value, old_value, log, bool(key in fields))
                pricelist.save()
                create_pricelist_items(pricelist, pricelist_items, values['updated_by'])
        except ValidationError as e:
            raise ValidationError(str(e))
        delete_log_if_no_changes(log)
    except Exception as e:
        raise ValidationError(f"Error updating pricelist: {str(e)}")

    return pricelist

def create_pricelist_items(pricelist, pricelist_items, name):
    validation_errors, item_foreign_key_objects, new_items = validate_and_prepare_items(pricelist_items)

    if validation_errors:
        raise ValidationError(validation_errors)

    with transaction.atomic():
        existing_item_ids = get_existing_item_ids(pricelist)
        item_ids = set(item_foreign_key_objects.keys())
        ids_to_delete = existing_item_ids - item_ids

        if ids_to_delete:
            delete_pricelist_items(pricelist, ids_to_delete, pricelist.company, name)

        process_items(pricelist, pricelist_items, item_foreign_key_objects, new_items, name)


def validate_and_prepare_items(pricelist_items):
    validation_errors = []
    item_foreign_key_objects = {}
    new_items = set()

    for index, item in enumerate(pricelist_items):
        errors, fk_objects = validate_pricelist_item(item)
        validation_errors.extend(errors)

        item_id = str(item.get('id', ''))

        if not item_id:
            temp_key = f'new_item_{index}'
            new_items.add(temp_key)
            item_foreign_key_objects[temp_key] = fk_objects
        else:
            if item_id.isdigit():
                item_foreign_key_objects[item_id] = fk_objects
            else:
                validation_errors.append(f'Invalid item ID: {item_id}')

    return validation_errors, item_foreign_key_objects, new_items


def get_existing_item_ids(pricelist):
    return set(map(str, PricelistItem.objects.filter(pricelist=pricelist).values_list('id', flat=True)))


def process_items(pricelist, pricelist_items, item_foreign_key_objects, new_items, name):
    for item_id, fk_objects in item_foreign_key_objects.items():
        if item_id.startswith('new_item_'):
            create_pricelist_item(pricelist, pricelist_items, item_id, fk_objects, name)
        else:
            update_pricelist_item(item_id, pricelist, pricelist_items, fk_objects, name)


def create_pricelist_item(pricelist, pricelist_items, item_id, fk_objects, name):
    index = int(item_id.split('_')[-1])
    item = pricelist_items[index]
    values = build_pricelist_items_values(item, fk_objects, pricelist)
    pricelist_item = PricelistItem.objects.create(**values)
    log = create_log('PricelistItem', pricelist.company, 'create', name, pricelist_item.id)
    log_tracked_fields(log, values, get_tracked_fields(pricelist.company, 'PricelistItem'))
    delete_log_if_no_changes(log)


def update_pricelist_item(item_id, pricelist, pricelist_items, fk_objects, name):
    try:
        pricelist_item = PricelistItem.objects.get(id=item_id)
    except PricelistItem.DoesNotExist:
        raise ValidationError([f'PricelistItem with ID {item_id} does not exist.'])

    item = next((item for item in pricelist_items if str(item.get('id')) == item_id), None)
    if item is None:
        raise ValidationError([f'Item with ID {item_id} not found in the provided items.'])

    values = build_pricelist_items_values(item, fk_objects, pricelist)
    log = create_log('PricelistItem', pricelist.company, 'update', name, item_id)

    for key, value in values.items():
        old_value = getattr(pricelist_item, key, None)
        if old_value != value:
            log_and_update_field(pricelist_item, key, value, old_value, log, bool(key in get_tracked_fields(pricelist.company, 'PricelistItem')))
    
    pricelist_item.save()
    delete_log_if_no_changes(log)


def delete_pricelist_items(pricelist ,item_ids, company, name):
    for item_id in item_ids:
        pricelist_item = PricelistItem.objects.get(id=item_id)
        log = create_log('Pricelist', company, f'REMOVE&{pricelist_item.from_area.name}&{pricelist_item.to_area.name}&{pricelist_item.price}', name, pricelist.id)
        pricelist_item.delete()

def build_pricelist_items_values(pricelist_item, foreign_key_objects, pricelist):
    from_area = foreign_key_objects.get('from_area')
    to_area = foreign_key_objects.get('to_area')
    
    return {
        'from_area': from_area,
        'to_area': to_area,
        'price': pricelist_item.get('price'),
        'pricelist': pricelist
    }

def validate_pricelist_items(pricelist_items):
    validation_errors = []
    foreign_key_objects = {}
    
    for item in pricelist_items:
        errors, fk_objects = validate_pricelist_item(item)
        validation_errors.extend(errors)
        foreign_key_objects.update(fk_objects)
    
    return validation_errors, foreign_key_objects

def validate_pricelist_item(pricelist_item):
    validation_errors = []
    foreign_key_objects = {}
    
    required_fields = ['from_area', 'to_area', 'price']
    for field in required_fields:
        if pricelist_item.get(field) in [None, '']:
            validation_errors.append(f'Missing field: {field}')
    
    if 'from_area' in required_fields:
        area = {'area': pricelist_item.get('from_area')}
        foreign_key_objects['from_area'] = get_area(area, validation_errors, None)

    if 'to_area' in required_fields:
        area = {'area': pricelist_item.get('to_area')}
        foreign_key_objects['to_area'] = get_area(area, validation_errors, None)
    
    return validation_errors, foreign_key_objects

def set_code(values):
    company = values['company']
    name = values['name']
    last_pricelist = Pricelist.objects.filter(company=company).last()
    if last_pricelist:
        last_code_part = last_pricelist.code.split('_')[-1]
        try:
            last_code_number = int(last_code_part)
        except ValueError:
            last_code_number = 0
        code = f"{name}_{last_code_number + 1}"
    else:
        code = f"{name}_1"

    return code

def create_default_pricelist(company):
    try:
        return Pricelist.objects.create(name='Default Pricelist', code='default_1', company=company)
    except Exception as e:
        raise ValidationError(f"Error creating default pricelist: {str(e)}")

def validate_pricelist(pricelist, required_fields):
    validation_errors = []
    foreign_key_objects = {}

    # Validate required fields
    for field in required_fields:
        if not pricelist.get(field):
            validation_errors.append(f'Missing field: {field}')

    # Validate foreign keys
    foreign_key_objects.update(validate_foreign_keys(pricelist, required_fields, validation_errors))

    return validation_errors, foreign_key_objects

def get_pricelist(pricelist_id, company):
    """Fetches the pricelist object."""
    return Pricelist.objects.filter(id=pricelist_id, company=company)\
                            .select_related('delivery_company')\
                            .first()

def get_pricelist_items(pricelist):
    """Fetches the pricelist items related to the given pricelist."""
    return PricelistItem.objects.filter(pricelist=pricelist)\
                                .select_related('from_area', 'to_area')

def get_pricelist_items_by_driver(user):
    """Fetches the pricelist items for a specific driver."""
    return user.driver_pricelist, PricelistItem.objects.filter(pricelist=user.driver_pricelist)\
                                .select_related('from_area', 'to_area')

def get_pricelist_items_by_connection(connection_id, company):
    """Fetches the pricelist items for a specific connection with delivery company."""
    pricelist = Pricelist.objects.filter(connection__id=connection_id, company=company).first()
    if not pricelist:
        return None, None
    return pricelist, PricelistItem.objects.filter(pricelist=pricelist)\
                                .select_related('from_area', 'to_area')