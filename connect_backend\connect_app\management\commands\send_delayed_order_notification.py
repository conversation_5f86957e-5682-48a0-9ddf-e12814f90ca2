from django.core.management.base import BaseCommand
from api.orders.order_model import Order
from api.users.user_model import User, Company, CompanyConf
from django.utils.timezone import now, timedelta
from api.notification.notification_utils import send_notification
from api.util_functions import get_company_conf
from django.utils.translation import gettext as _
from django.db.models import Q
from django.utils import timezone

class Command(BaseCommand):
    help = "Sends notification to all Super Managers with count of delayed orders"

    def handle(self, *args, **kwargs):
        for company in Company.objects.all():
            conf = get_company_conf(company)
            if not conf:
                continue
            delayed_order_filter = Q()
            if conf.delayed_order_statuses:
                delayed_order_filter &= Q(delivery_company_status__in=conf.delayed_order_statuses.values_list('delivery_company_status', flat=True))
            if conf.days_delayed:
                delayed_order_filter &= Q(delivery_status_change_date__lte=timezone.now() - timedelta(days=conf.days_delayed))

            if conf.delayed_order_statuses and conf.days_delayed:
                orders = Order.objects.filter(
                    company=company,
                    status__code__in=['with_delivery_company', 'with_driver']
                ).filter(delayed_order_filter)

                if orders:
                    self.notify_super_managers(orders)
        
        self.stdout.write(self.style.SUCCESS('Sent delayed order notifications to clients'))

    def notify_super_managers(self, orders):
        order_sequences = [order.order_sequence for order in orders]
        created_by = orders.values_list('created_by', flat=True)
        users = User.objects.filter(Q(Q(role__name='Super Manager') | Q(name__in=created_by)), company=orders.first().company)
        title = _('Delayed orders')
        message = _("You have {order_count} delayed order(s). Please follow up on these orders to ensure timely delivery and resolve any issues.")
        notification_data = {
            'message': message,
            'title': title
        }
        notification_data['data'] = {
            "filters": [{
                "operator": "and",
                "filters": [{
                    "field": "order_sequence",
                    "operator": "in",
                    "value": order_sequences
                }]
            }],
            'order_count': len(order_sequences),
            'title': title
        }
        notification_data['users'] = users
        send_notification(notification_data, context={'order_count': orders.count()})