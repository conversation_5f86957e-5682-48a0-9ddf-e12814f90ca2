from ..users.user_model import Company
from .mystore_model import MyStore
from ..util_functions import *
import re
from django.db import transaction

# return error when requird_fields empty
def validate_missing_field(values, required_fields):
    validation_errors = []
    for field in required_fields:
        if values.get(field) is None: 
            validation_errors.append(f'Missing field: {field}')
    return validation_errors

def create_banner_detail(values):
    # handle image 
    banner_detail = BannerDetail.objects.create(**values)
    return banner_detail

def get_banner_detail(values):
    return BannerDetail.objects.get(id=values['id'],mystore = values['mystore'])

def update_banner_detail_util(values):
    banner_detail = get_banner_detail(values)
    if not banner_detail:
        return None
    for key,value in values.items():
        setattr(banner_detail,key,value)
    banner_detail.save()
    return banner_detail

def mystore_name_validator(name,company):
    
    if name is None:
        return 'Missing field: Name'
    
    name_regex = re.compile(r'^[a-zA-Z0-9][a-zA-Z0-9-]{2,18}[a-zA-Z0-9]$')
    if not name_regex.match(name):
        return 'Name is not valid'
    
    existing_name_mystore = MyStore.objects.filter(name=name).exclude(company=company).exists()
    if existing_name_mystore:
        return 'Name is in use'
    
    return None

def get_mystore_util(company):
    return MyStore.objects.get(company=company)

def mystore_unique_name(company):
    name = company.company_name
    if not name:
        raise ValidationError("Error: Company doesn't have a name")
    
    sanitized_name = re.sub(r'[^a-zA-Z0-9-_]', '', name.replace(" ", "-"))
    base_name = f"{sanitized_name}-store"
    store_name = base_name
    counter = 1

    while MyStore.objects.filter(name=store_name).exists():
        store_name = f"{sanitized_name}-{counter}-store"
        counter += 1

    return store_name

def create_store(company):
    name = mystore_unique_name(company)
    existing_store = MyStore.objects.filter(company=company)
    if(not existing_store):
        values = {
            'name': name,
            'company': company,
            'primary_color': None,
            'secondary_color': None,
            'mystore_image_url': None,
            'store_template':"1"
        }
        mystore = MyStore.objects.create(**values)
        return mystore
    return None

def update_mystore_util(values):
    try:
        mystore = MyStore.objects.get(id=values['id'])
    except MyStore.DoesNotExist:
        raise ValidationError("Mystore not found")
    
    log = create_log('MyStore', values['company'], 'update', values['updated_by'], mystore.id)
    update_mystore_fields(mystore, values, log)
    delete_log_if_no_changes(log)
    mystore.save()
    return mystore

def update_mystore_fields(mystore, values, log):
    fields = get_tracked_fields(mystore.company, 'MyStore')

    for key, value in values.items():
        old_value = getattr(mystore, key, None)
        if old_value != value:
            log_and_update_field(mystore, key, value, old_value, log, bool(key in fields))


def create_store_section(values):
    with transaction.atomic():
        section = StoreSection.objects.create(
            store=values['store'],
            section_description=values["section_description"],
            section_code=values['section_code']
            )

        categories = values.pop('categories')
        if categories:
            category_instance = []
            for category in categories:
                category_instance.append(Category.objects.get(id=category))
            section.categories.set(category_instance)

        tags = values.pop('tags')
        if tags:
            tag_instances = []
            for tag in tags:
                tag_instances.append(Tags.objects.get(id=tag))
            section.tags.set(tag_instances)
        return section

def update_store_section_util(values):
    
    store_section = StoreSection.objects.get(id=values.pop('id'))
    if not store_section:
        return None
    
    categories = values.pop('categories')
    
    old_category_ids = list(store_section.categories.values_list('id', flat=True))
    new_categories = handle_categories(categories)

    # Add new categories
    for category in new_categories:
        if category.id not in old_category_ids:  
            store_section.categories.add(category)

    # Remove old categories
    for category_id in old_category_ids:
        if category_id not in [category.id for category in new_categories]:  
            store_section.categories.remove(category_id)

    tags = values.pop('tags')

    new_tags = handle_tags(tags)
    old_tag_ids = list(store_section.tags.values_list('id', flat=True))

    # Add new tags
    for tag in new_tags:
        if tag.id not in old_tag_ids:  
            store_section.tags.add(tag)

    # Remove old tags
    for tag_id in old_tag_ids:
        if tag_id not in [tag.id for tag in new_tags]: 
            store_section.tags.remove(tag_id)

    for key, value in values.items():
        setattr(store_section, key, value)
    store_section.save()

    return store_section

def handle_categories(categories):
    instance_categories = []
    for category in categories:
        category_instance = Category.objects.get(id=category)
        instance_categories.append(category_instance)
    return instance_categories

def handle_tags(tags):
    instance_tags = []
    for tag in tags:
        category_instance = Tags.objects.get(id=tag)
        instance_tags.append(category_instance)
    return instance_tags

def validate_mystore(request):
    try:
        mystore = get_origin(request)
    except:
        return None
    return mystore

