from django.db import models
from ...models import SuperModel

class Ticket(SuperModel):
    ticket_title = models.CharField(max_length=200)
    ticket_description = models.CharField(max_length=10000)
    ticket_file_url = models.CharField(max_length=200, null=True, blank=True)
    file_type = models.CharField(max_length=50, null=True, blank=True)
    fix_version = models.CharField(max_length=50)

class ReleaseDocument(SuperModel):
    document_title = models.CharField(max_length=400)
    document_content = models.Char<PERSON>ield(max_length=15000)
    is_active = models.BooleanField(default=False)