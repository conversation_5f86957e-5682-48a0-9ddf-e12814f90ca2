from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from ..util_functions import *
from .product_model import *
from .product_utils import *
from ..serializers import *
import pandas as pd
from django.db import transaction
from django.utils.translation import gettext as _
from rest_framework.decorators import *
from ..permissions import *
from rest_framework.permissions import AllowAny
from ..error.error_model import *
from ..category.category_utils import *
from ..mystore.mystore_utils import*
import uuid
from rest_framework.parsers import MultiPartParser, FormParser

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_product'])])
@subscription_required
def add_product(request):
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    required_fields = [
        'name'
        ]
    values = json_data
    company = user.company
    product_prices = values.pop('prices', [])
    values['company'] = company
    values['created_by'] = user.name
    values['updated_by'] = user.name

    validation_errors, foreign_key_objects = validate_product(values, company, required_fields)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
    
    values = build_product_values(values, foreign_key_objects)
    warehouses = values.pop('warehouses', [])

    with transaction.atomic():
        dont_create_variants = values.pop('dont_create_variants', False)
        product = create_product(values, product_prices)
        variant_values = {
            'product': product,
            'name': product.name,
            'sku': f"{product.reference_sequence or 'SKU-DEFAULT'}",
            'variant_image_url': product.images.first().product_image_url if product.images.first() else None,
            'created_by': product.created_by,
            'updated_by': product.updated_by
        }
        if not dont_create_variants:
            variant = create_variant(variant_values, [])
            variant.warehouses.set(warehouses)
            for warehouse in warehouses:
                WarehouseVariant.objects.create(warehouse=warehouse, variant=variant, physical_quantity=0, virtual_quantity=0, reserved_quantity=0)
    serializer = ProductSerializer(product)
    response = {
        'success': True,
        'product': serializer.data,
        'message': 'Product created successfully'
    }
    return JsonResponse(response, status=201)

@csrf_exempt
def update_product(request):
    if request.method!= 'PUT':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    required_fields = ['name']
    try:
        values = json_data
        company = user.company
        product_prices = values.pop('prices', [])
        values['company'] = company
        values['updated_by'] = user.name
        validation_errors, foreign_key_objects = validate_product(values, company, required_fields)
        if validation_errors:
            return JsonResponse({'success': False,'error': validation_errors}, status=400)
        
        product_images = values.pop('images', [])
        product = update_product_util(values, product_images, product_prices)
        if not product:
            return JsonResponse({'success': False, 'message': 'Product update failed'}, status=500)
    except Product.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Product not found'}, status=404)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    serializer = ProductSerializer(product)
    response = {
        'success': True,
        'product': serializer.data,
        'message': 'Product updated successfully'
    }
    return JsonResponse(response, status=200)

@csrf_exempt
@api_view(['DELETE'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['delete_product'])])
def delete_product(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    user = get_user_from_token(request)
    if not user:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    try:
        values = json_data
        company = user.company
        product = Product.objects.get(id=values.get('id'), company=company)
        product.delete()
    except Product.DoesNotExist:
        return JsonResponse({'success': False, 'error': _('Product not found')}, status=404)
    return JsonResponse({'success': True, 'message': 'Product deleted successfully'}, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_product'])])
@csrf_exempt
def get_products(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('Product', json_data, user)
    return JsonResponse(response_data, status=200)

@csrf_exempt
def get_product_by_id(request):
    if request.method!= 'GET':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    
    try:
        company = user.company
        product_id = json_data.get('id')
        product = Product.objects.get(id=product_id, company=company)
        serializer = ProductSerializer(product)
    except Product.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Product not found'}, status=404)

    return JsonResponse({'success': True, 'product': serializer.data}, status=200)

@api_view(['POST'])
@authentication_classes([])
@permission_classes([AllowAny])
@csrf_exempt
def get_store_products(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    mystore = validate_mystore(request)
    if mystore == None:
        raise get_error_response('MYSTORE_1101',{})

    response_data = get_records('Product', json_data, None, company_filter=Q(company=mystore.company))
    return JsonResponse(response_data, status=200)

@csrf_exempt
def get_store_variants(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        mystore = validate_mystore(request)
    except Exception as e:
        raise get_error_response('MYSTORE_1101',{})

    result = get_records('ProductVariant', json_data, None , company_filter=Q(product__company=mystore.company))
    return JsonResponse(result, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['import_excel_products'])])
@csrf_exempt
@parser_classes([MultiPartParser, FormParser])
@subscription_required
def test_import_products(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    if 'file' not in request.FILES:
        raise get_error_response('GENERAL_003', {'fields': ['file']})

    file = request.FILES['file']
    if file.content_type not in [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
        'application/vnd.ms-excel',
        'text/csv'
    ]:
        raise get_error_response('GENERAL_004', {})
    
    products_to_create, validation_errors, warning_mismatch = process_excel_products(file, user)
    return JsonResponse({'success': True, 'validation_errors': validation_errors, 'warning_mismatch': warning_mismatch}, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['import_excel_products'])])
@csrf_exempt
@parser_classes([MultiPartParser, FormParser])
@subscription_required
def import_products_from_excel(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    warehouse_id = request.data.get('warehouse_id',False)
    if not warehouse_id:
        raise get_error_response('GENERAL_003', {'fields': ['warehouse_id']})
    warehouse_filter = Q(id=warehouse_id) &(
        Q(
            warehouse_connection__connection_status__iexact="connected",
            warehouse_connection__company_id=user.company
        ) |
        Q(
            reseller__isnull=True,
            company_id=user.company
        )
    )
    
    warehouse = Warehouse.objects.get(warehouse_filter)
    if not warehouse:
        raise get_error_response('GENERAL_002', {'model': 'Warehouse', 'field': 'id', 'value': warehouse_id})
    
    if 'file' not in request.FILES:
        raise get_error_response('GENERAL_003', {'fields': ['file']})

    file = request.FILES['file']
    if file.content_type not in [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
        'application/vnd.ms-excel',
        'text/csv'
    ]:
        raise get_error_response('GENERAL_004', {})
    
    products_to_create, validation_errors, warning_mismatch = process_excel_products(file, user)
    if validation_errors:
        return JsonResponse({'success': True, 'validation_errors': validation_errors, 'warning_mismatch': warning_mismatch}, status=200)
    
    with transaction.atomic():
        for product_data in products_to_create:
            variants = product_data.pop('variants', [])
            product_values = {
                **product_data,
                'company': user.company
            }
            product = create_product(product_values, [])
            create_log(
                model='Product',
                company=product.company,
                action='Imported using Excel',
                created_by=user.name,
                model_id=product.id
            )
            if variants:
                for variant in variants:
                    attributes = variant.pop('attributes', [])
                    variant['product'] = product
                    variant = create_variant(variant, attributes)
                    variant.warehouses.set([warehouse])
                    WarehouseVariant.objects.create(warehouse=warehouse, variant=variant, physical_quantity=0, virtual_quantity=0, reserved_quantity=0)
                    create_log(
                        model='ProductVariant',
                        company=product.company,
                        action='Imported using Excel',
                        created_by=user.name,
                        model_id=product.id
                    )
            else:
                variant_values = {
                    'product': product,
                    'name': product.name,
                    'sku': f"{product.reference_sequence or uuid.uuid4()}",
                    'variant_image_url': product.images.first().product_image_url if product.images.first() else None,
                    'created_by': user.name,
                    'updated_by': user.name
                }
                variant = create_variant(variant_values, [])
                create_log(
                    model='ProductVariant',
                    company=product.company,
                    action='Imported using Excel',
                    created_by=user.name,
                    model_id=product.id
                )
                variant.warehouses.set([warehouse])
                WarehouseVariant.objects.create(warehouse=warehouse, variant=variant, physical_quantity=0, virtual_quantity=0, reserved_quantity=0)

    return JsonResponse({'success': True, 'message': _('All products processed successfully')}, status=201)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_productvariant'])])
@parser_classes([MultiPartParser, FormParser])
@subscription_required
def add_product_variant(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    values = json_data
    values['created_by'] = user.name
    values['updated_by'] = user.name

    
    validation_errors, foreign_key_objects = validate_product_variant(values)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
    
    values = {**values, **foreign_key_objects}
    attributes = values.pop('attributes', [])
    warehouses = values.get('warehouses', [])

    product = values['product']
    attribute_ids = set(attr.id for attr in attributes)
    duplicate_variants = check_duplicate_variants(product, attribute_ids);
    if duplicate_variants:
        raise get_error_response('PRODUCT_502', {'product_name': product.name})

    variant = create_variant(values, attributes)
    serializer = ProductVariantSerializer(variant)

    for warehouse in warehouses:
        WarehouseVariant.objects.create(warehouse=warehouse, variant=variant, physical_quantity=0, virtual_quantity=0, reserved_quantity=0)

    return JsonResponse({'success': True, 'variant': serializer.data}, status=201)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_productvariant'])])
def get_variants(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    result = get_records('ProductVariant', json_data, user, company_filter=Q(product__company=user.company))
    return JsonResponse(result, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_attributes_values(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    result = get_records('AttributeValue', json_data, user, company_filter=Q(company=user.company))
    return JsonResponse(result, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_attributes(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    result = get_records('Attribute', json_data, None)
    return JsonResponse(result, status=200)
    
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_productvariant'])])
def update_product_variant(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    values = json_data
    values['updated_by'] = user.name

    validation_errors, foreign_key_objects = validate_product_variant(values)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
    
    values.pop('attributes', None)
    values = {**values, **foreign_key_objects}
    attributes = values.pop('attributes', [])

    variant = update_variant_util(values, attributes)
    serializer = ProductVariantSerializer(variant)
    return JsonResponse({'success': True, 'variant': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_attributevalue'])])
def add_attributes_value(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    attribute = json_data.get('attribute')
    values = json_data.get('values', [])
    company = user.company

    if not attribute or not values:
        raise get_error_response('GENERAL_001', {'fields': 'attribute, values'})
    
    attribute_values = []
    for value in values:
        if isinstance(value, dict):
            new_value = value.get('value')
        else:
            new_value = value
        attribute_value = AttributeValue.objects.create(attribute_id=attribute, value=new_value, company=company)
        attribute_values.append(attribute_value)
    serializer = AttributeValueSerializer(attribute_values, many=True)
    return JsonResponse({'success': True, 'attribute_value': serializer.data}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_attributevalue'])])
def update_attributes_value(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    attribute = json_data.get('attribute')
    values = json_data.get('values', [])

    if not attribute or not values:
        raise get_error_response('GENERAL_001', {'fields': 'attribute, values'})

    attribute_values = []
    attribute_value_ids = []

    for value in values:
        if isinstance(value, dict):
            if value.get('id', None):
                attribute_value, _ = AttributeValue.objects.update_or_create(
                    id=value.get('id'), 
                    defaults={'value': value.get('value'), 'company': user.company}
                )
            else:
                new_value = value.get('value')
                attribute_value = AttributeValue.objects.create(
                    attribute_id=attribute, 
                    value=new_value, 
                    company=user.company
                )
            attribute_values.append(attribute_value)
            attribute_value_ids.append(attribute_value.id)

    AttributeValue.objects.filter(
        company=user.company,
        attribute_id=attribute
    ).exclude(id__in=attribute_value_ids).delete()

    serializer = AttributeValueSerializer(attribute_values, many=True)
    return JsonResponse({'success': True, 'attribute_value': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_productvariant'])])
def create_bulk_variants(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    attributes = json_data.get('attributes')
    products = json_data.get('products')

    if not attributes:
        raise get_error_response('GENERAL_001', {'fields': 'attributes'})

    for product_id in products:
        raw_product_variant = {
            'created_by': user.name,
            'updated_by': user.name,
            'product': product_id,
            'attributes': attributes,
        }

        validation_errors, foreign_key_objects = validate_product_variant(raw_product_variant)
        if validation_errors:
            raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
        
        product_variant = {**raw_product_variant, **foreign_key_objects}
        product_variant['name'] = product_variant['product'].name
        product_variant['variant_image_url'] = product_variant['product'].images.first().product_image_url if product_variant['product'].images.first() else ''
        product_variant_attributes = product_variant.pop('attributes')
        variant = create_variant(product_variant, product_variant_attributes)

        company_conf = CompanyConf.objects.get(company=user.company)
        default_warehouse = company_conf.default_warehouse
        WarehouseVariant.objects.create(warehouse=default_warehouse, variant=variant, physical_quantity=0, virtual_quantity=0, reserved_quantity=0)

    return JsonResponse({'success': True}, status=200)
    
@csrf_exempt
def get_tags(request):
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    user = get_user_from_token(request)
    if not user:
        return JsonResponse({'success': False, 'error': 'Authentication failed'}, status=401)
    

    fields_to_include = [
        'id',
        'name'
    ]

    try:
        # Filter products by company and use select_related to optimize queries
        queryset = Tags.objects.filter(company = user.company)
    except:
        return JsonResponse({'success': False, 'error': 'Tags Not found'}, status=400)
    
    # Apply search and filter logic
    tags = search_filter_group(queryset, json_data, fields_to_include, related_fields = None)
    tags = object_to_json(tags, [], {})

    response_data = {
    'success': True,
    'tags': tags,
    'data_count': len(tags)
    }
    
    return JsonResponse(response_data, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['import_excel_products'])])
@subscription_required
def create_dummy_products(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    images = json_data.get('images', [])
    category = json_data.get('category')
    
    warehouse_id =json_data.get('warehouse_id','')
    if not warehouse_id:
        raise get_error_response('GENERAL_003', {'fields': ['warehouse_id']})
    warehouse_filter = Q(id=warehouse_id) &(
        Q(
            warehouse_connection__connection_status__iexact="connected",
            warehouse_connection__company_id=user.company
        ) |
        Q(
            reseller__isnull=True,
            company_id=user.company
        )
    )
    
    warehouse = Warehouse.objects.get(warehouse_filter)
    if not warehouse:
        raise get_error_response('GENERAL_002', {'model': 'Warehouse', 'field': 'id', 'value': warehouse_id})
    
    for image in images:
        product = Product.objects.create(name='dummyProduct', company=user.company, category_id=category)
        image = ProductsImage.objects.create(product=product, product_image_url=image)

        variant_values = {
            'product': product,
            'name': product.name,
            'sku': f"{product.reference_sequence or 'SKU-DEFAULT'}",
            'variant_image_url': image.product_image_url if image else None,
            'created_by': product.created_by,
            'updated_by': product.updated_by
        }
        variant = create_variant(variant_values, [])
        variant.warehouses.set([warehouse])
        WarehouseVariant.objects.create(warehouse=warehouse, variant=variant, physical_quantity=0, virtual_quantity=0, reserved_quantity=0)

    return JsonResponse({'success':True, 'message': _('Products created successfully')})