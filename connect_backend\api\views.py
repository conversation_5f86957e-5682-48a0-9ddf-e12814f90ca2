import os
from django.conf import settings
from django.http import FileResponse, Http404, JsonResponse

def serve_media(request, path):
    media_file_path = os.path.join(settings.MEDIA_ROOT, path)
    if os.path.exists(media_file_path):
        return FileResponse(open(media_file_path, 'rb'))
    else:
        raise Http404("File does not exist")
    
def handle_404(request):
    return JsonResponse({'success': False, 'message': f'{request.path} not found'}, status=404)