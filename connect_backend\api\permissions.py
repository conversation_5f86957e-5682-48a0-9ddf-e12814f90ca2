from api.reseller.models import Reseller
from rest_framework.permissions import BasePermission

def get_permission_class(required_permissions):
    class DynamicHasGroupPermission(HasGroupPermission):
        def __init__(self):
            super().__init__(required_permissions)
    return DynamicHasGroupPermission

class HasGroupPermission(BasePermission):
    def __init__(self, required_permissions):
        self.required_permissions = required_permissions

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        if isinstance(request.user, Reseller):
            return True
        user_groups = request.user.groups.all()
        for group in user_groups:
            if group.permissions.filter(codename__in=self.required_permissions).exists():
                return True

        return False
    

class HasRolePermission(BasePermission):
    def __init__(self, required_roles):
        self.required_roles = required_roles

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        user_groups = request.user.groups.values_list('name', flat=True)
        return any(role in user_groups for role in self.required_roles)

def has_role(required_roles):
    class DynamicRolePermission(BasePermission):
        def has_permission(self, request, view):
            if not request.user.is_authenticated:
                return False
            user_groups = request.user.groups.values_list('name', flat=True)
            return any(role in user_groups for role in required_roles)

    return DynamicRolePermission

class IsSuperUser(BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.is_superuser

class HasOneOfPermissions(BasePermission):
    def __init__(self, permissions=None):
        self.permissions = permissions or []

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        if isinstance(request.user, Reseller):
            return True
        user_groups = request.user.groups.all()
        for group in user_groups:
            if group.permissions.filter(codename__in=self.permissions).exists():
                return True
        return False

def has_one_of_permissions(permissions):
    class DynamicPermission(HasOneOfPermissions):
        def __init__(self):
            super().__init__(permissions)
    return DynamicPermission
