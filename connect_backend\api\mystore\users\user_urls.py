from django.urls import path
from django.urls import include
from .user_apis import *


urlpatterns = [
    path('otp', generate_and_send_otp, name='generate_and_send_otp'),
    path('verify_otp', verify_otp, name='verify_otp'),
    path('create_user', create_store_user, name='create_user_store'),
    path('token', get_token, name='get_token'),
    path('token_extend', extend_token_validity ,name='token_extend'),
    path('reset_password_otp', reset_password_otp, name='reset_password_otp'),
    path('reset_password', reset_password, name='reset_password'),
    path('store_user_info', get_store_user_info, name='store_user_info'),
    path('update_store_user', update_store_user, name='update_store_user'),
    path('add_favorite', add_user_favorite, name='add_user_favorite'),
    path('get_favorite', get_user_favorites, name="get_user_favorites"),
    path('delete_favorite', delete_user_favorite, name='delete_user_favorite'),
    path('add_billing_address', add_store_billing_address, name='add_store_billing_address'),
    path('get_billing_address', get_store_billing_address, name='get_store_billing_address'),
    path('update_billing_address', update_store_billing_address, name='update_store_billing_address'),
    path('delete_billing_address', delete_store_billing_address, name='delete_store_billing_address'),



]