import stripe
from ..util_functions import *
from .billing_model import *
from django.db.models import Sum
import calendar
from decimal import Decimal
from dateutil.relativedelta import relativedelta
from stripe.error import StripeError, InvalidRequestError, AuthenticationError, PermissionError, APIConnectionError

stripe.api_key = settings.STRIPE_TEST_SECRET_KEY

def create_package(values):
    package = Package.objects.create(**values)
    log = create_log('Package', None, 'create', values['created_by'], package.id)
    fields = ['name', 'description', 'no_of_orders', 'price_monthly', 'price_yearly', 'is_active', 'tier', 'stripe_product_id']
    log_tracked_fields(log, values, fields)
    delete_log_if_no_changes(log)
    return package

def update_package_util(values):
    package = Package.objects.filter(id=values['id']).first()
    if not package:
        return None
    log = create_log('Package', None, 'update', values['updated_by'], package.id)
    update_package_fields(package, values, log)
    delete_log_if_no_changes(log)
    package.save()
    return package

def update_package_fields(package, values, log):
    fields = ['name', 'description', 'no_of_orders', 'price_monthly', 'price_yearly', 'is_active', 'tier', 'stripe_product_id']
    for key, value in values.items():
        if hasattr(package, key):
            old_value = getattr(package, key)
            if old_value != value:
                log_and_update_field(package, key, value, old_value, log, key in fields)

def create_or_update_stripe_product(product_id, product_name, product_description, price_data, recurring_interval, package):
    price = price_data['month_price_amount'] if recurring_interval == 'month' else \
            (price_data['year_price_amount'] if recurring_interval == 'year' else 0)

    return StripeProduct.objects.update_or_create(
        stripe_product_id=product_id,
        recurring_interval=recurring_interval,
        package=package,
        defaults={
            'name': product_name,
            'description': product_description,
            'price': price,
            'price_id': price_data[f'{recurring_interval}_price_id'],
            'currency': 'usd'
        }
    )

def create_stripe_product(name, description):
    try:
        return stripe.Product.create(
            name=name,
            description=description,
            active=True,
            metadata={
                'is_connect': True
            }
        )
    except stripe.error.StripeError as e:
        raise Exception(f"Failed to create Stripe product: {e}")
    
def create_stripe_price(product_id, amount, currency='usd', recurring_interval=None):
    try:
        if recurring_interval:
            return stripe.Price.create(
                product=product_id,
                unit_amount=int(amount * 100),
                currency=currency,
                recurring={'interval': recurring_interval},
                metadata={
                    'is_connect': True
                }
            )
        else:
            raise stripe.error.StripeError("Price couldn't be created")
    except stripe.error.StripeError as e:
        raise Exception(f"Failed to create Stripe price: {e}")

def deactivate_old_prices(existing_prices, price_data):
    try:
        for price in existing_prices.auto_paging_iter():
            if price.recurring:
                if price.recurring['interval'] in price_data:
                    if price_data[price.recurring['interval']] is not None:
                        stripe.Price.modify(price.id, active=False)
    except stripe.error.StripeError as e:
        raise Exception(f"Failed to deactivate old prices: {e}")

def update_or_create_stripe_product(package, product, price_ids):
    try:
        for interval, price_id in price_ids.items():
            StripeProduct.objects.update_or_create(
                package=package,
                recurring_interval=interval,
                defaults={
                    'price': (package.price_monthly if interval == 'month' else
                              (package.price_yearly if interval == 'year' else 0)),
                    'price_id': price_id,
                    'currency': 'usd'
                }
            )
    except Exception as e:
        raise Exception(f"Failed to update or create Stripe product record: {e}")

def create_stripe_product_and_prices(package):
    try:
        product = create_stripe_product(
            name=package.name,
            description=package.description
        )

        package.stripe_product_id = product.id
        package.save()

        price_ids = {}
        if package.price_monthly:
            monthly_price = create_stripe_price(
                product.id,
                package.price_monthly,
                recurring_interval='month'
            )
            price_ids['month'] = monthly_price.id

        if package.price_yearly:
            yearly_price = create_stripe_price(
                product.id,
                package.price_yearly,
                recurring_interval='year'
            )
            price_ids['year'] = yearly_price.id

        update_or_create_stripe_product(package, product, price_ids)

        return {'success': True, 'stripe_product_id': product.id}
    except Exception as e:
        return {'success': False, 'error': str(e)}
    
def update_stripe_product_and_prices(package):
    try:
        stripe_product = StripeProduct.objects.filter(package=package).first()
        if not stripe_product:
            raise Exception('Stripe product not found')

        price = stripe.Price.retrieve(stripe_product.price_id)
        product_id = price['product']
        product = stripe.Product.modify(
            product_id,
            name=package.name,
            description=package.description
        )

        package.stripe_product_id = product.id
        package.save()

        existing_prices = stripe.Price.list(product=product.id)

        price_data = {
            'month': package.price_monthly if package.price_monthly != 0.0 else None,
            'year': package.price_yearly if package.price_yearly != 0.0 else None,
        }

        deactivate_old_prices(existing_prices, price_data)

        price_ids = {}
        for interval, amount in price_data.items():
            if amount is not None:
                stripe_price = create_stripe_price(
                    product.id,
                    amount,
                    recurring_interval='month' if interval == 'month' else
                                         ('year' if interval == 'year' else None)
                )
                price_ids[interval] = stripe_price.id

        update_or_create_stripe_product(package, product, price_ids)

        return {'success': True, 'stripe_product_id': product.id}
    except Exception as e:
        raise Exception(str(e))

def get_current_subscription(company):
    company_plan = CompanyPlan.objects.filter(company=company).last()
    subscription = Subscription.objects.filter(company_plan=company_plan, company=company).last()
    if subscription and subscription.end_date < timezone.now():
        subscription.activate_freemium()
        subscription.save()

    return subscription

def get_remaining_orders_count(company):
    no_of_orders = 0
    subscription = get_current_subscription(company)

    if subscription:
        no_of_orders = get_orders_limit(subscription)
        count = get_orders_created_in_current_plan(company, subscription)
        no_of_orders -= count

    return no_of_orders

def get_orders_limit(subscription):
    start_date = subscription.start_date
    end_date = subscription.end_date + timedelta(days=1) - timedelta(seconds=1)

    delta = end_date - start_date
    
    if delta.days >= 365:
        start_date, end_date = get_current_month_date_range(start_date)

    no_of_orders = subscription.order_limit

    return no_of_orders

def get_orders_created_in_current_plan(company, subscription):
    start_date = subscription.start_date
    end_date = subscription.end_date + timedelta(days=1)

    delta = end_date - start_date
    
    if delta.days >= 365:
        start_date, end_date = get_current_month_date_range(start_date)
    
    count = Order.objects.filter(
        company=company,
        created_at__range=[start_date, end_date]
    ).count()
    return count

def get_discount_month(subscription, new_monthly_price):
    start_date = subscription.start_date
    now = timezone.now().date()

    if start_date > now:
        return new_monthly_price
    next_month = (now + relativedelta(months=1)).replace(day=1)
    total_days_in_month = (next_month - now.replace(day=1)).days
    delta = now - start_date
    days_remaining = total_days_in_month - delta.days

    if days_remaining <= 0:
        return new_monthly_price
    
    percentage_remaining = days_remaining / total_days_in_month
    prorated_cost = percentage_remaining * new_monthly_price

    return prorated_cost

def get_current_month_discount(subscription_start_date, new_monthly_price, old_monthly_price):
    now = timezone.now().date()
    cycle_start_date, cycle_end_date = get_current_month_date_range(subscription_start_date)
    if now < cycle_start_date:
        return 0
    total_days_in_cycle = (cycle_end_date - cycle_start_date).days
    days_passed_in_cycle = (now - cycle_start_date).days
    days_remaining_in_cycle = total_days_in_cycle - days_passed_in_cycle
    if days_remaining_in_cycle <= 0:
        return 0
    
    price_difference = new_monthly_price - old_monthly_price
    percentage_remaining = days_remaining_in_cycle / total_days_in_cycle
    prorated_current_month_discount = percentage_remaining * price_difference
    return round(prorated_current_month_discount, 2)

def get_prorated_upgrade_cost(subscription, new_yearly_price, old_yearly_price):
    start_date = timezone.now().date()
    end_date = subscription.end_date

    if end_date <= start_date:
        return 0

    days_remaining = (end_date - start_date).days

    months_remaining = days_remaining // 30

    if months_remaining == 0:
        return 0
    
    price_difference_per_month = (new_yearly_price - old_yearly_price)/12
    prorated_remaining_months_cost = price_difference_per_month * months_remaining
    prorated_current_month_discount = get_current_month_discount(subscription.start_date, new_yearly_price/12, old_yearly_price/12)
    total_prorated_cost = prorated_current_month_discount + prorated_remaining_months_cost

    return round(total_prorated_cost, 2)

def get_package(package_id):
    package = Package.objects.get(id=package_id)
    return package

def create_or_get_stripe_customer(company):
    if not company.stripe_customer_id:
        stripe_customer = stripe.Customer.create(
            name=company.company_name,
            phone=company.company_mobile,
            description=f"{company.company_name} - {company.company_mobile}",
            metadata={
                'is_connect': True
            }
        )
        company.stripe_customer_id = stripe_customer.id
        company.save()
    return company.stripe_customer_id

def build_line_items(recurring_interval, package):
    line_items = []
    price = 0

    if recurring_interval == 'month':
        price=package.price_monthly
    elif recurring_interval == 'year':
        price=package.price_yearly

    line_items.append({
        'price_data': {
            'unit_amount': int(price * 100),
            'currency': 'usd',
            'product_data': {'name': f'{package.name}'},
            'recurring': {
                'interval': recurring_interval
            }
        },
        'quantity': 1
    })

    return line_items

def create_stripe_checkout_session(company, line_items, recurring_interval, success_url, cancel_url, package):
    mode ='subscription'
    try:
        session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=line_items,
            mode=mode,
            success_url=success_url,
            cancel_url=cancel_url,
            customer=company.stripe_customer_id,
            metadata={
                'package_id': str(package.id),
                'recurring': recurring_interval,
                'is_connect': True
            },
        )
        return session
    except Exception as e:
        raise e
    
def handle_checkout_session_completed(data, billing_log):
    customer_id = data.get('customer')
    billing_log.process += f'Checkout Session Completed - Customer ID: {customer_id}\n'
    billing_log.save()

    try:
        company = Company.objects.get(stripe_customer_id=customer_id)
        billing_log.process += f'Company found: {company.company_name}\n'
        billing_log.company = company
    except Company.DoesNotExist:
        billing_log.process += f'Company not found for customer ID: {customer_id}\n'
        billing_log.save()
        raise Exception(f'Company not found for {customer_id}')

    is_client_payment = data.get('metadata', {}).get('is_client_payment', False)
    if is_client_payment:
        customer_name = data.get('customer_details', {}).get('customer_name')
        order_id = data.get('metadata', {}).get('order_id', False)
        amount = data.get('amount_total')
        billing_log.process += f'Client payment detected - Order ID: {order_id}, Amount: {amount}, Customer: {customer_name}\n'
        billing_log.save()
        handle_client_payment(order_id, amount, customer_name=customer_name, provider='Stripe')
        return f'Payment link (End Client) - Order {order_id} - Company {company.company_name}'

    is_limit_upgrade = data.get('metadata', {}).get('is_limit_upgrade', False)
    if is_limit_upgrade:
        billing_log.process += 'Limit upgrade detected\n'
        billing_log.save()
        create_payment(data, company)
        no_of_orders = handle_limit_upgrade_response(data, company, billing_log)
        billing_log.process += f'Limit upgraded - New limit: {int(no_of_orders)}\n'
        billing_log.save()
        return f'Limit Uprade (Connect Business) - Limit {int(no_of_orders)} - Company {company.company_name}'

    billing_log.process += 'Processing subscription response\n'
    billing_log.save()
    subscription = handle_subscription_response(data, company, billing_log)
    billing_log.process += f'Subscription created - Package: {subscription.company_plan.package.name}\n'
    billing_log.save()

    try:
        reseller_company = ResellerCompany.objects.get(company=company)
        wallet = ResellerWallet.objects.get(reseller=reseller_company.reseller)
        wallet.credit = float(wallet.credit) + float(((data.get('amount_total')/100) * 0.2))
        wallet.save()
    except ResellerCompany.DoesNotExist:
        pass
    except ResellerWallet.DoesNotExist:
        pass
    
    return f'Subscription (Conncet Business) - Subscription {subscription.company_plan.package.name} -  Company {company.company_name}'

def handle_price_created(data, billing_log):
    billing_log.process += f'Price Created Event - Price ID: {data["id"]}\n'
    billing_log.save()

    last_stripe_product = StripeProduct.objects.filter(price_id=data['id']).last()
    package = last_stripe_product.package
    try:
        product = StripeProduct.objects.create(
            price_id=data['id'],
            recurring_interval=data['recurring']['interval'] if data['recurring'] and data['recurring'].get('interval') else None,
            currency=data['currency'],
            price=data['unit_amount'] / 100 if data.get('unit_amount') else None,
            package=package
        )
        billing_log.process += f'Price created - Package: {product.package.name}, Price: {product.price}{product.currency}\n'
        billing_log.save()
        return f'Price Created (Stripe Products) - StripeProduct {product.package.name} -  Price {product.price}{product.currency}'
    except Exception as e:
        billing_log.process += f'Failed to create price for package {package.name}\n'
        billing_log.save()
        raise Exception(f'Failed to create price for {last_stripe_product.package.name}')

def handle_price_deleted(data, billing_log):
    billing_log.process += f'Price Deleted Event - Price ID: {data["id"]}\n'
    billing_log.save()

    stripe_product = StripeProduct.objects.get(price_id=data['id'])
    handled_action = f'Price Deletd (Stripe Products) - StripeProduct {stripe_product.package.name} -  Price {stripe_product.price}{stripe_product.currency}'
    stripe_product.delete()

    billing_log.process += f'Price deleted - Package: {stripe_product.package.name}, Price: {stripe_product.price}{stripe_product.currency}\n'
    billing_log.save()
    return handled_action

def handle_price_updated(data, billing_log):
    billing_log.process += f'Price Updated Event - Price ID: {data["id"]}\n'
    billing_log.save()

    stripe_product = StripeProduct.objects.get(price_id=data['id'])
    stripe_product.price = data.get('unit_amount') / 100 if data.get('unit_amount') else stripe_product.price
    stripe_product.save()

    billing_log.process += f'Price updated - Package: {stripe_product.package.name}, New Price: {stripe_product.price}{stripe_product.currency}\n'
    billing_log.save()
    return f'Price Deletd (Stripe Products) - StripeProduct {stripe_product.package.name} -  Price {stripe_product.price}{stripe_product.currency}'

def create_payment(data, company):
    amount = data.get('amount_total', None)
    if not amount:
        amount = data.get('amount', 0)
    try:
        return Payment.objects.create(
            stripe_payment_id=data['id'],
            amount=amount / 100,
            currency=data['currency'],
            created_at=timezone.now(),
            company=company,
            customer_email=data.get('customer_details', {}).get('email'),
            customer_name=data.get('customer_details', {}).get('name')
        )
    except Exception as e:
        print(f"Error creating payment: {e}")
        raise

def create_new_subscription(company, package, recurring, subscription_id):
    try:
        end_date = (timezone.now() + timezone.timedelta(days=365)) if recurring == 'year' else (timezone.now() + timezone.timedelta(days=30))
        latest_subscription = Subscription.objects.filter(end_date__gte=timezone.now(), company=company).last()
        if latest_subscription:
            latest_subscription.active = False
            latest_subscription.save()

        if CompanyPlan.objects.filter(company=company).count() > 1:
            CompanyPlan.objects.filter(company=company).delete()

        company_plan, _ = CompanyPlan.objects.get_or_create(company=company, defaults={'package': package})
        company_plan.package = package
        company_plan.save()

        return Subscription.objects.create(
            company_plan=company_plan,
            order_limit=package.no_of_orders,
            stripe_subscription_id=subscription_id,
            start_date=timezone.now(),
            recurring_interval=recurring,
            end_date=end_date,
            active=True,
            company=company
        )
    except Exception as e:
        raise Exception(f"Error creating new subscription: {e}")

def cancel_subscription_util(company, billing_log):
    stripe_customer_id = company.stripe_customer_id
    billing_log.process += f'Attempting to cancel subscriptions for customer {stripe_customer_id}\n'
    billing_log.save()

    try:
        subscriptions = stripe.Subscription.list(customer=stripe_customer_id).data
        billing_log.process += f'Retrieved {len(subscriptions)} subscriptions for customer {stripe_customer_id}\n'
        billing_log.save()
    except StripeError as e:
        billing_log.process += f'Failed to retrieve subscriptions: {str(e)}\n'
        billing_log.success = False
        billing_log.save()
        return

    subscriptions.sort(key=lambda sub: sub['created'])
    subscriptions_to_cancel = subscriptions[:-1]

    for subscription in subscriptions_to_cancel:
        subscription_id = subscription.get("id")
        if subscription_id:
            try:
                stripe.Subscription.cancel(subscription_id)
                billing_log.process += f'Cancelled subscription: {subscription_id}\n'
                billing_log.save()
            except (InvalidRequestError, AuthenticationError, PermissionError, APIConnectionError) as e:
                billing_log.process += f'Error cancelling subscription {subscription_id}: {e.user_message or str(e)}\n'
                billing_log.success = False
                billing_log.save()
                expire_active_sessions(stripe_customer_id, billing_log)
            except Exception as e:
                billing_log.process += f'Unexpected error while cancelling subscription {subscription_id}: {str(e)}\n'
                billing_log.success = False
                billing_log.save()

    billing_log.success = True if subscriptions_to_cancel else False
    billing_log.process += f'Completed cancellation process for customer {stripe_customer_id}. Subscriptions cancelled: {len(subscriptions_to_cancel)}\n'
    billing_log.save()

def handle_invoice_payment_succeeded(data, billing_log):
    customer_id = data.get('customer')
    subscription_id = data.get('subscription')
    billing_log.process += f'Invoice Payment Succeeded - Customer ID: {customer_id}, Subscription ID: {subscription_id}\n'
    billing_log.save()

    try:
        company = Company.objects.get(stripe_customer_id=customer_id)
        billing_log.process += f'Company found: {company.company_name}\n'
        billing_log.company = company
    except Company.DoesNotExist:
        billing_log.process += f'Company not found for customer ID: {customer_id}\n'
        billing_log.save()
        raise Exception(f'Company not found for {customer_id}')

    subscription = Subscription.objects.filter(company=company, stripe_subscription_id=subscription_id).last()
    if not subscription:
        billing_log.process += f'No active subscription found for company ID: {company.company_id}\n'
        billing_log.save()
        raise Exception(f"No active subscription found for company_id {company.company_id}")

    recurring = subscription.recurring_interval
    billing_log.process += f'Creating payment and processing new subscription - Recurring: {recurring}\n'
    billing_log.save()
    with transaction.atomic():
        create_payment(data, company)
        subscription = process_new_subscription(subscription, recurring)

    billing_log.process += f'Subscription processed - Package: {subscription.company_plan.package.name}\n'
    billing_log.save()
    return f'Auto Billing (Conncet Business) - Subscription {subscription.company_plan.package.name} -  Company {company.company_name}'

def process_new_subscription(subscription, recurring):
    start_date, end_date = get_next_subscription_dates(interval=recurring)
    subscription.start_date = start_date
    subscription.end_date = end_date
    subscription.is_freemium = False
    if subscription.company_plan and subscription.company_plan.package:
        subscription.order_limit = subscription.company_plan.package.no_of_orders
    subscription.save()
    return subscription

def get_next_subscription_dates(current_end_date=None, interval="month"):
    if current_end_date and current_end_date > timezone.now():
        start_date = current_end_date
    else:
        start_date = timezone.now()

    if interval == "month":
        end_date = start_date + relativedelta(months=1)
    elif interval == "year":
        end_date = start_date + relativedelta(years=1)
    else:
        raise ValueError("Unsupported interval: must be 'month' or 'year'")

    return start_date, end_date

def handle_limit_upgrade(company, limit_upgrade):
    success_url, cancel_url = get_webhook_urls()
    limit_upgrade = LimitUpgrade.objects.get(id=limit_upgrade)
    meta_data = {
        "no_of_orders": limit_upgrade.no_of_orders,
        "is_limit_upgrade": True,
        'is_connect': True
    }
    session = stripe.checkout.Session.create(
        payment_method_types=['card'],
        line_items=[
            {
                'price_data': {
                    'currency': 'usd',
                    'product_data': {
                        'name': f'Limit Upgrade {limit_upgrade.no_of_orders}',
                    },
                    'unit_amount': int(limit_upgrade.price * 100),
                },
                'quantity': 1,
            },
        ],
        mode='payment',
        success_url=success_url,
        cancel_url=cancel_url,
        customer=company.stripe_customer_id,
        metadata=meta_data,
    )
    return session

def get_webhook_urls():
    request = get_current_request()
    lang = request.headers.get('accept-language', 'ar')
    FRONT_BASEURL = settings.FRONT_BASEURL
    success_url = f'{FRONT_BASEURL}/{lang}/payment-success/'
    cancel_url = f'{FRONT_BASEURL}/{lang}/payment-cancel/'
    return success_url, cancel_url

def handle_subscription(company, package_id, recurring_interval):
    success_url, cancel_url = get_webhook_urls()
    package = get_package(package_id)
    create_or_get_stripe_customer(company)
    line_items = build_line_items(recurring_interval, package)
    session = create_stripe_checkout_session(company, line_items, recurring_interval, success_url, cancel_url, package)
    return session

def expire_active_sessions(customer_id, billing_log):
    expired_count = 0
    billing_log.process += f'Attempting to expire sessions for customer {customer_id}\n'
    billing_log.save()

    try:
        sessions = stripe.checkout.Session.list(customer=customer_id, limit=100)
        billing_log.process += f'Fetched {len(sessions.data)} sessions for customer {customer_id}\n'
        billing_log.save()
    except StripeError as e:
        billing_log.process += f'Failed to list sessions: {str(e)}\n'
        billing_log.success = False
        billing_log.save()
        return

    for session in sessions.auto_paging_iter():
        if session.get("status") == "open":
            try:
                stripe.checkout.Session.expire(session.id)
                billing_log.process += f'Expired session {session.id}\n'
                billing_log.save()
                expired_count += 1
            except InvalidRequestError as e:
                billing_log.process += f'Failed to expire session {session.id}: {e.user_message or str(e)}\n'
                billing_log.success = False
                billing_log.save()

    if expired_count == 0:
        billing_log.process += f'No active sessions found for customer {customer_id}.\n'
        billing_log.success = True
        billing_log.save()
    else:
        billing_log.process += f'Expired {expired_count} session(s) for customer {customer_id}.\n'
        billing_log.success = True
        billing_log.save()

def handle_limit_upgrade_response(data, company, billing_log):
    no_of_orders = data.get('metadata', {}).get('no_of_orders', False)
    billing_log.process += f'Limit Upgrade Response - No. of Orders: {no_of_orders}\n'
    billing_log.save()

    try:
        subscription = Subscription.objects.filter(end_date__gte=timezone.now(), company=company).last()
        subscription.order_limit += int(no_of_orders)
        subscription.save()
        billing_log.process += f'Subscription updated - New Order Limit: {subscription.order_limit}\n'
        billing_log.success = True
        billing_log.save()
        return no_of_orders
    except Exception as e:
        billing_log.process += f'Error updating subscription limit - {str(e)}\n'
        billing_log.success = False
        billing_log.save()
        raise

def handle_subscription_response(data, company, billing_log):
    package_id = data.get('metadata', {}).get('package_id')
    recurring = data.get('metadata', {}).get('recurring')

    billing_log.process += f'Subscription Response - Package ID: {package_id}, Recurring: {recurring}\n'
    billing_log.save()

    try:
        package = get_package(package_id)
    except Package.DoesNotExist:
        billing_log.process += f'Package not found for id {package_id}\n'
        billing_log.success = False
        billing_log.save()
        raise Exception(f'Package not found for id {package_id}')

    with transaction.atomic():
        create_payment(data, company)
        try:
            cancel_subscription_util(company, billing_log)
            subscription_id = data.get('subscription')
            subscription = create_new_subscription(company, package, recurring, subscription_id)

            billing_log.process += f'Subscription created - Package: {package.name}, Subscription ID: {subscription_id}\n'
            billing_log.success = True
            billing_log.save()
            return subscription
        except Exception as e:
            billing_log.process += f'Error updating subscription - {str(e)}\n'
            billing_log.success = False
            billing_log.save()
            raise Exception(f'Error updating subscription - Error: {str(e)}')
