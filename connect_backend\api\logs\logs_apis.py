from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json
from ..util_functions import *
from ..orders.order_model import Order
from ..pricelists.pricelist_model import *
from ..orders.order_utils import *
from rest_framework.permissions import AllowAny
from rest_framework.decorators import *
from .logs_utils import *
from django.utils.translation import gettext as _

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CombinedJWTAuthentication])
@permission_classes([AllowAny])
def get_logs(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    model = json_data.get('model')
    id = json_data.get('id')

    logs = Logs.objects.filter(model=model, model_id=id).order_by('-id')
    serializer = LogsSerializer(logs, many=True)
    return JsonResponse({'success': True, 'logs': serializer.data}, status=200)
    
@csrf_exempt
def set_tracked_fields(request):
    if request.method == 'POST':
        try:
            json_data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'Invalid JSON format in request body'}, status=400)

        user = get_user_from_token(request)
        if user is None:
            return JsonResponse({'success': False, 'message': 'Authentication failed'}, status=401)

        company = user.company
        fields = json_data['fields']
        model = json_data['model']

        try:
            tracked_fields = TrackedFields.objects.filter(company=company, model=model)
            tracked_fields.delete()

            for field in fields:
                tracked_field = TrackedFields(company=company, field_name=field, model=model)
                tracked_field.save()
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)

        return JsonResponse({'success': True, 'message':'set Tracked Fields successfully'}, status=200)
    else:
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)
    
@csrf_exempt
def get_tracked_fields(request):
    if request.method == 'POST':
        try:
            json_data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'Invalid JSON format in request body'}, status=400)
        
        user = get_user_from_token(request)
        if user is None:
            return JsonResponse({'success': False, 'message': 'Authentication failed'}, status=401)

        company = user.company
        model = json_data['model']

        try:
            tracked_fields = TrackedFields.objects.filter(company=company, model=model)
            data = [field.field_name for field in tracked_fields]
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)

        return JsonResponse({'success': True, 'tracked_fields': data}, status=200)
    else:
        return JsonResponse({'success': False, 'error': 'Method not allowed'}, status=405)

@csrf_exempt
def get_order_fields(request):
    if request.method == 'GET':
        user = get_user_from_token(request)
        if user is None:
            return JsonResponse({'success': False, 'message': 'Authentication failed'}, status=401)

        try:
            fields = [field.name for field in Order._meta.fields]
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)

        return JsonResponse({'success': True, 'fields': fields}, status=200)
    else:
        return JsonResponse({'success': False, 'Message': 'Method not allowed'}, status=405)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def reset_tracked_fields(request):
    company = None
    try:
        user = User.objects.get(user=request.user)
        company = user.company
    except User.DoesNotExist:
        company = None
    
    TrackedFields.objects.filter(company=company).delete()
    set_default_tracked_fields(company)
    if company is None:
        set_area_tracked_fields(company)
        set_sub_area_tracked_fields(company)
        set_status_map_tracked_fields(company)
    return JsonResponse({'success': True, 'message': 'Default tracked fields have been reset successfully'}, status=200)