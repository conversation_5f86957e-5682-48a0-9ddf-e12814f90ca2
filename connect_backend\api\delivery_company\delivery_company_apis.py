from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.db.models import Q
from ..users.user_model import User
from ..util_functions import *
from rest_framework.decorators import *
from ..permissions import *
from ..error.error_model import *

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_deliverycompany'])])
@csrf_exempt
def get_delivery_companies(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    delivery_companies_ids = CompanyDeliveryCompany.objects.filter(company=user.company).values_list('delivery_company', flat=True)
    custom_filter=Q(id__in=delivery_companies_ids, is_active=True)
    if not delivery_companies_ids:
        custom_filter = Q(is_active=True)
    
    response_data = get_records('DeliveryCompany', json_data, user, custom_filter=custom_filter, context={'show_connection': True, 'company': user.company})
    return JsonResponse(response_data, status=200)
