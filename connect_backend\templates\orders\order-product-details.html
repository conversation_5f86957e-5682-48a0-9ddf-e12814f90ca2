<!DOCTYPE html>

{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order-product-details.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}

  {% for doc in docs %}
    <div class=" page-break">

      <div class="header mb-4">
        <div class="left-header">
          <div class="fw-bold">
            {{meta.labels.CLIENT_INFO}}
          </div>
          <div>
            {{doc.order.customer_name}}
          </div>
          <div>
            {{doc.order.customer_mobile}}
          </div>
        </div>
        <div class="right-header">
          <div class="fw-bold">
            {{meta.labels.SHIPPING_ADDRESS}}
          </div>
          <div>
            {{doc.order.area.name}}
          </div>
          <div>
            {{doc.order.sub_area.name}}
          </div>
          <div>
            {{doc.order.address}}
          </div>
        </div>
      </div>
      {% if doc.order_lines and doc.order_lines|length > 0 %}
        <table class="mb-4" class="table-head">
        <tr class="table-head">
          <th>{{meta.labels.PRODUCT}}</th>
          <th>{{meta.labels.PRICE}}</th>
          <th>{{meta.labels.QUANTITY}}</th>
          <th>{{meta.labels.SUM}}</th>
        </tr>
          {% for order_line in doc.order_lines %}
            <tr>
              <td class="border">
                <div class="product-name-container">
                  <div class="product-image-container">
                    <img class="product-image" src="{{ order_line.product_variant.variant.variant_image_url }}" alt="">
                  </div>
                  {{order_line.product_variant.variant.name}}
                </div>
              </td>
              <td class="border">{{order_line.price}}</td>
              <td class="border">{{order_line.quantity}}</td>
              <td class="border">{{order_line.price * order_line.quantity}}</td>
            </tr>
          {% endfor %}
        </table>
      {% endif %}
        <table class="border">
          <tr class="space-under">
          <td rowspan="5" class="product-note border">
            {{meta.labels.NOTES}} :
            {% if doc.order.note %}
            {% else %}
            No Notes
            {% endif %}
            <span>{{doc.order.note}}</span>
          </td>
          <tr class="space-under">
            <th>{{meta.labels.PACKAGE_COST}}</th>
            <td>{{doc.order.package_cost}}
              {{doc.order.currency}}
            </td>
          </tr>
          <tr class="space-under">
            <th>{{meta.labels.DELIVERY_FEE}}</th>
            <td> + {{doc.order.delivery_fee}}
              {{doc.order.currency}}
            </td>
          </tr>
          <tr class="space-under">
            <th>{{meta.labels.COD}}</th>
            <td>{{doc.order.total_cod}}
              {{doc.order.currency}}
            </td>
          </tr>
          <tr class="space-under">
            <th>{{meta.labels.TOTAL_AMOUNT}}</th>
            <td>{{doc.order.order_due_amount}}
              {{doc.order.currency}}
            </td>
          </tr>
        </table>
      
    </div>
  {% endfor %}

</body>

</html>