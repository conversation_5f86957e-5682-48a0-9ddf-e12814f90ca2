from django.db import models
from ..models import SuperModel
from ..users.user_model import Company
from ..category.category_model import *
from ..products.product_model import *

class MyStore(SuperModel):
    name = models.CharField(max_length=20,unique=True)
    company = models.OneToOneField(Company, related_name='mystores', on_delete=models.CASCADE)
    title = models.CharField(max_length=200, null=True, blank=True)
    # ! mystore_image to be deleted after migration
    mystore_image = models.ImageField(upload_to='mystore_images/', null=True, blank=True)
    mystore_image_url = models.CharField(max_length=200, null=True, blank=True)
    primary_color = models.CharField(max_length=200, blank=True, null=True)
    secondary_color = models.CharField(max_length=200, blank=True, null=True)
    whatsapp_mobile = models.Char<PERSON>ield(max_length=200, null=True, blank=True)
    whatsapp_link = models.Char<PERSON>ield(max_length=200, blank=True, null=True)
    facebook = models.Char<PERSON>ield(max_length=200, blank=True, null=True)
    instagram = models.CharField(max_length=200, blank=True, null=True)
    email = models.CharField(max_length=200, blank=True, null=True)
    delivery_note = models.CharField(max_length=500, blank=True, null=True)
    store_template = models.CharField(
        max_length=50,
        choices=[
            ("1", "Template 1"),
            ("2", "Template 2"),
            ("3", "Template 3")
        ],
        default='1')    
    is_archived = models.BooleanField(default=False) 

    def delete(self, *args, **kwargs):
        self.is_archived = True
        self.save()
class BannerDetail(SuperModel):
    title = models.CharField(max_length=200, null=True)
    # ! banner_image to be deleted after migration
    banner_image = models.ImageField(upload_to='banner_images/', null=True, blank=True)
    banner_image_url = models.CharField(max_length=200, null=True, blank=True)
    link_url = models.CharField(max_length=200, null=True)
    link_label = models.CharField(max_length=200, null=True)
    link_color = models.CharField(max_length=200, null=True)
    active = models.BooleanField(default=True)
    mystore = models.ForeignKey(MyStore,related_name='banners_detail',on_delete=models.CASCADE,null=False)
    
class StoreSection(SuperModel):
    store = models.ForeignKey(MyStore,related_name='store_section',on_delete=models.CASCADE,null=False)
    section_description = models.CharField(max_length=100)  
    section_code = models.CharField(max_length=50, unique=True)  
    categories = models.ManyToManyField(Category, related_name="category_sections", blank=True)  # Selected categories for the section
    tags = models.ManyToManyField(Tags, related_name="tags_sections", blank=True)  
    position = models.PositiveIntegerField(default=1, blank=True)  
    active = models.BooleanField(default=True)  

    class Meta:
            unique_together = ('store', 'section_code')  
