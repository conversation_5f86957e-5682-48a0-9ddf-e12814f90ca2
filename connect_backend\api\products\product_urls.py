from django.urls import path
from .product_apis import *

urlpatterns = [
    path('add_product', add_product, name='add_product'),
    path('update_product', update_product, name='update_product'),
    path('get_products', get_products, name='get_products'),
    path('delete_product', delete_product, name='delete_product'),
    path('get_product_by_id', get_product_by_id, name='get_product_by_id'),
    path('test_import_products', test_import_products, name='test_import_products'),
    path('import_products_from_excel', import_products_from_excel, name='import_products_from_excel'),
    path('create_dummy_products', create_dummy_products, name='create_dummy_products'),
    path('add_product_variant', add_product_variant, name='add_product_variant'),
    path('get_variants', get_variants, name='get_variants'),
    path('get_attributes', get_attributes, name='get_attributes'),
    path('get_attributes_values', get_attributes_values, name='get_attributes_values'),
    path('update_product_variant', update_product_variant, name='update_product_variant'),
    path('add_attributes_values', add_attributes_value, name='add_attributes_values'),
    path('update_attributes_value', update_attributes_value, name='update_attributes_value'),
    path('create_bulk_variants', create_bulk_variants, name='create_bulk_variants'),
    path('get_store_products', get_store_products,name='get_products'),
    path('get_store_variants', get_store_variants,name='get_store_variants'),
    path('get_tags', get_tags, name='get_tags'),
]