[{"model": "api.CollectionType", "pk": 1, "fields": {"name": "Direct Cash", "code": "direct_cash"}}, {"model": "api.CollectionType", "pk": 2, "fields": {"name": "Requested Cash", "code": "requested_cash"}}, {"model": "api.CollectionStatus", "pk": 1, "fields": {"name": "Draft", "code": "draft"}}, {"model": "api.CollectionStatus", "pk": 2, "fields": {"name": "Pending", "code": "pending"}}, {"model": "api.CollectionStatus", "pk": 3, "fields": {"name": "Paid", "code": "paid"}}, {"model": "api.CollectionStatus", "pk": 4, "fields": {"name": "Confirmed", "code": "confirmed"}}, {"model": "api.CollectionStatus", "pk": 5, "fields": {"name": "Cancelled", "code": "cancelled"}}, {"model": "api.CollectionStatus", "pk": 6, "fields": {"name": "Completed", "code": "completed"}}, {"model": "api.CollectionBank", "pk": 1, "fields": {"code": "cliq", "name": "<PERSON><PERSON><PERSON>", "country": 2}}, {"model": "api.CollectionBank", "pk": 2, "fields": {"code": "arabic_bank_JO", "name": "Arabic Bank (Jordan)", "country": 2}}, {"model": "api.CollectionBank", "pk": 3, "fields": {"code": "barq", "name": "Bar<PERSON>", "country": 1}}, {"model": "api.CollectionBank", "pk": 4, "fields": {"code": "reflect", "name": "Reflect", "country": 1}}, {"model": "api.CollectionBank", "pk": 5, "fields": {"code": "bank_of_palestine", "name": "Bank of Palestine", "country": 1}}, {"model": "api.CollectionBank", "pk": 6, "fields": {"code": "arabic_bank_PS", "name": "Arabic Bank (Palestine)", "country": 1}}]