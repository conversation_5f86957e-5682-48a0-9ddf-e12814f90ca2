import subprocess
import polib
import os
import sys
import shutil
from django.core.management.base import BaseCommand

LOCALE_DIR = "locale/ar/LC_MESSAGES/django.po"
BACKUP_FILE = "locale/ar/LC_MESSAGES/django_backup.po"

class Command(BaseCommand):
    help = "Update translations while preserving order and metadata."

    def handle(self, *args, **kwargs):
        try:
            self.backup_po_file(LOCALE_DIR)
            self.run_makemessages()
            self.restore_existing_translations(LOCALE_DIR, BACKUP_FILE)
            self.clean_po_file(LOCALE_DIR)
            self.move_translated_terms_to_start(LOCALE_DIR)
            self.print_untranslated_count(LOCALE_DIR)
        except Exception as e:
            self.stderr.write(str(e))

    def backup_po_file(self, po_file_path):
        """Create a backup of the existing PO file before running makemessages."""
        if os.path.exists(po_file_path):
            shutil.copy(po_file_path, BACKUP_FILE)

    def run_makemessages(self):
        """Run makemessages to extract new translations."""
        subprocess.run([sys.executable, "manage.py", "makemessages", "-l", "ar"], check=True)

    def restore_existing_translations(self, new_po_path, backup_po_path):
        if not os.path.exists(backup_po_path):
            return

        old_po = polib.pofile(backup_po_path)
        new_po = polib.pofile(new_po_path)

        translation_map = {entry.msgid: entry for entry in old_po}

        updated_entries = []
        for entry in old_po:
            if entry.msgid in translation_map:
                old_entry = translation_map[entry.msgid]
                entry.msgstr = old_entry.msgstr
            updated_entries.append(entry)

        new_entries = [entry for entry in new_po if entry.msgid not in translation_map]
        updated_entries.extend(new_entries)

        new_po.clear()
        new_po.extend(updated_entries)

        new_po.save(new_po_path)
        os.remove(backup_po_path)

    def clean_po_file(self, po_file_path):
        """Remove fuzzy flags, empty msgstr, and comments."""
        if not os.path.exists(po_file_path):
            raise FileNotFoundError(f"Missing: {po_file_path}")

        po = polib.pofile(po_file_path)

        for entry in po:
            if "fuzzy" in entry.flags:
                entry.flags.remove("fuzzy")
                if entry.msgstr:
                    entry.msgstr = ""  

            entry.comment = ""

        po.save(po_file_path)

        with open(po_file_path, 'r', encoding='utf-8') as file:
            lines = file.readlines()

        lines = [line for line in lines if not line.startswith('#')]

        with open(po_file_path, 'w', encoding='utf-8') as file:
            file.writelines(lines)

    def move_translated_terms_to_start(self, po_file_path):
        """Move translated terms to the start while keeping original order of untranslated ones."""
        if not os.path.exists(po_file_path):
            raise FileNotFoundError(f"Missing: {po_file_path}")

        po = polib.pofile(po_file_path)

        # Keep translated terms at the start and untranslated at the end
        reordered_entries = [entry for entry in po if entry.msgstr] + [entry for entry in po if not entry.msgstr]

        # Overwrite entries while maintaining order
        po.clear()
        po.extend(reordered_entries)

        po.save(po_file_path)

    def print_untranslated_count(self, po_file_path):
        """Count and display the number of untranslated terms."""
        po = polib.pofile(po_file_path)
        count = sum(1 for entry in po if not entry.msgstr)
        self.stdout.write(f"Untranslated terms: {count}")
