from django.core.management.base import BaseCommand
from api.orders.order_model import BranchPage
from api.users.user_model import Company
from api.logs.logs_model import TrackedFields

class Command(BaseCommand):
    help = 'Create default warehouses'

    def handle(self, *args, **kwargs):
        for company in Company.objects.all():
            TrackedFields.objects.get_or_create(company=company, model='Order', field_name='branch_page')

        self.stdout.write(self.style.SUCCESS(f"Updated Tracked Fields for all companies"))