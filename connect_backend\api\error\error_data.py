from django.utils.translation import gettext as _

ERROR_MESSAGES = {
    # GENERAL ERROR MESSAGES 1 - 199
    "GENERAL_001": {
        "message": _("Invalid JSON format in request body"),
        "what_to_do": _("Please contact support."),
        'status': 500
    },
    'GENERAL_002': {
        'message': _("{model} with {field} '{value}' not found."),
        'what_to_do': _("Please check the {field} and try again or contact Support."),
        'status': 500
    },
    'GENERAL_003': {
        'message': _("Missing Fields: {fields}"),
        'what_to_do': _("Please check the {fields} and try again or contact Support."),
        'status': 400
    },
    'GENERAL_004': {
        'message': _("Invalid File Type"),
        'what_to_do': _("Only Excel (.xlsx) and CSV (.csv) are supported."),
        'status': 400
    },
    'GENERAL_005': {
        'message': _("Error reading group"),
        'what_to_do': _("Contact Support to resolve this issue"),
        'status': 500
    },
    'GENERAL_006': {
        'message': _("Validation Errors: {validation_errors}"),
        'what_to_do': _("Insure all fields are entered correctly, otherwise contact Support."),
        'status': 400
    },
    'GENERAL_007': {
        'message': _("Unexpected error: {error}"),
        'what_to_do': _("Contact Support to resolve this issue"),
        'status': 500
    },
    'GENERAL_008': {
        'message': _("A record with field {field} and value {value} already exists"),
        'what_to_do': _("Change the value of the field or contact support to resolve this issue"),
        'status': 400
    },
    'GENERAL_009': {
        'message': _("A Problem occurred with provider"),
        'what_to_do': _("Contact Support to resolve this issue"),
        'status': 500
    },
    'GENERAL_010': {
        'message': _("Excel File is empty"),
        'what_to_do': _("Check that the file is saved and contains data"),
        'status': 400
    },
    'GENERAL_011': {
        'message': _("Request wasn't successful, Status: {status_code}"),
        'what_to_do': _("Contact Support to resolve this issue"),
        'status': 400
    },
    # ORDER ERROR MESSAGES 200 - 299
    'ORDER_200': {
        "message": _("You Cannot change value of store status and delivery status together"),
        "what_to_do": _("Please choose one of the statuses to update"),
        'status': 400
    },
    'ORDER_201': {
        'message': _("Order of sequence {order_sequence} cannot change status from {order_status} to {to_status}"),
        'what_to_do': _("Please check the Order status and try again or contact Support."),
        'status': 400
    },
    'ORDER_202': {
        'message': _("Order of sequence {order_sequence} has no delivery sequence and cannot change status to {to_status}"),
        'what_to_do': _("Please check the Order status and try again or contact Support."),
        'status': 400
    },
    'ORDER_203': {
        'message': _("You are not permitted to modify the financial details of the order while its status is {status}. Financial modifications are only allowed when the order is in 'New Order' status."),
        'what_to_do': _("Please contact your manager or change the order status to 'New Order' to proceed."),
        'status': 400
    },
    # Connection Error MESSAGES 300 - 399
    "CONNECTION_300": {
        "message": _("Error sending request to delivery company {delivery_company} at {end_point}: \n {error}"),
        "what_to_do": _("Please contact support."),
        'status': 400
    },
    "CONNECTION_301": {
        "message": _("No Response Returned from Delivery Company {delivery_company.name}"),
        "what_to_do": _("contact support to check availability of delivery company"),
        'status': 400
    },
    "CONNECTION_302": {
        "message": _("Delivery Company {delivery_company.name} is not setup for Connect-plus"),
        "what_to_do": _("contact support to check setup of delivery company"),
        'status': 400
    },
    "CONNECTION_303": {
        "message": _("Error sending request to delivery company {delivery_company.name} \n {error}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_304": {
        "message": _("Your Credentials are invalid at {delivery_company.name} \n {error}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_305": {
        "message": _("You're unauthorized to preform this action at {delivery_company.name} \n {error}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_306": {
        "message": _("an error occured at {delivery_company.name} \n {error}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_307": {
        "message": _("Error sending to delivery company {delivery_company.name} \n Error Code: {error_code} \n Error Message: {connect_message}"),
        "what_to_do": _("Send error code to the support team or the delivery company"),
        'status': 400
    },
    "CONNECTION_308": {
        "message": _("Error received from delivery company {delivery_company.name} \n {error}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_309": {
        "message": _("Error in handling response from {delivery_company.name} \n {error}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_310": {
        "message": _("an unexpected response with code {error_code} from {delivery_company.name} \n {error}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_311": {
        "message": _("an unexpected action {action} from {delivery_company.name}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_312": {
        "message": _("Error importing {delivery_company.name} user information \n {error}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_313": {
        "message": _("Some information is missing: {missing_info}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 400
    },
    "CONNECTION_314": {
        "message": _("Error creating pricelist in connect-plus \n {error}"),
        "what_to_do": _("Send a screenshot of the error to the support team to resolve this issue"),
        'status': 200
    },
    "CONNECTION_315": {
        "message": _("Can't send orders to delivery company {delivery_company.name} with connection status {status}"),
        "what_to_do": _("Insure that the connection is accepted by the delivery company"),
        'status': 400
    },
    "CONNECTION_316": {
        "message": _("Delivery Company {delivery_company.name} has no url saved"),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 400
    },
    "CONNECTION_317": {
        "message": _("Cannot cancel connection when in status {status}"),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 400
    },
    "CONNECTION_318": {
        "message": _("Pricelist Cannot be exported"),
        "what_to_do": _("Please insure that the pricelist covers the business user area"),
        'status': 200
    },
    "CONNECTION_319": {
        "message": _("You are not connected to {delivery_company}"),
        "what_to_do": _("Establish a connection with {delivery_company} or contact Support"),
        'status': 400
    },
    "CONNECTION_320": {
        "message": _("Delivery Fee for {area} with {delivery_company} cannot be found"),
        "what_to_do": _("contact support to resolve this issue"),
        'status': 400
    },
    "CONNECTION_321": {
        "message": _("a connection to {delivery_company} with {username} already exists"),
        "what_to_do": _("change the username and try again."),
        'status': 400
    },
    "CONNECTION_322": {
        "message": _("You have reached the limit of connections with {delivery_company}"),
        "what_to_do": _("contact support to resolve this issue"),
        'status': 400
    },
    # AUTH ERROR MESSAGES 400 - 499
    "AUTH_400": {
        "message": _("user with mobile_number {username} is unauthorized"),
        "what_to_do": _("Login to continue using Connect-Plus"),
        'status': 401
    },
    "AUTH_401": {
        "message": _("An Error occurred while updating {role}'s permissions \n {error}"),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 500
    },
    "AUTH_402": {
        "message": _("An Error occurred while creating {role} \n {error}"),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 500
    },
    "AUTH_403": {
        "message": _("Mobile number: {mobile_number} is invalid"),
        "what_to_do": _("Please insure that mobile number is like 05XXXXXXXX or 07XXXXXXXX, otherwise contact Support to resolve this issue"),
        'status': 400
    },
    "AUTH_404": {
        "message": _("Mobile number: {mobile_number} is already used"),
        "what_to_do": _("Try logging into the account or reseting your password, otherwise contact Support to resolve this issue"),
        'status': 400
    },
    "AUTH_405": {
        "message": _("Error generating OTP: {error}"),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 500
    },
    "AUTH_406": {
        "message": _("Token Expired at {expires_at}"),
        "what_to_do": _("Retry generating a new OTP and registering"),
        'status': 400
    },
    "AUTH_407": {
        "message": _("Token is invalid"),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 401
    },
    "AUTH_408": {
        "message": _("Unexpected Error in creating user"),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 500
    },
    "AUTH_409": {
        "message": _("OTP Limit exceeded"),
        "what_to_do": _("generate a new OTP token or contact Support to resolve this issue"),
        'status': 400
    },
    "AUTH_410": {
        "message": _("Number of trials exceeded the limit"),
        "what_to_do": _("generate a new OTP token or contact Support to resolve this issue"),
        'status': 400
    },
    "AUTH_411": {
        "message": _("User with mobile number isn't activated"),
        "what_to_do": _("Recheck the mobile number, or contact your supervisor to get an invitation link"),
        'status': 400
    },
    "AUTH_412": {
        "message": _("Invalid Mobile Number or Password"),
        "what_to_do": _("Please try again or try reseting your password"),
        'status': 400
    },
    "AUTH_413": {
        "message": _("User is inactive"),
        "what_to_do": _("Contact Support to activate your account"),
        'status': 400
    },
    "AUTH_414": {
        "message": _('User with provided mobile number does not exist'),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 400
    },
    "AUTH_415": {
        "message": _('Cannot change password for an inactive user'),
        "what_to_do": _("Contact Support to activate your account"),
        'status': 400
    },
    "AUTH_416": {
        "message": _('You dont have permission for {end_point}'),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 403
    },
    "AUTH_417": {
        "message": _('You cannot deactivate your own user'),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 400
    },
    "AUTH_418": {
        "message": _("The mobile number {mobile_number} is already associated with another account."),
        "what_to_do": _("Please make sure you've entered the correct number. If the issue continues, contact our support team for assistance."),
        "status": 400
    },
    "AUTH_419": {
        "message": _("The name {company_name} is already associated with another account."),
        "what_to_do": _("Please make sure you've entered the correct name. If the issue continues, contact our support team for assistance."),
        "status": 400
    },

    # PRODUCT ERROR MESSAGES 500 - 599
    "PRODUCT_500": {
        "message": _("Category with same name alraedy exists"),
        "what_to_do": _("Change Category name"),
        'status': 400
    },
    "PRODUCT_501": {
        "message": _("A variant with SKU '{sku}' already exists for the product '{product_name}'. Please use a unique SKU for each variant."),
        "what_to_do": _("To avoid duplicate SKUs, please check if any existing variant already uses the same SKU before adding a new one. Each variant must have a unique SKU."),
        'status': 400
    },
    "PRODUCT_502": {
    "message": _("A variant with the same attribute combination already exists for the product '{product_name}'."),
    "what_to_do": _("Please select a different combination of attributes to create a unique variant. Each variant must have a distinct set of attribute values."),
    "status": 400
    },

    "PRODUCT_503": {
        "message": _("A product with Reference Sequence '{reference_sequence}' already exists for the product '{product_name}'. Please use a unique Reference Sequence for each product."),
        "what_to_do": _("To avoid duplicate Reference Sequence, please check if any existing product already uses the same Reference Sequence before adding a new one. Each product must have a unique Reference Sequence."),
        'status': 400
    },
    "PRODUCT_504": {
    "message": _("Invalid quantity value '{quantity}'. Quantity must be a valid numerical value."),
    "what_to_do": _("Please ensure the quantity field contains only numerical values (integers or decimals). Remove any non-numeric characters and try again."),
    'status': 400
    },

    # PRICELIST ERROR MESSAGES 600 - 699

    # USER ERROR MESSAGES 700 - 799

    # BILLING ERROR MESSAGES 800 - 899
    "BILLING_800": {
        "message": _("Subscription Ended, Please Renew your subscription"),
        "what_to_do": _("Go to the Billing on Connect-Plus and Payment Section under Configuration to see available plans"),
        'status': 400
    },
    "BILLING_801": {
        "message": _("You have exceeded your order limit"),
        "what_to_do": _("Upgrade your subscription to be able to add more orders"),
        'status': 400
    },
    "BILLING_802": {
        "message": _("Couldn't cancel your subscription"),
        "what_to_do": _("Contact Support to resolve this issue"),
        'status': 400
    },
    # AREA ERROR MESSAGES 900 - 999
    "AREA_900": {
        "message": _('The operation failed due to an unrecognized area or city name.'),
        "what_to_do": _('Please verify and update the area or city name to match the delivery company\'s accepted list, or contact support for assistance.'),
        'status': 400
    },
    # WAREHOUSE AND STOCK 1000-1099
    "WAREHOUSE_1000": {
        "message": _("You have no remaining stock in your warehouse"),
        "what_to_do": _("Contact your manager, otherwise contact support to resolve this issue"),
        'status': 400
    },
    "WAREHOUSE_1001": {
        "message": _("You cannot remove product variant from warehouse when it has stock, physical quantity = {variant.physical_quantity} & virtual quantity = {variant.virtual_quantity}"),
        "what_to_do": _("Clear the stock or wait for the orders with this product variant to process before removing it"),
        'status': 400
    },
    # MYSTORE ERROR MESSAGES 1101 - 1200
    "MYSTORE_1101": {
        "message": _("Ecommerce Store is not active"),
        "what_to_do": _("Please contact support."),
        'status': 400
    },
    # RESELLER 1200-1299
    "RESELLER_1200": {
        "message": _("A Business with mobile number {mobile_number} already exists"),
        "what_to_do": _("Insure that the correct mobile number is inputted or contact support to resolve this issue")
    },
    "RESELLER_1201": {
        "message": _("You've reached your limit of businesses"),
        "what_to_do": _("Contact Support to resolve this issue")
    },
    "RESELLER_1202": {
        "message": _("A Reseller for this delivery company already exists"),
        "what_to_do": _("Please check the delivery company chosen")
    },
    "RESELLER_1203": {
        "message": _("No Changes were made to update profile"),
        "what_to_do": _("Please check that you have applied changes to your profile")
    },
    # MYSTORE ERROR MESSAGES 1300 - 1399
    "INTEGRATION_1300": {
        "message": _("Order of sequence {order_sequence} is not assigned to delivery company {delivery_company}"),
        "what_to_do": _("Please contact the business to check the order."),
        'status': 400
    },
    # FEATURE ERROR MESSAGES 1400 - 1499
    "FEATURE_1400": {
        "message": _("{feature} is not enabled for your account."),
        "what_to_do": _("Please activate it or contact support."),
        'status': 400
    },

    # CATEGORY ERROR MESSAGES 1500 - 1599
    "CATEGORY_1500": {
        "message": _("Default category is not configured."),
        "what_to_do": _("Set the default category in your company configuration before deleting this category."),
        'status': 400
    },

    "CATEGORY_1501": {
        "message": _("You cannot delete the default category."),
        "what_to_do": _("Can't delete the default category. Please update the default category before attempting to delete this one."),
        'status': 400
    },


}