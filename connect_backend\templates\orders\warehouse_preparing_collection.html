<!DOCTYPE html>

{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/warehouse_preparing_collection.css">
</head>

{% for warehouse in docs.warehouses %}
    {% if meta.lang == 'ar' %}
        <div class="page rtl">
    {% else %}
        <div class="page ltr">
    {% endif %}
        <div class="header">
            <div class="business-logo">
                <img src="{{ docs.company_logo }}" class="business-logo">
            </div>
            <h2 class="info-item">
                {{ docs.company_name }} <span>/</span> {{ warehouse.warehouse }}
            </h2>
            <div class="date-time fw-bold">
                {{ meta.formatted_now }}
            </div>
        </div>

        <table>
            <tr>
                <th>{{ meta.labels.PRODUCTS }}</th>
                <th>{{ meta.labels.QUANTITY }}</th>
                <th>{{ meta.labels.LOCATIONS }}</th>
            </tr>
            {% for product, item in warehouse.variants.items() %}
                <tr>
                    <td>{{ product }}</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.locations }}</td>
                </tr>
            {% endfor %}
        </table>
    </div>
{% endfor %}
</html>