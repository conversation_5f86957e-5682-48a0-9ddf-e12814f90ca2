from django.db import models
from ..lookups.lookup_model import *
import re

def is_olivery_company(url):
    regex = r'^https?:\/\/([\w\-]+\.)?olivery\.(io|app)$'
    return bool(re.match(regex, url))

class DeliveryCompany(SuperModel):
    name = models.CharField(max_length=200)
    company_image = models.ImageField(upload_to='delivery_company_images/', null=True, blank=True)
    delivery_company_url = models.CharField(max_length=200, null=False, blank=False)
    delivery_company_db = models.CharField(max_length=200, null=True, blank=True)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    fully_synced_areas = models.BooleanField(default=False)
    fully_synced_subareas = models.BooleanField(default=False)
    fully_synced_statuses = models.BooleanField(default=False)
    is_subarea_required = models.BooleanField(default=True)
    is_olivery_company = models.BooleanField(default=True)
    documentation_link = models.CharField(max_length=500, null=True, blank=True)
    use_area_mapping = models.BooleanField(default=True)
    meta_data = models.JSONField(default=list)
    image_url = models.CharField(max_length=500, null=True, blank=True)
    order_custom_fields = models.JSONField(default=list)
    integration_template = models.ForeignKey('IntegrationTemplate', on_delete=models.SET_NULL, null=True, blank=True)
    template_meta_data = models.JSONField(default=dict)

    def save(self, *args, **kwargs):
        self.is_olivery_company = is_olivery_company(self.delivery_company_url)
        super(DeliveryCompany, self).save(*args, **kwargs)

class DeliveryCompanyEndPoints(models.Model):
    delivery_company = models.ForeignKey(DeliveryCompany, on_delete=models.CASCADE)
    create_api = models.CharField(max_length=200, null=True, blank=True)
    edit_api = models.CharField(max_length=200, null=True, blank=True)

    def save(self, *args, **kwargs):
        if self.delivery_company.is_olivery_company:
            self.create_api = "/connection/create_multi_order"
            self.edit_api = "/edit_order"
        super(DeliveryCompanyEndPoints, self).save(*args, **kwargs)

class AreaMap(SuperModel):
    area = models.ForeignKey(Area,on_delete=models.CASCADE)
    imported_area = models.CharField(max_length=200, null=True, blank=True)
    imported_area_id = models.IntegerField(null=True, blank=True)
    delivery_company = models.ForeignKey(DeliveryCompany, on_delete=models.CASCADE)

class SubAreaMap(SuperModel):
    sub_area = models.ForeignKey(SubArea,on_delete=models.CASCADE)
    imported_sub_area = models.CharField(max_length=200, null=True, blank=True)
    imported_sub_area_id = models.IntegerField(null=True, blank=True)
    parent_area_name = models.CharField(max_length=200, null=True, blank=True)
    parent_area_id = models.IntegerField(null=True, blank=True)
    delivery_company = models.ForeignKey(DeliveryCompany, on_delete=models.CASCADE)

class DeliveryStatusMap(SuperModel):
    imported_status_code = models.CharField(max_length=200, null=True)
    imported_status_name = models.CharField(max_length=200, null=True)
    status = models.ForeignKey('StatusMap',on_delete=models.SET_NULL, null=True)
    delivery_company = models.ForeignKey(DeliveryCompany, on_delete=models.CASCADE)
