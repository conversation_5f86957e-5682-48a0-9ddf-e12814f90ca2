from django.core.management.base import BaseCommand
from api.users.user_model import User, Company

class Command(BaseCommand):
    help = 'Set owner of businesses'

    def handle(self, *args, **kwargs):
        companies = list(Company.objects.all())
        total = len(companies)

        for index, company in enumerate(companies, start=1):
            owner_user = User.objects.filter(company=company, mobile_number=company.company_mobile).first()

            if owner_user:
                user = owner_user
            else:
                user = User.objects.filter(company=company, role__code='super_manager').first()
                if user:
                    self.stdout.write(
                        self.style.WARNING(
                            f"⚠️ No user with mobile {company.company_mobile} for company '{company.company_name}' "
                            f"(ID: {company.company_id}) — using super manager: {user.name} ({user.mobile_number})"
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(
                            f"❌ No matching user or super_manager for company '{company.company_name}' (ID: {company.company_id})"
                        )
                    )
                    continue

            company.owner = user
            company.save()

        self.stdout.write(
            self.style.SUCCESS(
                "Set owner for businesses"
            )
        )
