<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <style>
        :root {
            --black: #1A1818;
            --grey: #ECEBEB;
            --white: #ffffff;
        }

        @page {
            size: 100mm 100mm;
            margin: 0;
        }

        body,
        html {
            font-size: 9pt;
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            color: var(--black)
        }

        div {
            box-sizing: border-box;
        }

        .waybill-container {
            padding: 1.58rem;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .business-logo {
            display: inline-block;
            background-color: #999;
            border-radius: .34rem;
            height: 3.39rem;
            width: 12.42rem;
            display: inline-block;
        }

        .date-time {
            font-size: 1.13rem;
        }

        .card {
            border: solid 0.033rem;
            border-radius: .25rem;
            padding: .6rem;
            margin-top: .5rem;
        }

        .card-header {
            font-size: 1.25rem;
            font-weight: bold;
            border-bottom: .15rem solid;
            padding-bottom: .5rem;
            margin-bottom: .5rem;
        }

        .info-block {
            padding: .5rem;
            background-color: var(--grey);
            border-radius: .12rem;
        }

        .left-cards-container {
            display: inline-block;
            width: 43%;
            padding: .8rem .5rem .8rem 0rem;
        }

        .right-cards-container {
            display: inline-block;
            width: 55%;
            padding: .8rem 0rem .8rem 0rem;
        }
    </style>
</head>

<body>
    {% for doc in docs %}
    <div class="waybill-container">
        <div class="header">
            <div class="business-logo">
                <img src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}" alt="Barcode Sequence">
            </div>
            <div class="date-time">
                12:30 15-06-2024
            </div>
        </div>
        <div class="left-cards-container">
            <div class="card">
                <div class="card-header">
                    Recipient
                </div>
                <div>
                    <div class="info-block">
                        Test recipient name
                    </div>
                    <div class="info-block">
                        059111111111
                    </div>
                    <div class="info-block">
                        <div>
                            Area,
                        </div>
                        <div>
                            Test Recipient Address
                        </div>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    Delivered By
                </div>
                <div>
                    {% if doc.order.connection and doc.order.connection.delivery_company %}
                        {{ doc.order.connection.delivery_company.name }}
                    {% endif %}
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    COD
                </div>
                <div>
                    3500.00
                </div>
            </div>
        </div>
        <div class="right-cards-container">
            <div class="card">
                <div class="card-header">
                    Notes
                </div>
                <div>
                    Test notes .....
                </div>
            </div>
            <div class="card">
                <div class="card-header">
                    Products
                </div>
                <div>
                    <div class="info-block">
                        <span>
                            Test Product name....
                        </span>
                        <span>
                            X2
                        </span>
                        <span>
                            1000.00
                        </span>
                    </div>
                    <div class="info-block">
                        <span>
                            Test Product name....
                        </span>
                        <span>
                            X1
                        </span>
                        <span>
                            1000.00
                        </span>
                    </div>
                    <div class="info-block">
                        <span>
                            Test Product name....
                        </span>
                        <span>
                            X3
                        </span>
                        <span>
                            1000.00
                        </span>
                    </div>
                    <div class="info-block total">
                        <span>
                            Total:
                        </span>
                        <span>
                            3000.00
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="barcode-container">
            Barcode is here
        </div>
    </div>
    {% endfor %}
</body>

</html>