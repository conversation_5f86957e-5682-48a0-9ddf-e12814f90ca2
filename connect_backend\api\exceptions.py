from rest_framework.views import exception_handler
from rest_framework.exceptions import AuthenticationFailed, NotAuthenticated, PermissionDenied, NotFound
from api.error.error_model import *
from django.http import Http404

def custom_exception_handler(exc, context):
    endpoint = context["request"].path if "request" in context else "unknown"
    response = exception_handler(exc, context)
    if isinstance(exc, (AuthenticationFailed, NotAuthenticated)):
        raise get_error_response('AUTH_400', {'username': 'Unknown'})
    if isinstance(exc, PermissionDenied):
        raise get_error_response('AUTH_416', {'end_point': endpoint})

    return response