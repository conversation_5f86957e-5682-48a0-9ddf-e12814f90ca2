from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import *
from ..util_functions import *
from .release_document_utils import *
from rest_framework.permissions import AllowAny
from .utils_versioning import sort_items_by_version

@csrf_exempt
@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_sorted_release_documents(request):
    try:
        documents = ReleaseDocument.objects.filter(is_active=True)

        sorted_documents = sort_items_by_version(
            documents,
            lambda doc: doc.document_title
        )
        paginated_documents = sorted_documents[:20]

        serialized = ReleaseDocumentSerializer(paginated_documents, many=True)

        return JsonResponse({"success": True, "records": serialized.data}, status=200)

    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
