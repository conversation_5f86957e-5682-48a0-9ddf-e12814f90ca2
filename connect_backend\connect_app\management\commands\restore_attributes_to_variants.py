from django.core.management.base import BaseCommand
from api.products.product_model import ProductVariant, AttributeValue, VariantAttribute

class Command(BaseCommand):
    help = 'Update currency for orders based on customer mobile number or company currency'

    def handle(self, *args, **kwargs):
        variants = ProductVariant.objects.all()
        for variant in variants:
            variant_name = variant.name.split('-')
            for value in variant_name[1:]:
                attribute = AttributeValue.objects.filter(value=value, company=variant.product.company).first()
                if attribute:
                    if not variant.attributes.all().filter(value=attribute).exists():
                        VariantAttribute.objects.create(variant=variant, value=attribute)
                        print(f"Added attribute {attribute.value} to variant {variant.name}")