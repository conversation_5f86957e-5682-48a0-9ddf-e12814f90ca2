from django.utils.translation import gettext as _

NEW_ORDER = _("New Order")  # طلب جديد
WAITING = _("Waiting")  # انتظار
PICKED_UP = _("Picked up")  # تم الاستلام
PICKING_UP = _("Picking up")  # جارٍ الاستلام
READY_FOR_DISTRIBUTION = _("Ready for Distribution")  # جاهز للتوزيع
IN_PROGRESS = _("In Progress")  # قيد التنفيذ
DELIVERED = _("Delivered")  # تم التسليم
STUCK = _("Stuck")  # محتجز
RESCHEDULE = _("Reschedule")  # إعادة جدولة
RETURNED = _("Returned")  # تم الإرجاع
PARTIAL_RETURNED = _("Partial Returned")  # إرجاع جزئي
REPLACEMENT = _("Replacement")  # استبدال
READY_FOR_RETURN = _("Ready for Return")  # جاهز للإرجاع
RETURNED_IN_PROGRESS = _("Returned In Progress")  # الإرجاع قيد التنفيذ
PREPARING = _("Preparing")  # جارٍ التحضير
READY_FOR_DELIVERY = _("Ready For Delivery")  # جاهز للتوصيل
COMPLETED = _("Completed")  # مكتمل
DELETED = _("Deleted")  # محذوف
DELIVERED_PARTIAL = _("Delivered Partial")  # تم التسليم جزئيًا
DELIVERED_STUCK = _("Delivered Stuck")  # تم التسليم محتجز
WITH_DELIVERY_COMPANY = _("With Delivery Company")  # في طريقه للتوصيل
MONEY_RECEIVED = _("Money Received")  # تم استلام المال
COMPLETED_PARTIAL = _("Completed Partial")  # مكتمل جزئيًا
EXPECTED_RETURN_MONEY = _("Expected Return Money")  # المبلغ المتوقع إرجاعه
EXPECTED_RETURN_PACKAGE = _("Expected Return Package")  # الحزمة المتوقع إرجاعها
MONEY_COLLECTED = _("Money Collected")  # تم جمع المال
NORMAL_ORDER = _("Normal")  # عادي
REPLACEMENT_ORDER = _("Replacement")  # استبدال
PARTIAL_DELIVERY_ORDER = _("Partial Delivery")  # تسليم جزئي
IN_BRANCH = _("In Branch") # في الفرع
RETRIEVAL_ORDER = _("Retrieval")  # استرجاع
PALESTINE = _("Palestine") # فلسطين
JORDAN = _("Jordan") # الأردن
COMPLETED_RETURNED = _("Completed Returned") # مرجع مكتمل
BRANCH_RETURNED = _("Branch Returned") #
SUPER_MANAGER = _("Super Manager") #
MANAGER = _("Manager") #
SALES_MANAGER = _("Sales Manager") #
SALES = _("Sales") #
ACCOUNTANT = _("Accountant") #
CALL_CENTER = _("Call center") #
DRIVER = _("Driver") #
TO_BRANCH = _("To Branch") #
READY_FOR_DISPATCH = _("Ready For Dispatch") #
MONEY_IN = _("Money In") #
MONEY_OUT = _("Money Out") #
REJECTED_PARTIAL = _("Rejected Partial") #
WAREHOUSE = _("Warehouse") #
WITH_DRIVER = _("With Driver") # مع السائق
_('United Arab Emirates') # الإمارات العربية المتحدة
_('Warehouse data entry')
_('Oman') # عُمان
_('Waiting Activation')
_("Pending") #
_("Active") #
_('Kuwait') # الكويت
_('Saudi Arabia') # السعودية
_('Iraq') # العراق
_('Libya') # ليبيا