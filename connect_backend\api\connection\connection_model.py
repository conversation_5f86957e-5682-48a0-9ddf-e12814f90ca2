from django.db import models
from ..delivery_company.delivery_company_model import DeliveryCompany
from ..lookups.lookup_model import Area
from ..users.user_model import Company
from ..models import SuperModel
from django.utils.translation import gettext as _

class ConnectDeliveryCompany(SuperModel):
    company = models.ForeignKey(Company,on_delete=models.CASCADE,null=True)
    delivery_company = models.ForeignKey(DeliveryCompany, related_name='connections', on_delete=models.SET_NULL,null=True)
    connection_status = models.CharField(max_length=200,
                                         choices=[('not_connected',_('Not Connected')),
                                                  ('connected',_('Connected')),
                                                  ('pending',_('Pending')),
                                                  ('rejected',_('Rejected')),
                                                  ('cancelled',_('Cancelled')),
                                                  ],
                                                  default="not_connected")
    user_url = models.Char<PERSON>ield(max_length=400, null=True, blank=True)
    area = models.ForeignKey(Area,on_delete=models.SET_NULL,null=True)
    name = models.Char<PERSON>ield(max_length=200, null=True)
    display_name = models.CharField(max_length=200, null=True)
    custom_field_defaults = models.JSONField(default=dict)
    
    def is_connected(self):
        return self.connection_status == "connected"
    
    def save(self, *args, **kwargs):
        if not self.name:
            name = self.delivery_company.name
            self.name = name
        if self.name:
            if self.name != self.delivery_company.name:
                self.display_name = f'{self.delivery_company.name}-{self.name}'
            else:
                self.display_name = self.delivery_company.name
        super(ConnectDeliveryCompany, self).save(*args, **kwargs)
    
class ConnectionUser(SuperModel):
    company = models.ForeignKey(Company,on_delete=models.CASCADE, null=True)
    delivery_company = models.ForeignKey(DeliveryCompany,on_delete=models.SET_NULL,null=True)
    company_username = models.CharField(max_length=200, null=True)
    company_email = models.CharField(max_length=200, null=True, blank=True)
    company_password = models.CharField(max_length=200, null=True)
    connection = models.OneToOneField(ConnectDeliveryCompany, related_name='credentials', on_delete=models.SET_NULL, null=True)
    token = models.TextField(null=True)
    meta_data = models.JSONField(default=dict)