body,
html {
    font-size: 7.5pt;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

table {
    width: 100%;
    border-collapse: collapse;
}

td,
th {
    border: 1px solid #dddddd;
    padding: 8px;
}

tr:nth-child(even) {
    background-color: #eee;
}

.page {
    page-break-after: always;
    padding: 20px;
}

.page:last-child {
    page-break-after: auto;
}
.rtl {
    direction: rtl;
}
.ltr {
    direction: ltr;
}
