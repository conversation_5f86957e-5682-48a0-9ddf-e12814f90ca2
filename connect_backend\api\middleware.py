from django.utils import translation
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonR<PERSON>ponse
from api.error.error_model import ConnectError, get_error_response
from api.users.user_model import User
from api.admin.admin_model import <PERSON><PERSON><PERSON><PERSON><PERSON>s
import json
from django.http import Http404
from rest_framework.exceptions import NotFound
import threading
from .admin.store_domain_mapping.store_domain_mapping_model import StoreDomainMapping
from django.core.cache import cache
import re

_thread_locals = threading.local()

class LanguageMiddleware(MiddlewareMixin):
    def process_request(self, request):
        accept_language = request.META.get('HTTP_ACCEPT_LANGUAGE', '')
        if accept_language:
            language = accept_language.split(',')[0]
            translation.activate(language)
            request.LANGUAGE_CODE = language

    def process_response(self, request, response):
        response['Content-Language'] = translation.get_language()
        return response
    
class ConnectErrorMiddleware(MiddlewareMixin):
    def process_exception(self, request, exception):
        company = None
        try:
            if hasattr(request, "user") and request.user.is_authenticated:
                user = request.user
                try:
                    user = User.objects.get(user=request.user)
                except User.DoesNotExist:
                    pass
                company = getattr(user, "company", None)
        except Exception as e:
            company = None
        if not isinstance(exception, ConnectError):
            connect_error = get_error_response('GENERAL_007', {'error': str(exception)})
        else:
            connect_error = exception
        response_data = connect_error.to_dict()
        status_code = connect_error.status if connect_error.status else 500
        self.log_error(request, response_data, status_code, company)
        return JsonResponse(response_data, status=status_code)

    def log_error(self, request, response_data, status_code, company):
        try:
            request_body = self.get_request_body(request)
            ErrorLogs.objects.create(
                end_point=request.path,
                request_body=request_body,
                response_body=json.dumps(response_data),
                status_code=status_code,
                company=company,
            )
        except Exception as log_exception:
            print(f"Failed to log error: {log_exception}")

    def get_request_body(self, request):
        try:
            return request.body.decode('utf-8') if request.body else ""
        except Exception:
            return ""

def get_current_request():
    return getattr(_thread_locals, 'request', None)

def get_current_user():
    request = get_current_request()
    if request:
        return getattr(request, 'user', None)
    return None

class ThreadLocalMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        _thread_locals.request = request
        response = self.get_response(request)
        return response

STATIC_CORS_REGEXES = [
    r'capacitor:\/\/localhost',
    r'^https?:\/\/([\w\-]+\.)?olivery\.io$',
    r'^https?:\/\/([\w\-]+\.)*connect\-plus\.app?$',
    r'^https?:\/\/([\w\-]+\.)*localhost(:4200|:4201|:8100)?$',
    r'^https?:\/\/([\w\-]+\.)connect\-dev\.olivery\.io$',
]

CACHE_KEY = 'allowed_cors_store_urls'
CACHE_TIMEOUT = 300

def get_cached_store_urls():
    urls = cache.get(CACHE_KEY)
    if urls is None:
        urls = list(StoreDomainMapping.objects.values_list('store_url', flat=True))
        cache.set(CACHE_KEY, urls, CACHE_TIMEOUT)
    return urls

class DynamicCORSOriginMiddleware(MiddlewareMixin):
    def process_request(self, request):
        origin = request.META.get('HTTP_ORIGIN')
        allowed = False
        # Check static regexes
        for pattern in STATIC_CORS_REGEXES:
            if re.match(pattern, origin or ''):
                allowed = True
                break
        # Check cached DB URLs
        if not allowed and origin:
            allowed = origin in get_cached_store_urls()
        if allowed:
            request._dynamic_cors_origin = origin
            if request.method == 'OPTIONS':
                from django.http import HttpResponse
                response = HttpResponse()
                response['Access-Control-Allow-Origin'] = origin
                response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS, PUT, DELETE'
                response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, Store-Name'
                response['Access-Control-Allow-Credentials'] = 'true'
                return response

    def process_response(self, request, response):
        origin = getattr(request, '_dynamic_cors_origin', None)
        if origin:
            response['Access-Control-Allow-Origin'] = origin
            response['Access-Control-Allow-Credentials'] = 'true'
        return response