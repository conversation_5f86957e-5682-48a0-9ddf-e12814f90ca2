from django.conf import settings
from .store_domain_mapping_model import StoreDomainMapping
from ...mystore.mystore_model import MyStore

def create_store_domain_mapping(values):
    values['mystore'] = MyStore.objects.get(id=values['mystore'])
    mapping = StoreDomainMapping.objects.create(**values)
    return mapping

def update_store_domain_mapping_util(values):
    mapping = StoreDomainMapping.objects.get(id=values['id'])
    values['mystore'] = MyStore.objects.get(id=values['mystore'])
    for key, value in values.items():
        setattr(mapping, key, value)
    mapping.save()
    return mapping
