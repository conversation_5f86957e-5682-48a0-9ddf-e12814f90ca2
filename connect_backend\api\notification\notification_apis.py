from django.utils.translation import gettext as _
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from ..util_functions import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ..permissions import *
from ..error.error_model import *
from django.utils.timezone import now

@api_view(['GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_active_banners(request):
    banners = NotificationBanner.objects.filter(
        active=True,
        start_date__lte=now(),
        end_date__gte=now()
    )
    serializer = NotificationBannerSerializer(banners, many=True)
    return JsonResponse({'success': True, 'banners': serializer.data}, status=200)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_notification_center_items(request):
    json_data = parse_request_body(request)
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('NotificationCenter', json_data, False, company_filter=Q(users=user))
    return JsonResponse(response_data, status=200)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def update_notification_center_item(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    notification_id = json_data.get('notification_id')
    
    try:
        notification = NotificationCenter.objects.get(id=notification_id)
    except NotificationCenter.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'NotificationCenter', 'field':'id', 'value': notification_id})
    
    try:
        notification.seen = True
        notification.save()

    except Exception as e:
        print(e)
        pass

    return JsonResponse({'success': True, 'message': 'Notification Updated Successully'}, status=200)