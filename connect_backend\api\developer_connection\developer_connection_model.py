from django.db import models
from ..models import SuperModel
from ..users.user_model import Company
from ..orders.order_model import *

class DeveloperConnection(SuperModel):
    name = models.CharField(max_length=200)
    description = models.TextField(max_length=255, blank=True, null=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=False)

class DeveloperConnectionToken(SuperModel):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=False)
    developer_connection = models.ForeignKey(DeveloperConnection, on_delete=models.CASCADE, null=False)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    token = models.CharField(max_length=200, null=True)


class DeveloperOrder(SuperModel):
    developer_connection = models.ForeignKey(DeveloperConnection, on_delete=models.CASCADE, null=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE, null=False)
    
class DeveloperConnectionWebhook(SuperModel):
    developer_connection = models.ForeignKey(DeveloperConnection, on_delete=models.CASCADE, null=False)
    url = models.URLField(max_length=200)
    is_active = models.BooleanField(default=True)
    apply_to_all_orders = models.BooleanField(default=False)
    headers = models.JSONField(default=dict)

class WebhookLog(SuperModel):
    webhook = models.ForeignKey(DeveloperConnectionWebhook, on_delete=models.CASCADE, null=False)
    request_body = models.JSONField(default=dict)
    request_headers = models.JSONField(default=dict, blank=True, null=True)
    response_body = models.JSONField(default=dict, blank=True, null=True)
    status_code = models.IntegerField(default=200)
    created_at = models.DateTimeField(auto_now_add=True)
