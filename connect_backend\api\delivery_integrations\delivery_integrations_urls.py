from django.urls import path
from .delivery_integrations_apis import *

urlpatterns = [
    path('get_connection_token', get_connection_token, name='get_connection_token'),
    path('add_connection_token', add_connection_token, name='add_connection_token'),
    path('regenerate_connection_token', regenerate_connection_token, name='regenerate_connection_token'),
    path('update_order_status', update_order_status, name='update_order_status'),
    path('refresh_token', refresh_token, name='refresh_token'),
    path('update_status_webhook', update_status_webhook, name='update_status_webhook'),
]