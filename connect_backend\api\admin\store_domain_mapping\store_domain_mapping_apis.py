from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import *
from ...util_functions import *
from ...permissions import *
from .store_domain_mapping_utils import *

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_store_domain_mappings(request):
    try: 
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        response_data = get_records('StoreDomainMapping', json_data, False)
        
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def add_store_domain_mapping(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        mapping = create_store_domain_mapping(json_data)
        serializer = StoreDomainMappingSerializer(mapping)
        return JsonResponse({'success': True, 'mapping': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def update_store_domain_mapping(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        mapping = update_store_domain_mapping_util(json_data)
        serializer = StoreDomainMappingSerializer(mapping)
        return JsonResponse({'success': True, 'mapping': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        status = 401 if response['error']['code'] == 'AUTH_400' else 400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([IsSuperUser])
def get_merchants_stores(request):
    try: 
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        response_data = get_records('MyStore', json_data, False)
        
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
