from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
import json

from ..connection.connection_utils import process_sending_orders
from ..util_functions import *
from .order_model import *
from ..users.user_model import User, Company
from ..lookups.lookup_model import Country, Area, SubArea
from ..delivery_company.delivery_company_model import DeliveryCompany
from ..connection.connection_model import ConnectionUser
from .order_utils import *
import logging
from django.db import transaction
from .constants import waybill_labels;
from .order_utils import *
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ..permissions import *
from ..error.error_model import *
from ..dashboard.dashboard_model import *
import pandas as pd
from rest_framework.parsers import MultiPartParser, FormParser
from django.utils.translation import gettext as _
from django.utils import translation
from django.db.models import When
logger = logging.getLogger(__name__)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_order'])])
@csrf_exempt
def add_order(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    required_fields = ['customer_mobile', 'customer_name', 'country_code', 'address',
    'area', 'sub_area', 'country', 'total_cod', 'order_type']

    if not json_data.get('order_type'):
        json_data['order_type'] = 'normal'

    validation_errors, foreign_key_objects = validate_order(json_data, user.company, required_fields)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})

    if 'parent_order' in json_data and json_data['parent_order']:
        parent_order_id = json_data.pop('parent_order')
        try:
            parent_order = Order.objects.get(id=parent_order_id)
        except Order.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'id', 'value': parent_order_id})
        foreign_key_objects['parent_order'] = parent_order
    else:
        foreign_key_objects['parent_order'] = None
    
    with transaction.atomic():
        order_lines = json_data.pop('order_lines', [])
        order_values = build_order_values(json_data, foreign_key_objects, user.company, user.name)
        order_values['created_by_user'] = user
        order = create_order(order_values, order_lines)
        if 'parent_order' in order_values and order_values['parent_order']:
            parent_order = order_values['parent_order']
            parent_order.save()
        if not order:
            raise get_error_response('GENERAL_007', {'error': _('Order Failed In Creating')})

    success_messages, fail_messages = auto_send_orders([order], user.company, user.name, status=order.status)
    serializer = OrderSerializer(order)
    response = {
        'success': True,
        'order': serializer.data,
        'success_messages': success_messages,
        'fail_messages': fail_messages
    }
    return JsonResponse(response, status=201)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_order'])])
@csrf_exempt
def add_bulk_orders(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    orders_data = json_data

    required_fields = [
        'customer_mobile', 'customer_name', 'country_code', 'address',
        'area', 'sub_area', 'country', 'total_cod', 'order_type'
    ]

    results = []
    errors = []
    order_instances = []

    try:
        with transaction.atomic():
            for idx, order_data in enumerate(orders_data):
                if not order_data.get('order_type'):
                    order_data['order_type'] = 'normal'

                validation_errors, foreign_key_objects = validate_order(order_data, user.company, required_fields)
                if validation_errors:
                    errors.append({'index': idx, 'error': ','.join(validation_errors)})
                    continue

                if 'parent_order' in order_data and order_data['parent_order']:
                    parent_order_id = order_data.pop('parent_order')
                    try:
                        parent_order = Order.objects.get(id=parent_order_id)
                    except Order.DoesNotExist:
                        errors.append({'index': idx, 'error': f'Parent order {parent_order_id} does not exist'})
                        continue
                    foreign_key_objects['parent_order'] = parent_order
                # Check if there are any validation errors before proceeding then return all errors and skip others also
                else:
                    foreign_key_objects['parent_order'] = None
                
                try:
                    order_lines = order_data.pop('order_lines', [])
                    order_values = build_order_values(order_data, foreign_key_objects, user.company, user.name)
                    order_values['created_by_user'] = user
                    order = create_order(order_values, order_lines)
                    if 'parent_order' in order_values and order_values['parent_order']:
                        parent_order = order_values['parent_order']
                        parent_order.save()
                    if not order:
                        errors.append({'index': idx, 'error': 'Order Failed In Creating'})
                        continue
                    order_instances.append(order)
                    results.append({'index': idx, 'id': order.id})
                except Exception as e:
                    errors.append({'index': idx, 'error': str(e)})
                    
            if errors:
                raise errors
    except Exception as e:
        response = {
            'success': False,
            'errors': errors
        }
        return JsonResponse(response, status=400)


    if order_instances:
        success_messages, fail_messages = auto_send_orders(order_instances, user.company, user.name, status=order_instances[0].status if order_instances else None)
        response = {
            'success': True,
            'orders': results,
            'success_messages': success_messages,
            'fail_messages': fail_messages,
            'errors': errors
        }
        return JsonResponse(response, status=201)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_order'])])
@csrf_exempt
def get_orders(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
        
    custom_filter = Q()
    if user_has_group_permission(request.user, 'view_driver_orders'):
        custom_filter = Q(driver=user, status__code='with_driver')
    if json_data.get('order_by_latest_scanned_barcode',None) and json_data.get('scanned_sequences',None):
        scanned_sequences = json_data.get('scanned_sequences', [])
        reversed_sequences = list(reversed(scanned_sequences))
        sequence_to_index = {seq: idx for idx, seq in enumerate(reversed_sequences)}
        custom_order_by = [
            When(Q(order_sequence=seq) | Q(order_reference=seq) | Q(olivery_sequence=seq)
                 | Q(parent_order__order_sequence=seq) | Q(parent_order__order_reference=seq), then=idx)
            for seq, idx in sequence_to_index.items()
        ]
        response_data = get_records(
            'Order', 
            json_data, 
            user, 
            custom_filter=custom_filter,
            custom_order_by=custom_order_by,
            custom_order_default=len(scanned_sequences)
        )
    else:
        response_data = get_records(
            'Order',
            json_data,
            user,
            custom_filter=custom_filter
        )
    return JsonResponse(response_data, status=200)

@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_order'])])
@csrf_exempt
def update_order(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    required_fields = [
        'customer_mobile', 'customer_name', 'country_code', 'address',
        'area', 'sub_area', 'country', 'order_type'
    ]

    if not json_data.get('order_type'):
        json_data['order_type'] = 'normal'

    validation_errors, foreign_key_objects = validate_order(json_data, user.company, required_fields)
    if validation_errors:
        raise get_error_response('GENERAL_006', {'validation_errors': ','.join(validation_errors)})
    
    values = {**json_data, **foreign_key_objects, 'company': user.company}
    values['updated_by'] = user.name

    with transaction.atomic():
        order_lines = values.pop('order_lines', [])
        values['area_mismatch'] = ''
        order, warning = update_order_util(values, order_lines, user)
        if 'parent_order' in values and values['parent_order']:
            parent_order = values['parent_order']
            parent_order.save()
        if not order:
            raise get_error_response('GENERAL_007', {'error': _('Order Failed In Updating')})
        
    serializer = OrderSerializer(order)
    
    return JsonResponse({'success': True, 'message': _('Order Updated Successfully'), 'order': serializer.data, 'warning': warning if warning else None})

@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([has_one_of_permissions(['change_delivery_status', 'change_store_status'])])
@csrf_exempt
def update_order_status(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
        
    company = user.company
    edited = json_data.get('edited', [])
    order_ids = json_data.get('order_ids', [])
    status_code = json_data.get('status')
    delivery_status_code = json_data.get('delivery_status')

    if status_code and delivery_status_code:
        raise get_error_response('ORDER_200', {})
    
    if delivery_status_code:
        try:
            delivery_status = StatusMap.objects.get(status_code=delivery_status_code)
        except StatusMap.DoesNotExist:
            raise get_error_response('GENERAL_002', {'model': 'StatusMap', 'field': 'code', 'value': delivery_status_code})
        for id in order_ids:
            try:
                order = Order.objects.get(id=id)
            except Order.DoesNotExist:
                continue
            if delivery_status_code == 'reschedule':
                reschedule_date = json_data.get('reschedule_date')
                order.reschedule_date = reschedule_date
            elif delivery_status_code == 'rejected' or delivery_status_code == 'returned':
                reject_reason = json_data.get('reject_reason')
                order.reject_reason = reject_reason
            elif delivery_status_code == 'stuck':
                stuck_comment = json_data.get('stuck_comment')
                order.stuck_comment = stuck_comment
            log = create_log('Order', order.company, 'update', user.name, order.id)
            log_info = create_log_info(log, 'delivery_company_status', order.delivery_company_status, delivery_status.delivery_company_status)
            order.delivery_company_status = delivery_status.delivery_company_status
            order.updated_by = user.name
            order.save()
            if order.channel.lower() == 'woocommerce' and order.order_reference:
                try:
                    send_order = send_delivery_status_to_woocommerce(order.order_reference, order.delivery_company_status, order.olivery_sequence, company)
                except:
                    pass
        response = {
        'success': True, 
        'message': _('Order status updated successfully'),
        'order_ids': order_ids
        }
        return JsonResponse(response, status=200)

    edited_ids = [item['id'] for item in edited]
    filtered_order_ids = [order_id for order_id in order_ids if order_id not in edited_ids]
    try:
        status = Status.objects.filter(code=status_code).first()
    except Status.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'STATUS_DOES_NOT_EXIST'}, status=404)
    
    failed_message = ''
    edited_order_ids = []

    order_id = None
    order = None
    try:
        if edited:
            with transaction.atomic():
                for item in edited:
                    order_id = item['id']
                    item['created_by'] = user.name
                    item['status'] = status
                    edited_ids = handle_order_type(item)
                    if len(edited_ids):
                        edited_order_ids.extend(edited_ids)
        updated_order_ids = []
        fail_messages = []
        success_messages = []
        with_delivery_company_status = Status.objects.get(code='with_delivery_company')
        with transaction.atomic():
            for order_id in filtered_order_ids:
                order = Order.objects.get(id=order_id)
                values = {
                    'id': order_id,
                    'status': status,
                    'company': company,
                    'updated_by': user.name
                }

                if (status != with_delivery_company_status or order.olivery_sequence):
                    if json_data.get('store_stuck_comment', ''):
                        values['store_stuck_comment'] = json_data.get('store_stuck_comment')
                
                    order_old_value = get_order(values['id'])
                    order = update_order_statuses(values, order_old_value)
                    updated_order_ids.append(order.id)
                    if not order:
                        return JsonResponse({'success': False, 'error': 'ORDER_NOT_FOUND'}, status=404)
                    handle_stock(order)
                    response = handle_delivery_company_status(order, company, status)
                    success_messages.append({'sequence': order.order_sequence, 'delivery_company': company.company_name, 'message': _('Order status updated successfully')})
                else :
                    if (order.status == Status.objects.get(code='with_driver')): 
                        error = get_error_response('ORDER_201', {'order_sequence': order.order_sequence, 'order_status': order.status.name, 'to_status': with_delivery_company_status.name})
                        fail_messages.append(handle_sending_connect_error(error.to_dict(), order))
                    else: # order not has olivery_sequence
                        error = get_error_response('ORDER_202', {'order_sequence': order.order_sequence,  'to_status': with_delivery_company_status.name})
                        fail_messages.append(handle_sending_connect_error(error.to_dict(), order))
    except Exception as e:
        order = order if order else Order.objects.get(id=order_id) if order_id else None
        error_message = str(e)
        if order is not None and hasattr(order, 'order_sequence'):
            error_message = f"{order.order_sequence}:{error_message}"
        return JsonResponse({
            'success': False,
            'error': error_message
        }, status= 500)

    updated_order_ids = list(set(updated_order_ids).union(edited_order_ids))
    orders = Order.objects.filter(id__in=updated_order_ids)
    auto_send_success_messages, auto_send_fail_messages = auto_send_orders(orders, user.company, user.name, status=status)
    fail_messages.extend(auto_send_fail_messages)
    success_messages.extend(auto_send_success_messages)
    response = {
        'success': True, 
        'message': _('Order status updated successfully'),
        'success_messages': success_messages,
        'fail_messages': fail_messages,
        'order_ids': updated_order_ids
        }
    return JsonResponse(response, status=200)
    
@api_view(['GET'])
@authentication_classes([CombinedJWTAuthentication])
@permission_classes([get_permission_class(['view_status'])])
@csrf_exempt
def get_statuses(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    company_conf = get_company_conf(user.company)
    records = Status.objects.filter(hidden=False)\
        .exclude(code__in=[company_conf.delivery_request_status.code,'money_collected'])\
        .order_by('show_at_position')
    serializer = StatusSerializer(records, many=True)
    return JsonResponse({'success': True, 'records': serializer.data})

@api_view(['GET'])
@authentication_classes([CombinedJWTAuthentication])
@permission_classes([get_permission_class(['view_status'])])
@csrf_exempt
def get_all_statuses(request):
    records = Status.objects.all().order_by('show_at_position')
    serializer = StatusSerializer(records, many=True)
    return JsonResponse({'success': True, 'records': serializer.data})

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_order'])])
@csrf_exempt
def get_order_by_sequence(request):
    json_data = parse_request_body(request)
    if not json_data:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)
    
    order_sequence = json_data.get('order_sequence')
    if not order_sequence:
        return JsonResponse({'success': False, 'error': 'Missing sequence parameter'}, status=400)
    
    records = Order.objects.filter(order_sequence__contains=order_sequence)
    serializer = OrderSerializer(records, many=True)
    return JsonResponse({'success': True, 'data': serializer.data})

@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_ordertype'])])
@csrf_exempt
def get_order_types(request):
    records = OrderType.objects.all()
    serializer = OrderTypeSerializer(records, many=True)
    return JsonResponse({'success': True, 'order_types': serializer.data}, status=200)

@api_view(['GET'])
@authentication_classes([CombinedJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def generate_report(request):
    if isinstance(request.user, Reseller):
        user = request.user
    else:
        try:
            user = User.objects.get(user=request.user)
        except User.DoesNotExist:
            raise get_error_response('AUTH_400', {'username': request.user.username})

    report_name = request.GET.get('report')
    ids = request.GET.getlist('ids')

    if not report_name or not ids:
        raise get_error_response('GENERAL_003', {'fields': 'report_name or ids'})

    orders = Order.objects.filter(id__in=ids).order_by('id')

    if report_name == "warehouse_preparing_collection":
        order_lines = (
            OrderLine.objects
            .filter(order__in=orders, order__warehouse__isnull=False)
            .values(
                'order__warehouse',
                'order__warehouse__name',
                'product_variant',
                'product_variant__variant__name'
            )
            .annotate(total_quantity=Sum('quantity'))
            .order_by('order__warehouse')
        )

        warehouse_data = defaultdict(lambda: {'warehouse': None, 'variants': {}})

        for line in order_lines:
            warehouse = line['order__warehouse']
            warehouse_name = line['order__warehouse__name']
            variant_id = line['product_variant']
            variant_name = line['product_variant__variant__name']
            quantity = line['total_quantity']

            locations = WarehouseVariant.objects.get(pk=variant_id).product_locations.values_list("location__name", flat=True)
            locations_str = ', '.join(locations)

            warehouse_data[warehouse]['warehouse'] = warehouse_name
            warehouse_data[warehouse]['variants'][variant_name] = {
                "quantity": quantity,
                "locations": locations_str
            }

        render_data = {
            'company_name': user.company.company_name if isinstance(user, User) else user.name,
            'company_logo': user.company.company_image_url if isinstance(user, User) else user.company_logo_url,
            'warehouses': list(warehouse_data.values())
        }
    else:
        grouped_orders = defaultdict(list)
        for order in orders:
            company_conf = get_company_conf(order.company)
            show_order_reference = company_conf.show_reference_in_waybills
            if show_order_reference and order.order_reference: 
                barcode_sequence_base64 = generate_barcode_base64(order.order_reference) if order.order_reference else None
            else: 
                barcode_sequence_base64 = generate_barcode_base64(order.order_sequence) if order.order_sequence else None

            qrcode_olivery_sequence_base64 = generate_qrcode_base64(order.olivery_sequence) if order.olivery_sequence else None
            barcode_olivery_base64 = generate_barcode_base64(order.olivery_sequence) if order.olivery_sequence else None

            total = order.total_cod - order.delivery_fee - (order.extra_delivery_fee or 0)
            order_lines = OrderLine.objects.filter(order=order)

            for line in order_lines:
                variant_id = line.product_variant.id
                locations = WarehouseVariant.objects.get(pk=variant_id).product_locations.values_list("location__name", flat=True)
                locations_str = ', '.join(locations)
                line.locations = locations_str

            if order_lines.exists():
                total = sum(item.price * item.quantity for item in order_lines)
            order_data = {
                'order': order,
                'barcode_sequence_base64': barcode_sequence_base64,
                'qrcode_olivery_sequence_base64': qrcode_olivery_sequence_base64,
                'barcode_olivery_base64': barcode_olivery_base64,
                'company': order.company,
                'company_name': (order.branch_page and order.branch_page.name) or order.company.company_name,
                'order_lines': list(order_lines),
                'total': total,
                'company_logo': (
                    (isinstance(user, Reseller) and user.company_logo_url) or
                    (order.branch_page and order.branch_page.branch_page_image_url)  or 
                    (order.company.company_image_url)  or
                    './templates/shared/connect-full-logo.png'
                    ),
                'delivery_company_logo': (
                    (order.connection and order.connection.delivery_company and order.connection.delivery_company.image_url)
                    or './templates/shared/connect-full-logo.png'
                ),
                'print_product_sequence': company_conf.print_product_sequence,
                'company_registry':user.company_registry if isinstance(request.user, Reseller) else company_conf.company_registry,
            }
            grouped_orders[order.company].append(order_data)
        render_data = [order for company_orders in grouped_orders.values() for order in company_orders]

    language = translation.get_language() or 'ar'
    language = language.lower()

    meta = {
        'labels': waybill_labels[language],
        'lang':language,
        'user_name': user.name,
    }

    response = generate_pdf('orders', report_name, render_data, meta)
    return response

@api_view(['GET'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_order'])])
@csrf_exempt
def get_order_by_id(request):
    try:
        user = User.objects.get(user=request.user)
    except:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    order_id = request.GET.get('id')
    try:
        order = Order.objects.get(id = order_id)
    except Order.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'id', 'value': order_id})
    
    serializer = OrderSerializer(order)
    return JsonResponse({'success': True, 'data': serializer.data}, status=200)
    
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([has_role(['Super Manager', 'Sales Manager'])])
@csrf_exempt
def set_default_vhub_fields(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    vhub_fields = {
        'total_cod': {
            'vhub_field': 'copy_total_cost',
            'is_foreign_key': False,
        },
        'note': {
            'vhub_field': 'note',
            'is_foreign_key': False,
        },
        'area': {
            'vhub_field': 'customer_area',
            'is_foreign_key': True,
        },
        'sub_area': {
            'vhub_field': 'customer_sub_area',
            'is_foreign_key': True,
        },
        'status': {
            'vhub_field': 'state',
            'is_foreign_key': True,
        },
        'paid': {
            'vhub_field': 'paid',
            'is_foreign_key': False,
        },
        'customer_mobile': {
            'vhub_field': 'customer_mobile',
            'is_foreign_key': False,
        },
        'address': {
            'vhub_field': 'customer_address',
            'is_foreign_key': False,
        }
    }

    delivery_statuses = ['in_branch', 'picked_up', 'picking_up', 'waiting']

    for delivery_status in delivery_statuses:
        status_map = StatusMap.objects.get(status_code=delivery_status)
        for key, value in vhub_fields.items():
            vhub_field = VhubFieldMap.objects.update_or_create(
                company=user.company,
                field=key,
                status= status_map,
                defaults={
                    'vhub_field': value['vhub_field'],
                    'is_foreign_key': value['is_foreign_key']
                }
            )

    return JsonResponse({'success': True, 'message': 'Default Vhub Fields Set Successfully'}, status=200)
    
@csrf_exempt
@api_view(['GET'])
@authentication_classes([CombinedJWTAuthentication])
@permission_classes([AllowAny])
def get_delivery_statuses(request):
    statuses = StatusMap.objects.all()
    serializer = StatusMapSerializer(statuses, many=True)
    return JsonResponse({'success': True, 'statuses': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['import_excel_order'])])
@parser_classes([MultiPartParser, FormParser])
@subscription_required
def import_orders_from_excel(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    if 'file' not in request.FILES:
        raise get_error_response('GENERAL_003', {'fields': ['file']})

    file = request.FILES['file']
    if file.content_type not in [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
        'application/vnd.ms-excel',
        'text/csv'
    ]:
        raise get_error_response('GENERAL_004', {})

    orders_to_create, validation_errors, warning_mismatch = process_excel_orders(file, user)

    if validation_errors:
        return JsonResponse({'success': False, 'validation_errors': validation_errors, 'warning_mismatch': warning_mismatch}, status=400)

    for order_data, foreign_key_objects in orders_to_create:
        order_lines = order_data.pop('order_lines', [])
        order_values = {
            **order_data,
            **foreign_key_objects,
            'company': user.company
        }
        order = create_order(order_values, order_lines)
        create_log(
            model='Order',
            company=order.company,
            action='Imported using Excel',
            created_by=user.name,
            model_id=order.id
        )

    return JsonResponse({'success': True, 'message': _('All orders processed successfully')}, status=201)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['import_excel_order'])])
@parser_classes([MultiPartParser, FormParser])
@subscription_required
def test_import_excel(request):
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    if 'file' not in request.FILES:
        raise get_error_response('GENERAL_003', {'fields': ['file']})

    file = request.FILES['file']
    if file.content_type not in [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
        'application/vnd.ms-excel',
        'text/csv'
    ]:
        raise get_error_response('GENERAL_004', {})
    
    orders_to_create, validation_errors, warning_mismatch = process_excel_orders(file, user)
    return JsonResponse({'success': True, 'validation_errors': validation_errors, 'warning_mismatch': warning_mismatch}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_order_by_sequence_or_mobile(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    query = json_data.get('query')
    if not query:
        raise get_error_response('GENERAL_003', {'fields': ['query']})

    filter = Q()
    if query:
        filter |= Q(customer_mobile=query)
        filter |= Q(order_sequence=query)

    orders = Order.objects.filter(company=user.company).filter(filter).order_by('-order_sequence')
    serializer = OrderSerializer(orders, many=True)
    return JsonResponse({'success': True, 'orders': serializer.data}, status=200)

# This API is used by drivers for setting the driver note for an order 
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['edit_order_driver_note'])])
def set_order_driver_note(request):

    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    order_id = json_data.get('order_id')
    driver_note = json_data.get('driver_note')

    order = Order.objects.get(id=order_id, driver__id=user.id)
    if not order:
        raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'id', 'value': order_id})
    
    log = create_log('Order', user.company, 'update', user.name, order.id)
    create_log_info(log, 'driver_note', order.driver_note, driver_note)
    order.driver_note = driver_note
    order.save()

    serializer = OrderSerializer(order)
    return JsonResponse({'success': True, 'orders': serializer.data}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_branchpage'])])
def get_branch_pages(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('BranchPage', json_data, user)
    return JsonResponse(response_data, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_branchpage'])])
def create_branch_page(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})\
    
    values = json_data
    values['created_by'] = values['updated_by'] = user.name
    values['company'] = user.company
    branch_page = create_branch_page_util(json_data)
    serializer = BranchPageSerializer(branch_page)
    return JsonResponse({'success': True, 'branch_page': serializer.data}, status=201)
    
@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_branchpage'])])
def update_branch_page(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    values = json_data
    values['updated_by'] = user.name
    values['company'] = user.company
    branch_page = update_branch_page_util(json_data)
    serializer = BranchPageSerializer(branch_page)
    return JsonResponse({'success': True, 'branch_page': serializer.data}, status=201)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def resync_with_delivery_company(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    order_ids = json_data.get('order_ids', [])

    for order_id in order_ids:
        order = Order.objects.get(id=order_id)
        if order.olivery_sequence:
            if order.connection.delivery_company.is_olivery_company:
                connection_user = order.connection.credentials
                sync_existing_order(order, connection_user, user.company, order.connection.delivery_company, order.olivery_sequence)
            else:
                notification_details = []
                try:
                    sync_dynamic_order_status(order, notification_details)
                except Exception as e:
                    print(str(e))

    return JsonResponse({'success': True, 'message': _('Order Resynced Successfully')}, status=201)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_payment_link(request):
    json_data = parse_request_body(request)
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    id = json_data.get('id')
    provider = json_data.get('provider')
    try:
        order = Order.objects.get(id=id)
    except Order.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'id', 'value': id})
    
    FRONT_BASEURL = settings.FRONT_BASEURL
    success_url = f'{FRONT_BASEURL}/en-US/payment-success/'
    cancel_url = f'{FRONT_BASEURL}/en-US/payment-cancel/'
    if provider == 'Stripe':
        stripe_customer_id = create_or_get_stripe_customer(user.company)

        expiration_time = timezone.now() + timedelta(days=1)
        amount_in_smallest_unit = int(order.total_cod * 100)

        session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            mode='payment',
            line_items=[
                {
                    'price_data': {
                        'currency': order.currency.lower(),
                        'product_data': {
                            'name': f"Order Payment for order {order.order_sequence}",
                        },
                        'unit_amount': amount_in_smallest_unit,
                    },
                    'quantity': 1,
                }
            ],
            success_url=success_url,
            cancel_url=cancel_url,
            customer=stripe_customer_id,
            expires_at=int(expiration_time.timestamp()),
            metadata={
                'order_id': order.id,
                'is_client_payment': True,
                'is_connect': True
            }
        )
        url = session.url        
    elif provider == 'Lahza':
        if order.branch_page:
            page_name = order.branch_page.name
        else:
            page_name = order.company.company_name
        values = {
            "amount": order.total_cod * 100,
            "name": f"Order payment for order by {page_name}",
            "currency": order.currency.capitalize(),
            "description": f"Payment for order {order.order_sequence}",
            "redirect_url": success_url,
            "order_id": order.id,
            "metadata": {
                "order_id": order.id,
                "is_client_payment": True
            }
        }
        headers = {
            'Authorization': f'Bearer {settings.LAHZA_TEST_SECRET_KEY}',
            'Content-Type': 'application/json'
        }
        response = requests.post(url='https://api.lahza.io/page', headers=headers, json=values)
        if response.status_code == 200:
            result = response.json()
            slug = result.get('data').get('slug')
            url = f'https://pay.lahza.io/{slug}'
        else:
            raise get_error_response('GENERAL_009', {})
    else:
        raise get_error_response('GENERAL_009', {})
    return JsonResponse({'success': True, 'url': url}, status=200)
    
@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def sync_delivery_fees(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    order_ids = json_data.get('order_ids', [])
    sync_delivery_fees_util(order_ids)
    return JsonResponse({'success': True, 'message': _('Delivery Fees Synced Successfully')}, status=201)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_orderdeliverystatushistory'])])
def get_previous_states(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('OrderDeliveryStatusHistory', json_data, user, company_filter=Q(order__company=user.company))
    return JsonResponse(response_data, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['view_stuckreason'])])
def get_stuck_reasons(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('StuckReason', json_data, user)
    return JsonResponse(response_data, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_stuckreason'])])
def create_stuck_reason(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    values = {**json_data, 'company': user.company}
    stuck_reason = StuckReason.objects.create(**values)
    serializer = StuckReasonSerializer(stuck_reason)
    return JsonResponse({'success': True, 'stuck_reason': serializer.data}, status=200)

@csrf_exempt
@api_view(['PUT'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_stuckreason'])])
def update_stuck_reason(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    values = {**json_data, 'company': user.company}
    id = values.get('id')
    StuckReason.objects.filter(id=id).update(**values)
    serializer = StuckReasonSerializer(StuckReason.objects.get(id=id))
    return JsonResponse({'success': True, 'stuck_reason': serializer.data}, status=200)


@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
def get_order_from_ai(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    
    try:
        feature = CompanyFeature.objects.get(feature__key='entry_agent', company=user.company, enabled=True)
    except CompanyFeature.DoesNotExist:
        raise get_error_response('FEATURE_1400', {'feature': _('Entry Agent')})
    
    texts = json_data.get('texts')
    if not texts:
        raise get_error_response('GENERAL_003', {'fields': 'texts'})
    url = 'https://data-entry-ai.olivery.io/api/process-text'
    result = {}

    company_conf = CompanyConf.objects.get(company=user.company)
    default_warehouse = WarehouseSerializer(company_conf.default_warehouse).data if company_conf.default_warehouse else None
    response = requests.post(url,
                             json={
                                 "texts": texts,
                                 'company_id': user.company.company_id,
                                 'warehouse_id': default_warehouse["id"],
                                 'environment': settings.BASEURL,
                                 }
                            )
    if response.status_code == 200:
        results = response.json()
        processed_results = []
        #here to get default warehouse
        for result in results:
            result['is_fixed_total'] = True
            result['area'] = result.pop('area_text', None)
            result['sub_area'] = result.pop('sub_area_text', None)

            try:
                area_instance = get_area(result, [])
            except Exception as e:
                area_instance = None
            try:
                sub_area_instance = get_sub_area(result, [], area_instance)
            except Exception as e:
                sub_area_instance = None

            result['area'] = AreaSerializer(area_instance).data if area_instance else None
            result['sub_area'] = SubAreaSerializer(sub_area_instance).data if sub_area_instance else None
            result['country'] = CountrySerializer(area_instance.country).data if area_instance else None
            result['warehouse'] = default_warehouse
            valid_order_lines = []
            for orderline in result.get('order_lines', []):
                variant = WarehouseVariant.objects.filter(id=orderline['product_variant'], variant__product__company=user.company).first()
                orderline['product_variant'] = WarehouseVariantSerializer(variant).data if variant else None

                product = Product.objects.filter(id=orderline['product'], company=user.company).first()
                orderline['product'] = ProductSerializer(product).data if product else None
                if orderline['product_variant'] and orderline['product']:
                    valid_order_lines.append(orderline)
            result['order_lines'] = valid_order_lines

            if not result['sub_area'] and result['area']:
                try:
                    default_sub_area = SubArea.objects.get(area=area_instance, is_default_for_area=True)
                    result['sub_area'] = SubAreaSerializer(default_sub_area).data
                except SubArea.DoesNotExist:
                    print(f"No default sub_area found for area ID {result['area']}")

            for key in ['fallback_used', 'fast_path', 'processed_at', 'request_id', 'processing_time_seconds']:
                result.pop(key, None)

            processed_results.append(result)
    else:
        raise get_error_response('GENERAL_011', {'status_code': response.status_code})

    return JsonResponse({'orders': processed_results}, status=200)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['add_order'])])
def duplicate_order(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    order_id = json_data.get('order_id')
    if not order_id:
        raise get_error_response('GENERAL_003', {'fields': 'order_id'})

    try:
        order = Order.objects.get(id=order_id, company=user.company)
    except Order.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'id', 'value': order_id})
    
    new_order = duplicate_order_util(order, user)
    
    serializer = OrderSerializer(new_order)
    return JsonResponse({'success': True, 'order': serializer.data}, status=201)

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['send_orders_to_delivery_company'])])
def resend_order(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})
    order_id = json_data.get('order_id')
    if not order_id:
        raise get_error_response('GENERAL_003', {'fields': 'order_id'})

    try:
        order = Order.objects.get(id=order_id, company=user.company)
    except Order.DoesNotExist:
        raise get_error_response('GENERAL_002', {'model': 'Order', 'field': 'id', 'value': order_id})
    
    new_order = duplicate_order_util(order, user)
    success_messages = []
    fail_messages = []
    delivery_request_status = get_company_delivery_request_status(user.company)
    send_json = {
        'order_ids': [new_order.id],
        'connection': new_order.connection.id if new_order.connection else None,
        'driver_id': new_order.driver.id if new_order.driver else None,
    }
    process_sending_orders([send_json], user, delivery_request_status, success_messages, fail_messages)
    return JsonResponse({
        'success': True, 
        'success_messages': success_messages,
        'fail_messages': fail_messages,
        'order_id': new_order.id,
    }, status=200)
