from django.urls import path
from .developer_connection_apis import *

urlpatterns = [
    path('get_developer_connections', get_developer_connections, name='get_developer_connections'),
    path('add_developer_connection', add_developer_connection, name='add_developer_connection'),
    path('generate_developer_connection_token', generate_developer_connection_token, name='generate_developer_connection_token'),
    path('get_developer_connection_webhooks', get_developer_connection_webhooks, name='get_developer_connection_webhooks'),
    path('set_webhook', set_webhook, name='set_webhook'),
    path('test_webhook', test_webhook, name='test_webhook'),
    path('get_webhook_logs', get_webhook_logs, name='get_webhook_logs'),
    path('get_webhook_log_summation', get_webhook_log_summation, name='get_webhook_log_summation'),
]