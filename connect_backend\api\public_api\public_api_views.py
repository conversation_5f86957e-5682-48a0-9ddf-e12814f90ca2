# Django imports
from django.conf import settings
from django.core.cache import cache
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

# REST framework imports
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.permissions import AllowAny

# Standard library imports
import logging
import re

# Local imports
from .public_api_model import ApiProducts
from .public_api_serializers import PublicProductSerializer, BestMatchSearchSerializer
from ..products.product_model import Product
from ..warehouse.warehouse_model import WarehouseVariant
from ..auth.auth_model import NoAuthentication
from ..util_functions import parse_request_body

logger = logging.getLogger(__name__)

# Using NoAuthentication from auth_model.py and parse_request_body from util_functions.py

@csrf_exempt
@api_view(['POST', 'GET'])
@authentication_classes([NoAuthentication])
@permission_classes([AllowAny])
def get_best_match_product(request):
    """
    Get the single best matching product using advanced PostgreSQL text search capabilities.
    Always returns a product even if no exact match is found.
    
    Supports both GET and POST methods:
    - GET: query parameter in URL
    - POST: JSON body with search parameters
    
    Parameters:
        query (str): Search query string
        company_id (int): Company ID to filter products
        warehouse_id (int, optional): Warehouse ID to filter product variants by warehouse
        use_fuzzy (bool, optional): Enable more intensive fuzzy matching (default: True)

    Returns:
        JSON response with the best matching product (guaranteed to return a product) including:
        - product_id: ID of the best matching product
        - variant_id: ID of the best matching variant
        - warehouse_variant_id: ID of the warehouse variant (when warehouse_id is provided)
        - match_info: Details about the match quality and strategy used
    """
    try:
        # Handle both GET and POST requests
        if request.method == 'GET':
            query = request.GET.get('query', '').strip()
            company_id = request.GET.get('company_id')
            warehouse_id = request.GET.get('warehouse_id')
            use_fuzzy = request.GET.get('use_fuzzy', 'true').lower() == 'true'  # Default to true

            data = {
                'query': query,
                'company_id': company_id,
                'warehouse_id': warehouse_id,
                'use_fuzzy': use_fuzzy
            }
        else:  # POST
            json_data = parse_request_body(request)
            if json_data is None:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid JSON in request body'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            data = json_data
            # Default to true if not specified in POST data
            if 'use_fuzzy' not in data:
                data['use_fuzzy'] = True

        # Validate input
        serializer = BestMatchSearchSerializer(data=data)
        if not serializer.is_valid():
            return JsonResponse({
                'success': False,
                'error': 'Invalid search parameters',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        validated_data = serializer.validated_data
        query = validated_data['query']
        company_id = validated_data['company_id']
        warehouse_id = validated_data.get('warehouse_id')
        use_fuzzy = validated_data['use_fuzzy']

        # Create cache key for the search (include warehouse_id in the cache key)
        cache_key = f"public_guaranteed_best_match:{hash(query)}:{company_id}:{warehouse_id}:{use_fuzzy}"
        
        # Try to get from cache first (with short TTL for freshness)
        cached_result = cache.get(cache_key)
        if cached_result:
            return JsonResponse(cached_result, status=status.HTTP_200_OK)
        
        # Perform guaranteed best match search
        # The result is a tuple (product, match_info)
        best_match, match_info = ApiProducts.get_guaranteed_best_match(query, company_id, warehouse_id)
        
        # Handle case where no products are found for this company
        if best_match is None:
            return JsonResponse({
                'success': False,
                'error': 'No products available',
                'message': match_info.get('reason', 'No active products found for this company'),
                'match_info': {
                    'query': query,
                    'match_type': match_info.get('match_type', 'no_products'),
                    'confidence_score': 0.0,
                    'search_term': match_info.get('search_term', query)
                }
            }, status=status.HTTP_404_NOT_FOUND)
        
        # If fuzzy is disabled and we got a fuzzy match, try to get a more relevant product
        if not use_fuzzy and match_info.get('match_type') in ['fuzzy', 'trigram', 'token', 'fallback']:
            # Try more strict matching
            full_text_match = ApiProducts.get_best_match(query, company_id)
            if full_text_match:
                best_match = full_text_match
                match_info = {
                    'match_type': 'full_text',
                    'confidence': 0.9,
                    'search_term': query
                }

        # Extract and improve match information
        match_confidence = match_info.get('confidence', 0) * 100
        match_type = match_info.get('match_type', 'unknown')
        is_arabic = match_info.get('is_arabic', False)
        
        # Customize message based on match quality
        if match_confidence > 80:
            message = 'Found perfect matching product'
        elif match_confidence > 60:
            message = 'Found good matching product'
        elif match_confidence > 40:
            message = 'Found somewhat relevant product'
        else:
            message = 'Found best available product'

        # Serialize the product for the response
        product_serializer = PublicProductSerializer(best_match, context={'request': request})
        
        # Determine the best matching variant if any
        best_variant_id = None
        best_warehouse_variant_id = None  
        best_variant_match_score = 0
        
        # Check if we have size match information
        size_match = match_info.get('size_match')
        
        # If we have variants, find the best matching one
        if best_match.variants.exists():
            normalized_query = match_info.get('search_term', query)
            
            for variant in best_match.variants.all():
                # Calculate similarity score for this variant
                variant_score = ApiProducts._calculate_character_similarity(
                    normalized_query, variant.name.lower()
                )
                
                # If we have a size match, prioritize variants with that size
                if size_match and size_match.lower() in variant.name.lower():
                    variant_score += 0.3  # Boost score for size match
                
                if variant_score > best_variant_match_score:
                    best_variant_match_score = variant_score
                    best_variant_id = variant.id
                    
                    # Get the warehouse variant ID based on the specified warehouse_id
                    # The WarehouseVariant model maps to the api_productvariant_warehouses table
                    warehouse_filter = {
                        'variant': variant
                    }
                    
                    # If a specific warehouse is requested, filter by it
                    if validated_data.get('warehouse_id'):
                        warehouse_filter['warehouse_id'] = validated_data['warehouse_id']
                    
                    # Get the warehouse variant with the highest ID (most recent) for this variant
                    warehouse_variant = WarehouseVariant.objects.filter(**warehouse_filter).order_by('-id').first()
                    
                    if warehouse_variant:
                        # This is the ID from the api_productvariant_warehouses table
                        # It matches the 'id' column in the database table
                        best_warehouse_variant_id = warehouse_variant.id
        
        # Create a structure for match information
        best_match_info = {
            'query': query,
            'match_type': match_type,
            'confidence_score': round(match_confidence, 1),
            'product_id': best_match.id,
            'product_name': best_match.name,
            'is_arabic': is_arabic
            # Removed api_productvariant_warehouses_id as it's now included in the variant id
        }
        
        response_data = {
            'success': True,
            'message': message,
            'result': product_serializer.data,
            'match_info': best_match_info
        }
        
        # Add any additional match info that was provided
        for key, value in match_info.items():
            if key not in ['match_type', 'confidence', 'product_id', 'product_name', 'is_arabic']:
                response_data['match_info'][key] = value

        # Cache the result for 5 minutes
        cache.set(cache_key, response_data, 300)

        return JsonResponse(response_data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Error in get_best_match_product: {str(e)}", exc_info=True)
        return JsonResponse({
            'success': False,
            'error': 'Internal server error',
            'message': 'An error occurred while searching for the best match product'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
