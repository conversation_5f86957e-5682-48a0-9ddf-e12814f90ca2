# REST framework imports
from rest_framework import serializers

# Local imports
from ..category.category_model import Category
from ..products.product_model import Product, ProductVariant, ProductsImage


class PublicProductImageSerializer(serializers.ModelSerializer):
    """Serializer for product images in public API"""
    class Meta:
        model = ProductsImage
        fields = ['id', 'product_image_url']


class PublicCategorySerializer(serializers.ModelSerializer):
    """Serializer for category in public API"""
    class Meta:
        model = Category
        fields = ['id', 'name']


class PublicProductVariantSerializer(serializers.ModelSerializer):
    """Serializer for product variants in public API"""
    variant_image_url = serializers.SerializerMethodField()
    id = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductVariant
        fields = [
            'id', 'name', 'sku', 'variant_image_url', 
            'delivery_attempt_count', 'successful_delivery_count'
        ]
    
    def get_variant_image_url(self, obj):
        """Get variant image URL"""
        # Return the variant image URL if available
        if hasattr(obj, 'image') and obj.image:
            return obj.image.url if hasattr(obj.image, 'url') else None
        return None
        
    def get_id(self, obj):
        """Get warehouse variant ID instead of variant ID"""
        # Get the request from the context if available
        request = self.context.get('request')
        warehouse_id = None
        
        # Try to get warehouse_id from request
        if request:
            if request.method == 'GET':
                warehouse_id = request.GET.get('warehouse_id')
            elif request.method == 'POST':
                warehouse_id = request.data.get('warehouse_id')
                
        # If we have a warehouse_id, return the warehouse variant ID
        if warehouse_id:
            from ..warehouse.warehouse_model import WarehouseVariant
            warehouse_variant = WarehouseVariant.objects.filter(
                variant=obj,
                warehouse_id=warehouse_id
            ).order_by('-id').first()
            
            if warehouse_variant:
                return warehouse_variant.id
                
        # Fall back to the original variant ID if no warehouse variant found
        return obj.id


class PublicProductSerializer(serializers.ModelSerializer):
    """Serializer for products in public API without sensitive information"""
    images = PublicProductImageSerializer(many=True, read_only=True)
    category = PublicCategorySerializer(read_only=True)
    variants = PublicProductVariantSerializer(many=True, read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id', 'name', 'price', 'description', 'product_sequence',
            'reference_sequence', 'active', 'publish', 'category',
            'virtual_stock', 'physical_stock', 'images', 'variants'
        ]

    def to_representation(self, instance):
        """Custom representation to ensure only public data is exposed"""
        representation = super().to_representation(instance)
        
        # Remove cost and other sensitive fields if accidentally included
        sensitive_fields = ['cost', 'company', 'created_by', 'updated_by']
        for field in sensitive_fields:
            representation.pop(field, None)
            
        return representation


class BestMatchSearchSerializer(serializers.Serializer):
    """Serializer for best match search input validation"""
    query = serializers.CharField(
        max_length=200, 
        required=True,
        help_text="Search query for finding the best matching product"
    )
    company_id = serializers.IntegerField(
        required=True,
        help_text="Company ID to filter products"
    )
    warehouse_id = serializers.IntegerField(
        required=False,
        allow_null=True,
        help_text="Warehouse ID to filter product variants by warehouse"
    )
    use_fuzzy = serializers.BooleanField(
        default=True,
        required=False,
        help_text="Use advanced fuzzy matching techniques to find closest matches"
    )
