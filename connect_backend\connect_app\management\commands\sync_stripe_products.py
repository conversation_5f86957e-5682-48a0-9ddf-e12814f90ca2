from django.core.management.base import BaseCommand
from django.conf import settings
import stripe
from api.billing.billing_model import Package, StripeProduct  # Adjust the import based on your actual project structure

stripe.api_key = settings.STRIPE_TEST_SECRET_KEY

class Command(BaseCommand):
    help = 'Sync products and prices from Stripe'

    def handle(self, *args, **kwargs):
        products = stripe.Product.list()
        
        for product in products.auto_paging_iter():
            prices = stripe.Price.list(product=product.id)
            for price in prices.auto_paging_iter():
                # Create or update the StripeProduct instance
                StripeProduct.objects.update_or_create(
                    stripe_product_id=price.id,
                    defaults={
                        'name': product.name,
                        'description': product.description,
                        'price': price.unit_amount / 100,
                        'currency': price.currency,
                        'recurring_interval': price.recurring['interval'] if price.recurring else None,
                    }
                )
        
        self.stdout.write(self.style.SUCCESS('Successfully synced products and prices from <PERSON><PERSON>'))
