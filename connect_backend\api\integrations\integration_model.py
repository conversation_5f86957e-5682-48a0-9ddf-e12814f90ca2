from django.db import models
from ..users.user_model import Company
from ..models import SuperModel

class EcommerceToken(SuperModel):

    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    ecommerce = models.CharField(max_length=200, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()

class WoocommerceInformation(SuperModel):

    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)    
    consumer_key =  models.CharField(max_length=200, null=True)
    consumer_secret =  models.CharField(max_length=200, null=True)
    woocommerce_url = models.CharField(max_length=200, null=True)

