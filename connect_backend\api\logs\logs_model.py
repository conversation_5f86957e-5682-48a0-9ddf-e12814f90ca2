from django.db import models
from ..users.user_model import Company
from ..orders.order_model import NOTE_INFO_MAX_LENGHT

class Logs(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True, db_index=True)
    model = models.CharField(max_length=200, db_index=True)
    action = models.CharField(max_length=200)
    model_id = models.IntegerField(null=False, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.CharField(max_length=200)

    class Meta:
        indexes = [
            models.Index(fields=['company','model', 'model_id']),
        ]

class LogsInfo(models.Model):
    field_name = models.CharField(max_length=200, db_index=True)
    old_value = models.CharField(max_length=NOTE_INFO_MAX_LENGHT, null=True)
    new_value = models.CharField(max_length=NOTE_INFO_MAX_LENGHT, null=True)
    log = models.ForeignKey(Logs, on_delete=models.CASCADE, db_index=True)

    class Meta:
        indexes = [
            models.Index(fields=['log']),
        ]

class TrackedFields(models.Model):
    company = models.ForeignKey(Company, on_delete=models.CASCADE, null=True)
    field_name = models.CharField(max_length=200, null=False)
    model = models.CharField(max_length=200, null=False)
    

