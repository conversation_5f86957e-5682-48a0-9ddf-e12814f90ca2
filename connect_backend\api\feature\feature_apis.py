from django.http import JsonResponse
from rest_framework.decorators import *
from django.views.decorators.csrf import csrf_exempt
from ..permissions import *
from rest_framework.permissions import AllowAny
from ..util_functions import *
from .feature_model import CompanyFeature, Feature

@csrf_exempt
@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([get_permission_class(['change_companyfeature'])])
def set_company_feature(request):

    json_data = parse_request_body(request)
    if json_data is None:
        return JsonResponse({'success': False, 'error': 'Invalid JSON format in request body'}, status=400)

    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    feature = Feature.objects.filter(key=json_data.get('feature')).first()
    company_feature, created = CompanyFeature.objects.update_or_create(
        company=user.company,
        feature=feature,
        defaults={'enabled': json_data.get('enabled')})
    serializer = CompanyFeatureSerializer(company_feature)
    response = {
        'success': True,
        'company_feature': serializer.data,
        'message': 'Company Feature set successfully'
    }
    return JsonResponse(response, status=200)

@api_view(['POST'])
@authentication_classes([CustomJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_company_features(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    try:
        user = User.objects.get(user=request.user)
    except User.DoesNotExist:
        raise get_error_response('AUTH_400', {'username': request.user.username})

    response_data = get_records('CompanyFeature', json_data, user, custom_filter=Q(enabled=True))
    return JsonResponse(response_data, status=200)
