from ..orders.order_model import *
from django.core.exceptions import ValidationError
from ..util_functions import *
from ..orders.order_utils import *
from django.db import transaction
from api.classes import Whatsapp
from ..auth.auth_utils import *
from django.utils.translation import gettext as _
from django.utils import translation
from django.core.files.storage import default_storage
from django.db.models import Count, OuterRef, Subquery, CharField

def update_status_map_util(values, updated_by):
    try:
        status_map = StatusMap.objects.get(id=values['id'])
    except StatusMap.DoesNotExist:
        raise ValidationError("Status map not found")
    
    try:
        status = Status.objects.get(code=values['status'])
    except Status.DoesNotExist:
        raise ValidationError("Status not found")
    
    values['status'] = status.name
    
    log = create_log('StatusMap', None, 'update', updated_by, status_map.id)
    try:
        with transaction.atomic():
            lookup = update_status_map_fields(status_map, values, log)
    except Exception as e:
        delete_log_if_no_changes(log)
        raise ValidationError('Status Map was not updated due to' + str(e))
    delete_log_if_no_changes(log)
    lookup.save()  

def update_status_map_fields(status_map, values, log):
    fields = get_tracked_fields(None, 'StatusMap')

    for key, value in values.items():
        old_value = getattr(status_map, key, None)
        if old_value != value:
            log_and_update_field(status_map, key, value, old_value, log, bool(key in fields))

    return status_map

def create_status_map(values, created_by):
    try:
        status = Status.objects.get(code=values['status'])
    except Status.DoesNotExist:
        raise ValidationError("Status not found")
    
    values['status'] = status.name

    try:
        with transaction.atomic():
            status_map = StatusMap.objects.create(**values)
            log = create_log('StatusMap', None, 'create', created_by, status_map.id)
            fields = get_tracked_fields(None, 'StatusMap')
            log_tracked_fields(log, values, fields)
            delete_log_if_no_changes(log)
    except Exception as e:
        raise ValidationError(f"Error creating status map: {str(e)}")
    return status_map
    
def update_area_maps(area_map, delivery_company):
    try:
        with transaction.atomic():
            for item in area_map:
                id = item.get('id')
                area_id = item.get('area')
                imported_area = item.get('imported_area')
                imported_area_id = item.get('imported_area_id')

                if id:
                    area_map_instance = update_existing_area_map(id, area_id, imported_area, imported_area_id)
                else:
                    area_map_instance = create_new_area_map(area_id, imported_area, delivery_company, imported_area_id)

                if area_map_instance is None:
                    return {'success': False, 'error': 'Error processing area map item'}

    except Exception as e:
        return {'success': False, 'error': f'Failed to sync delivery areas: {str(e)}'}

    return {'success': True, 'message': 'Area Map updated successfully'}

def update_existing_area_map(area_map_id, area_id, imported_area, imported_area_id):
    try:
        area_map_instance = AreaMap.objects.get(id=area_map_id)
        
        try:
            area = Area.objects.get(id=area_id)
        except Area.DoesNotExist:
            raise Exception(f'Area not found with area_id: {area_id}')

        area_map_instance.area = area
        area_map_instance.imported_area = imported_area
        area_map_instance.imported_area_id = imported_area_id
        area_map_instance.save()
        return area_map_instance

    except AreaMap.DoesNotExist:
        return None
    except Exception as e:
        return None
    
def create_new_area_map(area_id, imported_area, delivery_company, imported_area_id):
    try:
        area = Area.objects.get(id=area_id)
    except Area.DoesNotExist:
        raise Exception(f'Area not found with area_id: {area_id}')

    return AreaMap.objects.create(area=area, imported_area=imported_area, delivery_company=delivery_company, imported_area_id=imported_area_id)

def update_subarea_maps(area_map, delivery_company):
    try:
        with transaction.atomic():
            for item in area_map:
                id = item.get('id')
                subarea_id = item.get('sub_area')
                imported_sub_area = item.get('imported_sub_area')
                imported_sub_area_id = item.get('imported_sub_area_id')
                parent_area_name = item.get('parent_area_name')
                parent_area_id = item.get('parent_area_id')

                if id:
                    area_map_instance = update_existing_subarea_map(id, subarea_id, imported_sub_area, imported_sub_area_id, parent_area_name, parent_area_id)
                else:
                    area_map_instance = create_new_subarea_map(subarea_id, imported_sub_area, delivery_company, imported_sub_area_id, parent_area_name, parent_area_id)

                if area_map_instance is None:
                    return {'success': False, 'error': 'Error processing sub area map item'}

    except Exception as e:
        return {'success': False, 'error': f'Failed to sync delivery areas: {str(e)}'}

    return {'success': True, 'message': 'Sub Area Map updated successfully'}

def update_existing_subarea_map(area_map_id, sub_area_id, imported_sub_area, imported_sub_area_id, parent_area_name, parent_area_id):
    try:
        sub_area_map_instance = SubAreaMap.objects.get(id=area_map_id)
        
        try:
            sub_area = SubArea.objects.get(id=sub_area_id)
        except SubArea.DoesNotExist:
            raise Exception(f'SubArea not found with sub_area_id: {sub_area_id}')

        sub_area_map_instance.sub_area = sub_area
        sub_area_map_instance.imported_sub_area = imported_sub_area
        sub_area_map_instance.imported_sub_area_id = imported_sub_area_id
        sub_area_map_instance.parent_area_name = parent_area_name
        sub_area_map_instance.parent_area_id = parent_area_id
        sub_area_map_instance.save()
        return sub_area_map_instance

    except SubAreaMap.DoesNotExist:
        return None
    except Exception as e:
        return None
    
def create_new_subarea_map(sub_area_id, imported_sub_area, delivery_company, imported_sub_area_id, parent_area_name, parent_area_id):
    try:
        subarea = SubArea.objects.get(id=sub_area_id)
    except SubArea.DoesNotExist:
        raise Exception(f'Sub Area not found with sub_area_id: {sub_area_id}')

    return SubAreaMap.objects.create(sub_area=subarea, imported_sub_area=imported_sub_area, delivery_company=delivery_company, imported_sub_area_id=imported_sub_area_id, parent_area_name=parent_area_name, parent_area_id=parent_area_id)

def update_or_create_area_map(imported_areas, delivery_company):
    areas = Area.objects.filter(country=delivery_company.country)

    for area in areas:
        for imported_area in imported_areas:
            if area.name == imported_area.get('name'):
                imported_area_value = imported_area.get('name')
                imported_area_id = imported_area.get('id')
                area_map, created = AreaMap.objects.get_or_create(
                    delivery_company=delivery_company,
                    area=area
                )
                if not area_map.imported_area and imported_area_value:
                    area_map.imported_area = imported_area_value
                    area_map.imported_area_id = imported_area_id
                    area_map.save()

def update_or_create_sub_area_map(imported_sub_areas, delivery_company):
    sub_areas = SubArea.objects.filter(area__country=delivery_company.country)
    
    for sub_area in sub_areas:
        for imported_sub_area in imported_sub_areas:
            if sub_area.name == imported_sub_area.get('name'):
                imported_sub_area_value = imported_sub_area.get('name')
                imported_sub_area_id = imported_sub_area.get('id')
                parent_area_name = imported_sub_area.get('parent_area_name')
                parent_area_id = imported_sub_area.get('parent_area_id')
                sub_area_map, created = SubAreaMap.objects.get_or_create(
                    delivery_company=delivery_company,
                    sub_area=sub_area
                )
                if not sub_area_map.imported_sub_area and imported_sub_area_value:
                    sub_area_map.imported_sub_area = imported_sub_area_value
                    sub_area_map.imported_sub_area_id = imported_sub_area_id
                    sub_area_map.parent_area_name = parent_area_name
                    sub_area_map.parent_area_id = parent_area_id
                    sub_area_map.save()

def match_area(area_name):
    if not area_name:
        return None, 0

    area_names = list(Area.objects.values_list('name', flat=True)) + ['رام الله', 'البيرة']
    area_best_match, area_score = fuzzy_search(area_name, 'Area', filter=Q(area_name_arabic__in=area_names))

    area = Area.objects.filter(name=area_best_match).first()
    return area, area_score

def match_sub_area(sub_area_name, area):
    if not sub_area_name:
        return None, 0

    sub_area_names = SubArea.objects.filter(area=area).values_list('name', flat=True) if area else SubArea.objects.values_list('name', flat=True)
    sub_best_match, sub_area_score = fuzzy_search(sub_area_name, 'SubArea', filter=Q(sub_area_name_arabic__in=sub_area_names))

    sub_area = SubArea.objects.filter(area=area, name=sub_best_match).last() if area else SubArea.objects.filter(name=sub_best_match).first()
    return sub_area, sub_area_score

def auto_map_statuses_util(delivery_company):
    statuses = []
    if delivery_company.is_olivery_company:
        params = {
            "jsonrpc": "2.0",
            "params": {

            }
        }
        response = send_api_to_delivery_company(delivery_company, '/connection/get_statuses', params)
        if response:
            code = response['code']
            if code == 200:
                result = response['result']
                statuses = result.get('response', [])
            else:
                return {'success': False, 'error': f'Error getting statuses from delivery company: {response.get("message") or "Unknown error"}'}
    else:
        request_template = RequestTemplate.objects.get(delivery_company=delivery_company, name='get_statuses')
        params = build_request_params(request_template, None)
        response = send_dynamic_api_request(request_template, params)
        statuses = response.get('data')
    if not statuses:
        return {'success': False, 'error': f'Error getting statuses from delivery company: {response.get("message") or "Unknown error"}'}
    for status in statuses:
        code = status.get('status_code').lower()
        name = status.get('delivery_company_status')
        try:
            status_map = StatusMap.objects.get(status_code=code)
        except StatusMap.DoesNotExist:
            status_map = None
        try:
            existing_status_map = DeliveryStatusMap.objects.get(imported_status_code=code, delivery_company=delivery_company)
        except DeliveryStatusMap.DoesNotExist:
            existing_status_map = None
        if not existing_status_map or not existing_status_map.status:
            DeliveryStatusMap.objects.update_or_create(
                imported_status_code=code,
                delivery_company=delivery_company,
                defaults={'status': status_map, 'imported_status_name': name}
            )

def send_activate_message(user):
    lang = user.lang
    if not lang:
        lang = 'en-us'
    translation.activate(lang)  
    message = _(
        "Welcome to Connect Plus!\n"
        "You can log in using the link below:\n"
        "connect-plus.app/login\n"
        "We wish you all the best on your journey with us!"
    )
    print(message)
    if user.country_code in ['+972', '+970']:
        for specific_code in ['+972', '+970']:
            refined_number = Whatsapp.refine_mobile_number(specific_code, user.mobile_number)
            send_message_and_log(refined_number,message)
    else:
        full_mobile_number = Whatsapp.refine_mobile_number(user.country_code, user.mobile_number)
        send_message_and_log(full_mobile_number, message)

def apply_field_updates(update_fields, reseller):
    for field_update in update_fields:
        field_name = field_update.field_name
        new_value = field_update.new_value
        new_value_id = field_update.new_value_id
        field = Reseller._meta.get_field(field_name)

        if field.is_relation and field.many_to_one:
            if new_value_id:
                related_model = field.related_model
                related_instance = related_model.objects.get(id=new_value_id)
                setattr(reseller, field_name, related_instance)
            else:
                setattr(reseller, field_name, None)
        elif isinstance(field, models.ImageField) and new_value and "temp/" in new_value:
            finalize_image_update(reseller, field_update)
        else:
            setattr(reseller, field_name, new_value)
    reseller.save()

def finalize_image_update(reseller, update_field):
    if update_field.new_value:
        temp_path = update_field.new_value.split('temp/')[1]
        temp_file_path = f"temp/{temp_path}"
        if not default_storage.exists(temp_file_path):
            raise FileNotFoundError(f"Temporary file not found: {temp_file_path}")
        with default_storage.open(temp_file_path, 'rb') as temp_file:
            final_path = f"reseller/{update_field.field_name}/{reseller.id}.jpg"
            final_file_path = default_storage.save(final_path, temp_file)
        setattr(reseller, update_field.field_name, final_file_path)
        reseller.save()
        default_storage.delete(temp_file_path)

def update_pricelist_items(pricelist_items_updates, reseller):
    for item_update in pricelist_items_updates:
        ResellerPricelistItem.objects.update_or_create(
            reseller=reseller,
            from_area=item_update.from_area,
            to_area=item_update.to_area,
            defaults={'price': item_update.price}
        )

def update_branches(branch_updates, reseller):
    for branch_update in branch_updates:
        DeliveryCompanyBranch.objects.update_or_create(
            reseller=reseller,
            name=branch_update.name
        )

def get_on_boarding_filters(json_data):
    is_subscribed = json_data.get('is_subscribed', 'all')
    last_order_date = json_data.get('last_order_date', None)
    min_order_count = json_data.get('min_order_count', 0)

    if is_subscribed == 'all' and not last_order_date and not min_order_count:
        return None

    companies = Company.objects.all()

    if is_subscribed != 'all':
        companies = get_is_subscribed_filter(companies, is_subscribed)

    if last_order_date:
        companies = get_last_order_date_filter(companies, last_order_date)
        if min_order_count:
            companies = get_min_order_count_filter(companies, last_order_date, min_order_count)
    elif min_order_count:
        companies = get_min_order_count_filter(companies, timezone.now().date, min_order_count)

    company_ids = list(companies.values_list('company_id', flat=True))

    return company_ids

def get_min_order_count_filter(companies, date, min_order_count):
    try:
        date = datetime.datetime.strptime(date, "%Y-%m-%d")
    except ValueError:
        raise get_error_response('GENERAL_007', {'error': 'Invalid date format'})
    # Only consider orders after the given date (not 30 days before)
    eligible_company_ids = Order.objects.filter(
        created_at__gte=date
    ).values('company_id').annotate(
        order_count=Count('id')
    ).filter(order_count__gte=min_order_count).values_list('company_id', flat=True)

    companies = companies.filter(company_id__in=eligible_company_ids)

    return companies

def get_last_order_date_filter(companies, date):
    try:
        last_order_date = datetime.datetime.strptime(date, "%Y-%m-%d")
    except ValueError:
        raise get_error_response('GENERAL_007', {'error': 'Invalid date format'})

    latest_order_subquery = Order.objects.filter(
        company_id=OuterRef('pk')
    ).order_by('-created_at').values('created_at')[:1]
    
    companies = companies.annotate(
        latest_order_date=Subquery(latest_order_subquery)
    ).filter(
        latest_order_date__date__gte=last_order_date
        )
    
    return companies
    
def get_is_subscribed_filter(companies, is_subscribed):
    # Subquery: latest subscription with a stripe_subscription_id (or not)
    latest_subscription_qs = Subscription.objects.filter(
        company=OuterRef('pk')
    ).order_by('-end_date')

    latest_stripe_subscription_id = Subquery(
        latest_subscription_qs.values('stripe_subscription_id')[:1],
        output_field=CharField()
    )

    companies = companies.annotate(
        latest_stripe_subscription_id=latest_stripe_subscription_id
    )

    if is_subscribed == 'subscribed':
        companies = companies.filter(
            Q(latest_stripe_subscription_id__isnull=False) & ~Q(latest_stripe_subscription_id='')
        )
    elif is_subscribed == 'not_subscribed':
        companies = companies.filter(
            Q(latest_stripe_subscription_id__isnull=True) | Q(latest_stripe_subscription_id='')
        )

    return companies

def apply_integration_template_to_company(integration_template, delivery_company, meta_values):
    from copy import deepcopy
    old_to_new = {}
    RequestTemplatePrerequisite.objects.filter(request_template__delivery_company=delivery_company).delete()
    ResponseTemplate.objects.filter(request_template__delivery_company=delivery_company).delete()
    RequestTemplate.objects.filter(delivery_company=delivery_company).delete()
    # First pass: create all new RequestTemplates and map old ids to new
    for req in integration_template.request_templates.all():
        req_data = deepcopy(req.__dict__)
        req_data.pop('_state', None)
        req_data.pop('delivery_company_id', None)
        req_data.pop('integration_template_id', None)
        req_data.pop('id', None)
        req_data['delivery_company'] = delivery_company
        req_data['integration_template'] = None
        for field in ['headers', 'body']:
            if isinstance(req_data[field], dict):
                for k, v in req_data[field].items():
                    if isinstance(v, str):
                        for meta_k, meta_v in meta_values.items():
                            if v == meta_k.upper():
                                req_data[field][k] = meta_v
        new_req = RequestTemplate.objects.create(**{k: v for k, v in req_data.items() if k not in ['id']})
        old_to_new[req.id] = new_req
        if hasattr(req, 'response_template'):
            resp = req.response_template
            resp_data = deepcopy(resp.__dict__)
            resp_data.pop('_state', None)
            resp_data.pop('id', None)
            resp_data.pop('request_template_id', None)
            resp_data['request_template'] = new_req
            ResponseTemplate.objects.create(**{k: v for k, v in resp_data.items() if k not in ['id']})
    for req in integration_template.request_templates.all():
        new_req = old_to_new.get(req.id)
        if not new_req:
            continue
        for prereq in req.prerequisite_links.all():
            new_request_template = new_req
            new_prerequisite = old_to_new.get(prereq.prerequisite.id)
            if new_request_template and new_prerequisite:
                RequestTemplatePrerequisite.objects.create(
                    request_template=new_request_template,
                    prerequisite=new_prerequisite,
                    position=prereq.position,
                    input=prereq.input
                )
    if integration_template.url:
        delivery_company.delivery_company_url = integration_template.url
    if integration_template.connection_meta_fields:
        delivery_company.meta_data = integration_template.connection_meta_fields
    if integration_template.order_custom_fields:
        delivery_company.order_custom_fields = integration_template.order_custom_fields
    delivery_company.save()
    return old_to_new
