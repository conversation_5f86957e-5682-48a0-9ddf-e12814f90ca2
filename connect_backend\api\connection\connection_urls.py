from django.urls import path
from .connection_apis import *

urlpatterns = [
    path('connect_with_delivery_company', connect_with_delivery_company, name='connect_with_delivery_company'),
    path('update_connection_request', update_connection_request, name='update_connection_request'),
    path('send_to_delivery_company', send_to_delivery_company, name='send_to_delivery_company'),
    path('get_user_credentials', get_user_credentials, name='get_user_credentials'),
    path('get_connected_companies', get_connected_companies, name='get_connected_companies'),
    path('update_order_vhub', update_order_vhub, name='update_order_vhub'),
    path('cancel_connection', cancel_connection, name='cancel_connection'),
    path('uncancel_connection', uncancel_connection, name='uncancel_connection'),
    path('sync_connection', sync_connection, name='sync_connection'),
    path('add_connection', add_connection, name='add_connection'),
    path('edit_connection', edit_connection, name='edit_connection'),
    path('change_password', change_password, name='change_password'),
    path('add_dynamic_connection', add_dynamic_connection, name='add_dynamic_connection'),
    path('update_dynamic_connection', update_dynamic_connection, name='update_dynamic_connection'),
    path('update_custom_fields_defaults', update_custom_fields_defaults, name='update_custom_fields_defaults'),
    path('get_options_list', get_options_list, name='get_options_list'),
]