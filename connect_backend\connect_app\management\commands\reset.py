from django.core.management.base import BaseCommand
from api.users.user_model import * 
from api.lookups.lookup_model import * 
from api.orders.order_model import * 
from api.auth.auth_model import * 
from api.pricelists.pricelist_model import * 
from api.delivery_company.delivery_company_model import *
from api.connection.connection_model import * 
from api.integrations.integration_model import * 
from api.logs.logs_model import *
from django.contrib.auth.models import User as DjUser
from django.db import transaction

class Command(BaseCommand):
    help = 'Deletes all records related to the user with the specified mobile number.'

    def add_arguments(self, parser):
        parser.add_argument('mobile_number', type=str, help='Mobile number of the user to reset')

    def handle(self, *args, **options):
        mobile_number = options['mobile_number']

        OtpCodes.objects.filter(mobile_number=mobile_number).delete()
        OtpToken.objects.filter(mobile_number=mobile_number).delete()
        try:
            user = User.objects.get(mobile_number=mobile_number)
        except User.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'No user found with mobile number {mobile_number}'))
            return
        with transaction.atomic():
            
            EcommerceToken.objects.filter(company=user.company).delete()
            user.user.delete()
            Order.objects.filter(company=user.company).delete()
            ConnectDeliveryCompany.objects.filter(company=user.company).delete()
            ConnectionUser.objects.filter(company=user.company).delete()
            PricelistItem.objects.filter(pricelist__company=user.company).delete()
            Pricelist.objects.filter(company=user.company).delete()
            CompanyConf.objects.filter(company=user.company).delete()
            TrackedFields.objects.filter(company=user.company).delete()
            logs = Logs.objects.filter(company=user.company)
            for log in logs:
                LogsInfo.objects.filter(log=log).delete()
                log.delete()
            user.company.delete()
            user.delete()

        self.stdout.write(self.style.SUCCESS(f'Successfully deleted all records related to the user with mobile number {mobile_number}'))
