<!DOCTYPE html>
{% if meta.lang == 'ar' %}
    <html lang='ar' dir="rtl"></html>
{% else %}
    <html lang='en' dir="ltr"></html>
{% endif %}

<head>
    <meta charset="UTF-8">
    <title>PDF Report</title>
    <link rel="stylesheet" href="templates/shared/shared-styles.css">
    <link rel="stylesheet" href="templates/orders/order_10_10.css">
</head>

{% if meta.lang == 'ar' %}
    <body class="rtl">
{% else %}
    <body class="ltr">
{% endif %}

    {% for doc in docs %}
    {% set products = doc.order_lines %}
    {% set product_count = products|length %}
    {% set chunks = (product_count / 4)|round(0, 'ceil')|int or 1 %}

    {% for i in range(chunks) %}
    <div class="waybill-container page-break">
        <div class="header">
            <div class="business-logo">
                <img src="{{doc.company_logo}}" class="business-logo">
                {% if doc.company_registry %}
                <div class="text-center fw-bold">{{doc.company_registry}}</div>
                {% endif %}
            </div>
            <div class="date-time fw-bold">
                {{meta.formatted_now}}
            </div>
        </div>
        <div class="">
            <div class="left-cards-container">
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.RECIPIENT}}
                    </div>
                    <div>
                        <div class="info-block border-b-grey">
                            <span class="text-truncate">
                                {{doc.order.customer_name}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <span class="">
                                {{doc.order.customer_mobile}}
                            </span>
                        </div>
                        <div class="info-block border-b-grey">
                            <div class="mb-2 address-content text-small address-container">
                                {{doc.order.area.name}},
                                {{doc.order.sub_area.name}},
                                {{doc.order.address}}
                            </div>
                        </div>
                        <div class="info-block">
                            <span class="text-truncate">
                                {{meta.labels.CREATOR}}: {{doc.order.created_by}}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card overflow-hidden">
                    <div class="notes-content overflow-hidden text-small">
                        <span>{{doc.order.note[:100] if doc.order.note is not none else ""}}</span>
                    </div>
                </div>
            </div>
            <div class="right-cards-container">
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.PRODUCTS}}
                        {% if chunks > 1 %}
                        <span class="text-small">(Page {{i+1}} of {{chunks}})</span>
                        {% endif %}
                    </div>

                    <div class="products-container">
                        {% if products and products|length > 0 %}
                        <table class="o-table">
                            {% for item in products[i*4 : (i+1)*4] %}
                            <tr>
                                <td class="cell-truncate border-b-grey">
                                    {{item.product.reference_sequence if item.print_product_sequence else item.product_variant.variant.name}}
                                </td>
                                <td class="border-b-grey">
                                    X{{item.quantity}}
                                </td>
                                <td class="border-b-grey">
                                    {{item.price}}
                                </td>
                            </tr>
                            {% endfor %}
                        </table>
                        {# Show total only on the last page #}
                        {% if i == chunks - 1 %}
                        <div class="info-block total-row">
                            <span>
                                {{meta.labels.TOTAL}}:
                            </span>
                            <span>
                                {{doc.total}}
                            </span>
                        </div>
                        {% endif %}
                        {% else %}
                        {% if doc.order.product_info %}
                            {{doc.order.product_info.replace('\n', '<br>')}}
                        {% endif %}
                        {% endif %}
                    </div>
                </div>
                {% if i == chunks - 1 %}
                <div class="card">
                    <div class="card-header">
                        {{meta.labels.COD}}
                    </div>
                    <div class="fs-lg fw-bold">
                        {{doc.order.total_cod}}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        {% if doc.order.connection and doc.order.connection.delivery_company and i == chunks - 1 %}
        <div class="mt-1">
            {{meta.labels.DELIVERY_COMPANY_NAME}}:
            {{doc.order.connection.delivery_company.name}}
        </div>
        {% endif %}
        <div class="">
            <div class="header-barcode-container">
                <div class="text-center">{{meta.labels.STORE_SEQUENCE}}</div>
                <img class="barcode-image m-auto" src="data:image/png;base64,{{ doc.barcode_sequence_base64 }}"
                        alt="">
            </div>
            {% if doc.order.olivery_sequence %}
            <div class="header-barcode-container d-flex flex-column text-center">
                <div class="text-center">{{meta.labels.DELIVERY_SEQUENCE}}</div>
                <img class="barcode-image m-auto" src="data:image/png;base64,{{ doc.barcode_olivery_base64 }}"
                        alt="">
            </div>
            {% endif %}
        </div>
    </div>
    {% endfor %}
    {% endfor %}
</body>

</html>