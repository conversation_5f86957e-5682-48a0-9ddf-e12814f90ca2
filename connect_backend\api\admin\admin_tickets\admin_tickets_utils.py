from django.conf import settings
from .admin_ticket_model import Ticket, ReleaseDocument
import requests
import jwt
from ...integrations.integration_utils import decode_token
from ...release_document.utils_versioning import extract_version_parts


release_note_ai_domain = settings.RELEASE_NOTE_AI_DOMAIN
release_note_ai_token = settings.RELEASE_NOTE_AI_TOKEN

def create_ticket(values):
    ticket = Ticket.objects.create(**values)
    return ticket

def update_ticket_util(values):
    ticket = Ticket.objects.get(id=values['id'])
    for key, value in values.items():
        setattr(ticket, key, value)
    ticket.save()
    return ticket


def update_release_document_util(values):
    release_document = ReleaseDocument.objects.get(id=values['id'])
    for key, value in values.items():
        setattr(release_document, key, value)
    release_document.save()
    return release_document

def resolve_latest_version():
    versions = Ticket.objects.values_list('fix_version', flat=True)
    versions = [v for v in versions if v]

    return max(versions, key=extract_version_parts, default=None)

def preprocess_filters(filters):
    for f in filters:
        if f.get("filters"):
            preprocess_filters(f["filters"])
        elif f["field"] == "fix_version" and f["value"] == "latest":
            f["value"] = resolve_latest_version()
    return filters

def generate_release_document_util(text_search):
    payload = {
        "query": text_search,
        "environment": settings.BASEURL
    }
    headers = {
        "accept": "application/json",
        "content-type": "application/json",
        "authorization": "Bearer " + release_note_ai_token
    }

    response = requests.post(release_note_ai_domain + "/generate", json=payload, headers=headers)

    # Check if the request was successful
    if response.status_code == 200:
        return response.json()  # This returns the actual response data as a dict
    else:
        response.raise_for_status()

def generate_service_token(service_name):
    payload = {
        'service': service_name,
    }
    secret_key = settings.SECRET_KEY
    return jwt.encode(payload, secret_key, algorithm='HS256')

def validate_service_token(request):
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Token '):
        return decode_token(auth_header[6:])
    return None