from django.utils.translation import gettext as _
from django.views.decorators.csrf import csrf_exempt
from api.serializers import WarehouseSerializer, ResellerCompanySerializer
from api.util_functions import get_records, parse_request_body
from rest_framework.decorators import *
from rest_framework.permissions import AllowAny
from ...auth.auth_utils import *
from django.db.models import Q
from ...warehouse.warehouse_utils import *
from rest_framework.parsers import MultiPartParser, FormParser

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def add_reseller_warehouse(request):
    try:

        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        reseller = request.user

        values = json_data
        values['reseller'] = reseller

        validation_errors, foreign_key_objects = validate_warehouse(json_data)

        if validation_errors:
            raise get_error_response('GENERAL_006', {'validation_errors': validation_errors})

        values = {**values, **foreign_key_objects}
        warehouse = create_warehouse_util(values)
        serializer = WarehouseSerializer(warehouse)
        return JsonResponse({'success': True, 'message': 'Warehouse Created Successfully', 'warehouse': serializer.data}, status=201)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def update_reseller_warehouse(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        reseller = request.user
        json_data['reseller'] = reseller

        validation_errors, foreign_key_objects = validate_warehouse(json_data)

        try:
            warehouse = Warehouse.objects.get(id=json_data['id'], reseller=reseller)
        except Warehouse.DoesNotExist:
            raise get_error_response('GENERAL_404', {'message': 'Warehouse not found'})

        if validation_errors:
            raise get_error_response('GENERAL_006', {'validation_errors': validation_errors})

        values = {**json_data, **foreign_key_objects}
        warehouse = update_warehouse_util(values, warehouse)
        serializer = WarehouseSerializer(warehouse)
        
        return JsonResponse({
            'success': True, 
            'message': 'Warehouse Updated Successfully', 
            'warehouse': serializer.data
        }, status=200)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_warehouses(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        reseller = request.user

        response_data = get_records('Warehouse', json_data, False, company_filter=Q(reseller=reseller))
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_warehouse_connections(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        reseller = request.user

        response_data = get_records('CompanyWarehouseConnection', json_data, False, company_filter=Q(warehouse__reseller=reseller))
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def accept_warehouse_connection(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        reseller = request.user
        warehouse_connection = CompanyWarehouseConnection.objects.get(id=json_data['id'], warehouse__reseller=reseller)
        warehouse_connection.connection_status = 'connected'
        warehouse_connection.save()

        serializer = CompanyWarehouseConnectionSerializer(warehouse_connection)
        return JsonResponse({'success': True, 'warehouse_connection': serializer.data}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def reject_warehouse_connection(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        
        reseller = request.user
        warehouse_connection = CompanyWarehouseConnection.objects.get(id=json_data['id'], warehouse__reseller=reseller)
        warehouse_connection.connection_status = 'rejected'
        warehouse_connection.save()
        return JsonResponse({'success': True, warehouse_connection: warehouse_connection}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def set_business_available_warehouses(request):
    try:
        reseller = request.user
        json_data = parse_request_body(request)
        if not json_data:
            raise get_error_response('GENERAL_001', {})

        company_id = json_data.get('company_id')
        warehouses = json_data.get('warehouses', [])
        company = Company.objects.get(company_id=company_id)

        CompanyResellerWarehouse.objects.filter(company=company).delete()
        for warehouse in warehouses:
            CompanyResellerWarehouse.objects.get_or_create(company=company, warehouse=Warehouse.objects.get(id=warehouse))

        return JsonResponse({'success': True, 'message': _('Company Updated Successfully')}, status=200)
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_all_resellers_warehouses(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        

        response_data = get_records('Warehouse', json_data, False, company_filter=Q(reseller__isnull=False))
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_stock_approval(request):
    json_data = parse_request_body(request)
    if json_data is None:
        raise get_error_response('GENERAL_001', {})
    
    reseller = request.user

    q_filter = Q(
        Q(warehouse__reseller__id=reseller.id) &
        Q(status='pending_warehouse_approval')
    )
    response_data = get_records('StockApproval', json_data, False, company_filter=q_filter)
    return JsonResponse(response_data, status=200)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def update_reseller_stock_approval(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    reseller = request.user
    values = json_data
    update_approval_record_reseller(values, reseller)
    return JsonResponse({'success': True, 'approval_id': values.get('stock_approval_id')}, status=200)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_warehouse_variants(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        reseller = request.user

        response_data = get_records(
            'WarehouseVariant',
            json_data,
            False,
            company_filter=Q(warehouse__reseller=reseller),
            context={
                'additional_fields': ['company'],
                'cache_prefix': 'reseller_warehouse_variant_'
                }
            )
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_company_assigned_warehouses(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        response_data = get_records('CompanyResellerWarehouse', json_data, False)
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def add_reseller_stocktaking(request):
    json_data = parse_request_body(request)
    if not json_data:
        raise get_error_response('GENERAL_001', {})
    
    reseller = request.user

    values = json_data
    values['reseller'] = reseller
    values['user'] = reseller.name
    product_items = values.pop('stocktaking_products', [])
    product_instances = []
    for item in product_items:
        warehouse_variant_id = item.pop('warehouse_variant')
        try:
            warehouse_variant = WarehouseVariant.objects.get(id=warehouse_variant_id)
        except WarehouseVariant.DoesNotExist:
            raise ValueError(f"WarehouseVariant with id {warehouse_variant_id} does not exist")
        product = StocktakingProducts.objects.create(warehouse_variant=warehouse_variant,**item)
        product_instances.append(product)
    stocktaking = Stocktaking.objects.create(**values)
    stocktaking.stocktaking_Products.set(product_instances)
    serializer = StocktakingSerializer(stocktaking)
    return JsonResponse({'success': True, 'stocktaking': serializer.data}, status=201)

@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_stocktaking(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        reseller = request.user

        response_data = get_records('Stocktaking', json_data, False, company_filter=Q(reseller=reseller))
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_locations(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        reseller = request.user

        response_data = get_records('Location', json_data, False, company_filter=Q(warehouse__reseller=reseller))
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def create_reseller_location(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        reseller = request.user
        values = json_data
        location = create_location_util(values)
        serializer = LocationSerializer(location)
        return JsonResponse({'success': True, 'message': 'Location Created Successfully', 'location': serializer.data}, status=201)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    

@api_view(['GET'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def print_reseller_location(request):
    try:
        reseller = request.user
        ids = request.GET.getlist('ids')

        locations = Location.objects.filter(id__in=ids)
        render_data = []
        for location in locations:
            location_barcode_olivery_base64 = generate_barcode_base64(location.name) if location.name else None       
            render_data.append({'name':location.name,'location_barcode_olivery_base64':location_barcode_olivery_base64})
        language = translation.get_language() or 'ar'
        language = language.lower()

        meta = {
            'lang':language,
        }
        response = generate_pdf('locations', 'location_barcode', render_data, meta)
        return response
    except Exception as e:
        raise e
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def add_reseller_location_for_products(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})
        location_id = json_data['location_id']
        products_info = json_data['products_info']
        location = Location.objects.get(id=location_id)
        for product_info in products_info:
            product_id = product_info['id']
            quantity = product_info['quantity']
            product = WarehouseVariant.objects.get(id=product_id)
            LocationProduct.objects.update_or_create(location=location, product=product, defaults={'quantity': quantity})
        serializer = LocationSerializer(location)
        return JsonResponse({'success': True, 'message': 'Location Updated Successfully', 'location': serializer.data}, status=201)
    except Exception as e:
        raise e
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_locations_with_products(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        reseller = request.user
        response_data = get_records('Location', json_data, False,company_filter=Q(warehouse__reseller=reseller),context={'use_location_with_product_serializer': True})
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
@parser_classes([MultiPartParser, FormParser])
def reseller_upload_location_excel(request):
    reseller = request.user
    warehouse_id = request.data.get('warehouse_id',False)
    if not warehouse_id:
        raise get_error_response('GENERAL_003', {'fields': ['warehouse_id']})
        
    file = request.FILES['file']
    if file.content_type not in [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
        'application/vnd.ms-excel',
        'text/csv'
    ]:
        raise get_error_response('GENERAL_004', {})
    
    locations_to_create = proceed_location_excel(file, reseller,warehouse_id)
    if not locations_to_create:
        raise get_error_response('GENERAL_005', {'message': _('No valid locations found in the file')})
    
    return JsonResponse({'success': True, 'message': _('All Locations Added successfully')}, status=201)


@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
@parser_classes([MultiPartParser, FormParser])
def reseller_upload_location_excel_with_products(request):
    reseller = request.user
    warehouse_id = request.data.get('warehouse_id',False)
    if not warehouse_id:
        raise get_error_response('GENERAL_003', {'fields': ['warehouse_id']})
        
    file = request.FILES['file']
    if file.content_type not in [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
        'application/vnd.ms-excel',
        'text/csv'
    ]:
        raise get_error_response('GENERAL_004', {})
    
    locations_to_updates = proceed_location_with_products_excel(file, reseller,warehouse_id)
    if not locations_to_updates:
        raise get_error_response('GENERAL_005', {'message': _('No valid locations found in the file')})
    
    return JsonResponse({'success': True, 'message': _('All Locations Added successfully')}, status=201)


@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def change_products_location(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        reseller = request.user
        values = json_data
        new_location_products = change_location_util(values)
        serializer = LocationProductSerializer(new_location_products, many=True)
        return JsonResponse({'success': True, 'message': 'Location Created Successfully', 'location': serializer.data}, status=201)
    
    except ConnectError as e:
        response = e.to_dict()
        return JsonResponse(response, status=401 if response['error']['code'] == 'AUTH_400' else 400)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
@api_view(['POST'])
@authentication_classes([ResellerJWTAuthentication])
@permission_classes([AllowAny])
@csrf_exempt
def get_reseller_location_products(request):
    try:
        json_data = parse_request_body(request)
        if json_data is None:
            raise get_error_response('GENERAL_001', {})

        reseller = request.user
        response_data = get_records('LocationProduct', json_data, False, company_filter=Q(location__warehouse__reseller=reseller))
        return JsonResponse(response_data, status=200)
    except ConnectError as e:
        response = e.to_dict()
        if response['error']['code'] == 'AUTH_400':
            status=401
        else:
            status=400
        return JsonResponse(response, status=status)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)