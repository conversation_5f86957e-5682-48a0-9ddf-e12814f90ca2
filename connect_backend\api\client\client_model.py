from django.db import models
from ..models import SuperModel
from ..lookups.lookup_model import *

class Client(SuperModel):
    name = models.CharField(max_length=200)
    mobile_number = models.CharField(max_length=15)
    country_code = models.CharField(max_length=6)
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True)
    area = models.ForeignKey(Area, on_delete=models.SET_NULL, null=True)
    sub_area = models.ForeignKey(SubArea, on_delete=models.SET_NULL, null=True)
    address = models.CharField(max_length=200)

    def save(self, *args, **kwargs):
        if self.country_code and '|' in self.country_code:
            self.country_code = self.country_code.split('|')[0]
        super(Client, self).save(*args, **kwargs)