from django.core.management.base import BaseCommand
from api.users.user_model import CompanyConf

class Command(BaseCommand):
    help = "Update business settings to have delivery section in form fields"

    def handle(self, *args, **kwargs):
        updated_count = 0

        for conf in CompanyConf.objects.all():
            if conf.order_form_fields:
                conf.order_form_fields = f"{conf.order_form_fields},logistics_information"
            else:
                conf.order_form_fields = "logistics_information"
            
            conf.save()
            updated_count += 1

        self.stdout.write(self.style.SUCCESS(f"Successfully updated {updated_count} records."))
